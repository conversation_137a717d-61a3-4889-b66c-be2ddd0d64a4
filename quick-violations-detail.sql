-- <PERSON><PERSON>t ngắn gọn để xem chi tiết vi phạm
-- Thay đổi userId và date range bên dưới

-- ===== THAY ĐỔI GIÁ TRỊ =====
WITH test_params AS (
    SELECT
        '03c5c8cc-3750-4845-ba67-67a95743993c' as user_id,  -- Thay đổi userId ở đây
        '2024-12-01'::date as start_date,                    -- Thay đổi ngày bắt đầu
        '2024-12-31'::date as end_date                       -- Thay đổi ngày kết thúc
)

-- 1. TẤT CẢ VI PHẠM THEO NGÀY
SELECT 
    attr."dateCreated"::date as ngay,
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'CN'
        WHEN 1 THEN 'T2 ✅'
        WHEN 2 THEN 'T3'
        WHEN 3 THEN 'T4'
        WHEN 4 THEN 'T5'
        WHEN 5 THEN 'T6'
        WHEN 6 THEN 'T7'
    END as thu,
    CASE attr."type"
        WHEN 16 THEN 'Đồng phục'
        WHEN 18 THEN 'Không có thẻ'
        WHEN 17 THEN 'Có thẻ'
        ELSE 'Khác'
    END as loai_vi_pham,
    COUNT(*) as so_lan,
    COUNT(DISTINCT attr."trackId") as so_track,
    MIN(attr."dateCreated"::time) as gio_dau,
    MAX(attr."dateCreated"::time) as gio_cuoi,
    -- Logic đếm
    CASE 
        WHEN attr."type" = 16 AND EXTRACT(DOW FROM attr."dateCreated") = 1 
        THEN 'TÍNH (Đồng phục thứ 2)'
        WHEN attr."type" = 16 AND EXTRACT(DOW FROM attr."dateCreated") != 1 
        THEN 'KHÔNG TÍNH (Đồng phục không phải thứ 2)'
        WHEN attr."type" = 18 
        THEN 'TÍNH (Thẻ tất cả ngày)'
        ELSE 'KHÔNG TÍNH'
    END as tinh_khong
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND result."userIdReCheckResult" = :userId
    AND attr."type" IN (16, 17, 18)  -- uniform, wearing_card, no_card
GROUP BY attr."dateCreated"::date, EXTRACT(DOW FROM attr."dateCreated"), attr."type"
ORDER BY attr."dateCreated"::date, attr."type";

-- 2. CHI TIẾT VI PHẠM THẺ VỚI LOGIC WEARING_CARD
SELECT '=== CHI TIẾT VI PHẠM THẺ ===' as title;

SELECT 
    attr."trackId",
    attr."dateCreated",
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'CN' WHEN 1 THEN 'T2' WHEN 2 THEN 'T3'
        WHEN 3 THEN 'T4' WHEN 4 THEN 'T5' WHEN 5 THEN 'T6' WHEN 6 THEN 'T7'
    END as thu,
    CASE attr."type"
        WHEN 17 THEN 'Có thẻ'
        WHEN 18 THEN 'Không có thẻ'
    END as trang_thai_the,
    CASE 
        WHEN attr."type" = 18 AND EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId" AND attr2."type" = 17
        ) THEN 'KHÔNG TÍNH (có cả 17 và 18)'
        WHEN attr."type" = 18 THEN 'TÍNH (chỉ có 18)'
        WHEN attr."type" = 17 THEN 'KHÔNG VI PHẠM'
        ELSE 'KHÁC'
    END as ket_qua
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND result."userIdReCheckResult" = :userId
    AND attr."type" IN (17, 18)
ORDER BY attr."trackId", attr."type";

-- 3. TỔNG KẾT CUỐI CÙNG
SELECT '=== TỔNG KẾT ===' as title;

SELECT 
    'Đồng phục (chỉ thứ 2)' as loai,
    COUNT(DISTINCT attr."dateCreated"::date) as so_luong,
    'ngày' as don_vi
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND result."userIdReCheckResult" = :userId
    AND attr."type" = 16
    AND EXTRACT(DOW FROM attr."dateCreated") = 1  -- Chỉ thứ 2

UNION ALL

SELECT 
    'Thẻ (tất cả ngày)' as loai,
    COUNT(DISTINCT attr."trackId") as so_luong,
    'lần' as don_vi
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND result."userIdReCheckResult" = :userId
    AND attr."type" = 18
    AND NOT EXISTS (
        SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
        WHERE attr2."trackId" = attr."trackId" AND attr2."type" = 17
    );

-- 4. KIỂM TRA SHIFT VÀ BREAK TIME
SELECT '=== KIỂM TRA SHIFT VÀ BREAK TIME ===' as title;

SELECT 
    attr."dateCreated"::date as ngay,
    attr."dateCreated"::time as gio,
    shift_type."startHour" as ca_bat_dau,
    shift_type."endHour" as ca_ket_thuc,
    shift_type."startBreak" as nghi_bat_dau,
    shift_type."endBreak" as nghi_ket_thuc,
    CASE attr."type"
        WHEN 16 THEN 'Đồng phục'
        WHEN 18 THEN 'Thẻ'
    END as loai,
    CASE 
        WHEN attr."dateCreated"::time < shift_type."startHour"::time 
             OR attr."dateCreated"::time > shift_type."endHour"::time
        THEN 'Ngoài ca ❌'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
             AND shift_type."startBreak" != '00:00'
        THEN 'Giờ nghỉ ❌'
        ELSE 'Giờ làm ✅'
    END as trang_thai_gio
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = :userId
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated"
LIMIT 20;
