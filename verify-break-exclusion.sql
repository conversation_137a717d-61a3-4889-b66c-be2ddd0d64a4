-- <PERSON><PERSON><PERSON> để verify break time exclusion hoạt động đúng
-- Thay userId và date range

WITH test_params AS (
    SELECT 
        '03c5c8cc-3750-4845-ba67-67a95743993c' as user_id,
        '2024-12-01'::date as start_date,
        '2024-12-31'::date as end_date
)

-- KIỂM TRA VI PHẠM TRONG/NGOÀI BREAK TIME
SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::time as violation_time,
    shift_type."startBreak" as break_start_text,
    shift_type."endBreak" as break_end_text,
    shift_type."startBreak"::time as break_start_time,
    shift_type."endBreak"::time as break_end_time,
    
    -- <PERSON><PERSON><PERSON> tra từng điều kiện
    CASE 
        WHEN shift_type."startBreak" IS NULL OR shift_type."endBreak" IS NULL
        THEN 'NULL break time'
        WHEN shift_type."startBreak"::time <= '00:00:00'::time
        THEN 'No break (startBreak = 00:00)'
        WHEN shift_type."endBreak"::time <= '00:00:00'::time  
        THEN 'No break (endBreak = 00:00)'
        WHEN shift_type."startBreak" = shift_type."endBreak"
        THEN 'No break (start = end)'
        WHEN attr."dateCreated"::time < shift_type."startBreak"::time
        THEN 'TRƯỚC break time ✅ TÍNH'
        WHEN attr."dateCreated"::time > shift_type."endBreak"::time
        THEN 'SAU break time ✅ TÍNH'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
        THEN 'TRONG break time ❌ LOẠI TRỪ'
        ELSE 'UNKNOWN'
    END as break_analysis,
    
    -- Logic cuối cùng trong code
    CASE 
        WHEN NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                 AND attr."dateCreated"::time <= shift_type."endBreak"::time
                 AND shift_type."startBreak" IS NOT NULL
                 AND shift_type."endBreak" IS NOT NULL
                 AND shift_type."startBreak"::time > '00:00:00'::time
                 AND shift_type."endBreak"::time > '00:00:00'::time
                 AND shift_type."startBreak" != shift_type."endBreak")
        THEN '✅ ĐƯỢC TÍNH'
        ELSE '❌ BỊ LOẠI TRỪ'
    END as final_result,
    
    CASE attr."type"
        WHEN 16 THEN 'Đồng phục'
        WHEN 18 THEN 'Thẻ'
        ELSE 'Khác'
    END as violation_type
    
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
CROSS JOIN test_params tp
WHERE attr."dateCreated" >= tp.start_date
    AND attr."dateCreated" <= tp.end_date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = tp.user_id
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated"
LIMIT 20;

-- THỐNG KÊ TỔNG QUAN
SELECT '=== THỐNG KÊ BREAK TIME EXCLUSION ===' as title;

WITH test_params AS (
    SELECT
        '03c5c8cc-3750-4845-ba67-67a95743993c' as user_id,
        '2024-12-01'::date as start_date,
        '2024-12-31'::date as end_date
),
break_analysis AS (
    SELECT
        attr."trackId",
        attr."dateCreated"::time as violation_time,
        shift_type."startBreak"::time as break_start,
        shift_type."endBreak"::time as break_end,
        CASE
            WHEN shift_type."startBreak" IS NULL OR shift_type."endBreak" IS NULL
                 OR shift_type."startBreak"::time <= '00:00:00'::time
                 OR shift_type."endBreak"::time <= '00:00:00'::time
                 OR shift_type."startBreak" = shift_type."endBreak"
            THEN 'no_break_time'
            WHEN attr."dateCreated"::time >= shift_type."startBreak"::time
                 AND attr."dateCreated"::time <= shift_type."endBreak"::time
            THEN 'in_break_time'
            ELSE 'outside_break_time'
        END as break_status
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    CROSS JOIN test_params tp
    WHERE attr."dateCreated" >= tp.start_date
        AND attr."dateCreated" <= tp.end_date + interval '1 day'
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND result."userIdReCheckResult" = tp.user_id
        AND attr."type" IN (16, 18)
)
SELECT
    break_status,
    COUNT(*) as violation_count,
    CASE break_status
        WHEN 'no_break_time' THEN 'Được tính (không có break time)'
        WHEN 'in_break_time' THEN 'Bị loại trừ (trong break time)'
        WHEN 'outside_break_time' THEN 'Được tính (ngoài break time)'
    END as result_description
FROM break_analysis
GROUP BY break_status
ORDER BY break_status;
