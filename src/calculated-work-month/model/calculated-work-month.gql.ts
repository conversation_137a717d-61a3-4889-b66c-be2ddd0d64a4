import { Field, ObjectType, Int } from '@nestjs/graphql';

@ObjectType()
export class CalculatedWorkMonthGql {
    constructor(partial: Partial<CalculatedWorkMonthGql>) {
        Object.assign(this, partial);
    }

    @Field(type => String, { nullable: false })
    userId: string;

    @Field(type => String, { nullable: false })
    cycleDate: string;

    @Field(type => Number, { nullable: false })
    year: number;

    @Field(type => Number, { nullable: false })
    month: number;

    @Field(type => String, { nullable: false })
    days: string;

    @Field(type => Number, { nullable: false })
    congPhep: number;

    @Field(type => Number, { nullable: false })
    phatTien: number;

    @Field(type => Number, { nullable: false })
    tongCong: number;

    @Field(type => Number, { nullable: false })
    congThucTe: number;

    @Field(type => Number, { nullable: false })
    congCheDo: number;

    @Field(type => Number, { nullable: true })
    count: number;

    @Field(type => Number, { nullable: false })
    uniformViolationCount: number;

    @Field(type => Number, { nullable: false })
    cardViolationCount: number;

    @Field(type => Number, { nullable: false })
    totalViolationCount: number;
}