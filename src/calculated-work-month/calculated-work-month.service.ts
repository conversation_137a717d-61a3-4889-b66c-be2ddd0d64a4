import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { dateUtil } from 'src/utils/date.util';
import { StringUtils } from 'src/utils/string';
import { ErrorEnum } from 'src/data/enums/error.enum';
import { CalculatedWorkMonthGql } from './model/calculated-work-month.gql';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';
import { ViolationCountService } from 'src/violation-count/violation-count.service';
import * as moment from 'moment';
import * as xl from 'excel4node';

@Injectable()
export class CalculatedWorkMonthService {

    constructor(
        private prisma: PrismaService,
        private cachedGlobalConfig: CachedGlobalConfigService,
        private violationCountService: ViolationCountService,
    ) { }

    async getCalculatedWorkMonth(
        userId: string,
        month: string
    ): Promise<CalculatedWorkMonthGql> {
        let searchDate = new Date(month);
        if (!dateUtil.isValidDate(searchDate)) throw new BadRequestException(StringUtils.generateExceptionString(ErrorEnum['Invalid date']));

        let calculatedWork = await this.prisma.calculatedWorkMonth.findFirst({
            where: {
                userId: userId,
                year: searchDate.getFullYear(),
                month: searchDate.getMonth() + 1,
            }
        });

        // Lấy thông tin vi phạm
        const violationCounts = await this.violationCountService.countViolationsByUserAndMonth(
            userId,
            searchDate.getFullYear(),
            searchDate.getMonth() + 1
        );

        if (calculatedWork)
            return new CalculatedWorkMonthGql({
                ...calculatedWork,
                uniformViolationCount: violationCounts.uniformViolationCount,
                cardViolationCount: violationCounts.cardViolationCount,
                totalViolationCount: violationCounts.totalViolationCount,
            });
        else return new CalculatedWorkMonthGql({
            userId: userId,
            year: searchDate.getFullYear(),
            month: searchDate.getMonth() + 1,
            tongCong: 0,
            congThucTe: 0,
            congPhep: 0,
            congCheDo: 0,
            phatTien: 0,
            days: '',
            cycleDate: searchDate.toISOString(),
            uniformViolationCount: violationCounts.uniformViolationCount,
            cardViolationCount: violationCounts.cardViolationCount,
            totalViolationCount: violationCounts.totalViolationCount,
        });
    }

    async getCalculatedWorkRange(
        userId: string,
        startDay: string,
        endDay: string,
    ): Promise<CalculatedWorkMonthGql> {
        let startDate = new Date(moment(startDay).tz(this.cachedGlobalConfig.timeZoneName).format(`YYYY-MM-DDTHH:mm:ss${this.cachedGlobalConfig.defaultTimezoneOffset}`))
        let endDate = new Date(moment(endDay).tz(this.cachedGlobalConfig.timeZoneName).format(`YYYY-MM-DDTHH:mm:ss${this.cachedGlobalConfig.defaultTimezoneOffset}`))

        if (!dateUtil.isValidDate(startDate) || !dateUtil.isValidDate(endDate)) throw new BadRequestException(StringUtils.generateExceptionString(ErrorEnum['Invalid date']));

        let calculatedWork = await this.prisma.calculatedWorkDay.findMany({
            where: {
                userId: userId,
                day: {
                    gte: moment(startDay).tz(this.cachedGlobalConfig.timeZoneName).format(`YYYY-MM-DD`),
                    lte: moment(endDay).tz(this.cachedGlobalConfig.timeZoneName).format(`YYYY-MM-DD`),
                }
            }, orderBy: { day: 'asc' }
        });
        let gql = new CalculatedWorkMonthGql({
            userId: userId,
            year: startDate.getFullYear(),
            month: startDate.getMonth() + 1,
            tongCong: calculatedWork.reduce((acc, x) => acc + x.tongCong, 0),
            congThucTe: calculatedWork.reduce((acc, x) => acc + x.congThucTe, 0),
            congPhep: calculatedWork.reduce((acc, x) => acc + x.congPhep, 0),
            congCheDo: calculatedWork.reduce((acc, x) => acc + x.congCheDo, 0),
            phatTien: calculatedWork.reduce((acc, x) => acc + x.phatTien, 0),
            days: calculatedWork.map((x) => x.displayString).join(','),
        });

        return gql;
    }

    async exportWorkRange(
        startDay: string,
        endDay: string,
        month: number,
        year: number,
        userIds: string[],
        companyIds: string[],
    ) {
        if (companyIds.length > 0) {
            let userInCom = await this.prisma.user.findMany({
                where: {
                    companyId: { in: companyIds }
                }, select: { id: true }
            });

            for (let index = 0; index < userInCom.length; index++) {
                const userInComId = userInCom[index].id;
                userIds.push(userInComId);
            }
        }

        var wb = new xl.Workbook();
        var ws = wb.addWorksheet("Report")

        var style = wb.createStyle({
            font: { size: 12 },
            alignment: {
                horizontal: 'center',
                vertical: 'center',
                wrapText: true
            },
            border: {
                left: {
                    style: `thin`
                },
                right: {
                    style: `thin`,
                },
                top: {
                    style: `thin`,
                },
                bottom: {
                    style: `thin`,
                },
            }
        });

        let headerStyle = {
            ...style,
            font: { size: 12, bold: true },
            fill: {
                type: 'pattern',
                patternType: 'solid',
                fgColor: '9de887',
            }
        };

        ws.cell(1, 1, 1, 5, true).string('Bảng tổng hợp công').style(headerStyle);
        ws.cell(2, 1, 2, 5, true).string(`Kỳ công: ${new Date(startDay).toLocaleDateString('en-GB')} - ${new Date(endDay).toLocaleDateString('en-GB')}`).style(headerStyle);
        ws.cell(4, 1, 5, 1, true).string('STT').style(headerStyle);
        ws.cell(4, 2, 5, 2, true).string('Tên nhân viên').style(headerStyle);

        let startSearchDate = new Date(startDay);
        let endSearchDate = new Date(endDay);
        endSearchDate.setDate(endSearchDate.getDate() + 1);
        let range = dateUtil.getDatesBetweenTimes(startSearchDate, endSearchDate);

        for (let dayIndex = 0; dayIndex < range.length; dayIndex++) {
            let day = range[dayIndex].getDate();
            let weekday = range[dayIndex].getDay();

            switch (weekday) {
                case 0:
                    ws.cell(4, dayIndex + 3).string(`CN`).style(headerStyle)
                    break
                case 1:
                    ws.cell(4, dayIndex + 3).string(`T.2`).style(headerStyle)
                    break
                case 2:
                    ws.cell(4, dayIndex + 3).string(`T.3`).style(headerStyle)
                    break
                case 3:
                    ws.cell(4, dayIndex + 3).string(`T.4`).style(headerStyle)
                    break
                case 4:
                    ws.cell(4, dayIndex + 3).string(`T.5`).style(headerStyle)
                    break
                case 5:
                    ws.cell(4, dayIndex + 3).string(`T.6`).style(headerStyle)
                    break
                case 6:
                    ws.cell(4, dayIndex + 3).string(`T.7`).style(headerStyle)
                    break

            }

            ws.cell(5, dayIndex + 3).string(day.toString()).style(headerStyle);
        }

        let nextCol = range.length + 3;
        ws.cell(4, nextCol).string("Ngày công chuẩn").style(headerStyle);
        ws.cell(4, nextCol + 1).string("Tổng công thực tế").style(headerStyle);
        ws.cell(4, nextCol + 2).string("Công thử việc").style(headerStyle);
        ws.cell(4, nextCol + 3).string("Công chính thức").style(headerStyle);
        ws.cell(4, nextCol + 4).string("Nghỉ việc riêng hưởng lương").style(headerStyle);
        ws.cell(4, nextCol + 5).string("Tổng công nghỉ phép").style(headerStyle);
        ws.cell(4, nextCol + 6).string("Công onsite").style(headerStyle);
        ws.cell(4, nextCol + 7).string("Tổng công hưởng lương").style(headerStyle);
        ws.cell(4, nextCol + 8).string("Tổng công không hưởng lương").style(headerStyle);
        ws.cell(4, nextCol + 9).string("Vi phạm đồng phục").style(headerStyle);
        ws.cell(4, nextCol + 10).string("Vi phạm thẻ").style(headerStyle);
        ws.cell(4, nextCol + 11).string("Tổng số lần vi phạm").style(headerStyle);
        ws.cell(3, 1, 3, 5, true).string("Công chuẩn: ").style(headerStyle);

        let totalWorkDays = dateUtil.countWorkDayInMont(startSearchDate, endSearchDate);

        // Lấy violation count cho tất cả users trong khoảng thời gian
        const startMoment = moment(startDay);
        const endMoment = moment(endDay);
        const violationCountsMap = await this.violationCountService.countViolationsByUsersAndMonth(
            userIds,
            startMoment.year(),
            startMoment.month() + 1
        );
        for (let i = 0; i < userIds.length; i++) {
            const userId = userIds[i];
            let user = await this.prisma.user.findFirst({
                where: { id: userId },
                select: { name: true }
            });
            let userCalculatedWork = await this.getCalculatedWorkRange(userId, startDay, endDay);

            ws.cell(i + 6, 1).string(`${i + 1}`).style(headerStyle);
            ws.cell(i + 6, 2).string(user.name).style(style);

            let dayArr = userCalculatedWork.days.split(',');

            for (let dayIndex = 0; dayIndex < dayArr.length; dayIndex++) {
                let day = dayArr[dayIndex];

                switch (day) {
                    case "Full":
                    case "Full -":
                    case "Full-":
                    case "Full---":
                    case "Full ---":
                        ws.cell(i + 6, dayIndex + 3).string("0.5X/0.5X").style(style);
                        break;
                    case "-":
                        ws.cell(i + 6, dayIndex + 3).string("").style(style);
                        break;
                    case "Half":
                    case "Half -":
                    case "Half-":
                        ws.cell(i + 6, dayIndex + 3).string("0.5X/").style(style);
                        break;
                    case "Quarter":
                        ws.cell(i + 6, dayIndex + 3).string("0.25X/").style(style);
                        break;
                    case "Off":
                        ws.cell(i + 6, dayIndex + 3).string("").style(style);
                        break;
                    case "Full(P-P)":
                        ws.cell(i + 6, dayIndex + 3).string("0.5P/0.5P").style(style);
                        break;
                    case "Full(.-P)":
                        ws.cell(i + 6, dayIndex + 3).string("0.5X/0.5P").style(style);
                        break;
                    case "Full(P-.)":
                        ws.cell(i + 6, dayIndex + 3).string("0.5P/0.5X").style(style);
                        break;
                    case "Half(P-.)":
                        ws.cell(i + 6, dayIndex + 3).string("0.5P/").style(style);
                        break;
                    case "Half(.-P)":
                        ws.cell(i + 6, dayIndex + 3).string("/0.5P").style(style);
                        break;
                }
            }

            let congLuong = userCalculatedWork.congThucTe + userCalculatedWork.congPhep;
            let congKoLuong = userCalculatedWork.tongCong - congLuong;

            // Lấy violation count cho user hiện tại
            const userViolationCounts = violationCountsMap.get(userId) || {
                uniformViolationCount: 0,
                cardViolationCount: 0,
                totalViolationCount: 0
            };

            ws.cell(i + 6, dayArr.length + 3).number(totalWorkDays).style(style);
            ws.cell(i + 6, dayArr.length + 4).number(userCalculatedWork.congThucTe).style(style);
            ws.cell(i + 6, dayArr.length + 5).string("0").style(style);
            ws.cell(i + 6, dayArr.length + 6).number(userCalculatedWork.congThucTe).style(style);
            ws.cell(i + 6, dayArr.length + 7).string("0").style(style);
            ws.cell(i + 6, dayArr.length + 8).number(userCalculatedWork.congPhep).style(style);
            ws.cell(i + 6, dayArr.length + 9).string("0").style(style);
            ws.cell(i + 6, dayArr.length + 10).number(congLuong).style(style);
            ws.cell(i + 6, dayArr.length + 11).number(congKoLuong).style(style);
            ws.cell(i + 6, dayArr.length + 12).number(userViolationCounts.uniformViolationCount).style(style);
            ws.cell(i + 6, dayArr.length + 13).number(userViolationCounts.cardViolationCount).style(style);
            ws.cell(i + 6, dayArr.length + 14).number(userViolationCounts.totalViolationCount).style(style);

            ws.cell(3, 1).string(`Công chuẩn: ${totalWorkDays.toString()}`).style(headerStyle);
        }

        let buffer = await wb.writeToBuffer();
        return buffer;
    }
}
