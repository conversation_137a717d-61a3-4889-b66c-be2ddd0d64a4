export class UnknownBatchInsertModel {
    constructor(partial: Partial<UnknownBatchInsertModel>) {
        Object.assign(this, partial);
    }

    deviceId: string;
    srcDeviceId: string;
    image: string;
    time: Date;
    orphanId: number;
    trackingId: string;
    cameraId: string;
    reId: string;
    base64Image: string;
    humanTrackId: string;
    humanBase64: string;
    faceRecogConfident: number;
    reIdConfident: number;
    faceQuality: number;
}