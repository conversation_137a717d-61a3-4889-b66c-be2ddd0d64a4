import { Prisma } from '.prisma/client';
import { BadRequestException, ForbiddenException, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { RoleType, ServerConfigKey } from '@prisma/client';
import { join } from 'path';
import { CoreAiImageService } from 'src/core-ai-image/core-ai-image.service';
import { CheckInOutArgs, FaceResultDebugInfo } from 'src/face-result/models/checkInOut.args';
import { SrcDeviceGql } from 'src/face-result/models/src-device.gql';
import { PrismaService } from 'src/prisma/prisma.service';
import { orphanResultBackupFolder } from 'src/utils/constants';
import { dateUtil } from 'src/utils/date.util';
import { UnknownFaceResultGql } from './models/unknown-face-result.gql';
import { UnknownReportGql } from './models/unknown-report.gql';
import { CameraGql } from 'src/camera/models/camera.gql';
import { CameraFilterService } from 'src/camera-filter/camera-filter.service';
import { DeviceFilterService } from 'src/device-filter/device-filter.service';
import { FaceResultGeneralIntegrationService } from 'src/face-result-general-integration/face-result-general-integration.service';
import * as moment from 'moment';
import { base64Util } from 'src/utils/base64';
import * as fs from 'fs';
import { v4 } from 'uuid';
import { Interval, SchedulerRegistry } from '@nestjs/schedule';
import { IgnoreReIdGql } from './models/gqls';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';
import { DeviceType } from 'src/data/enums/device.type.enum';
import { FaceResultDebugInfoGql, FaceResultGql } from 'src/face-result/models/face-result.gql';
import { StringUtils } from 'src/utils/string';
import { ErrorEnum } from 'src/data/enums/error.enum';
import { UnknownBatchInsertModel } from './models/models';
const Denque = require("denque");

@Injectable()
export class OrphanFaceResultService implements OnModuleInit {
    private readonly logger = new Logger();
    private isDebug = false;
    private cachedUnknown = new Denque();
    private isProcessing = false;

    private cachedCreateOrphan: Prisma.OrphanFaceResultCreateInput[] = [];
    private lastBatchInsertTime = new Date(0);
    private isBatchInserting = false;

    async onModuleInit() {
        const autoScanUnknownQueueJob = setInterval(this.queueProcess.bind(this), 1000);
        this.schedulerRegistry.addInterval("autoScanUnknownQueue", autoScanUnknownQueueJob);

        const autoUpsertManyUnknownJob = setInterval(this.upsertMany.bind(this), 1000);
        this.schedulerRegistry.addInterval("autoUpsertManyUnknown", autoUpsertManyUnknownJob);
    }

    constructor(
        private prisma: PrismaService,
        private coreAiImageService: CoreAiImageService,
        private deviceFilterService: DeviceFilterService,
        private camFilterService: CameraFilterService,
        private resultIntegrateService: FaceResultGeneralIntegrationService,
        private cachedGlobalConfig: CachedGlobalConfigService,
        private schedulerRegistry: SchedulerRegistry,
    ) { }

    async createUnknownMqtt(checkInOutArgs: CheckInOutArgs) {
        if (checkInOutArgs.face_reid) {
            if (this.cachedGlobalConfig.cachedIgnoredReIdIs.has(checkInOutArgs.face_reid)) {
                return; //drop ignored reid
            }
        }

        checkInOutArgs.updatedAt = dateUtil.isValidDate(checkInOutArgs.updatedAt) ? checkInOutArgs.updatedAt : new Date();
        let imageFileName = `${moment(checkInOutArgs.updatedAt ?? new Date()).format("YYYY-MM-DD")}/${checkInOutArgs.recognize_id}/${new Date(checkInOutArgs.updatedAt).getTime()}_${v4()}.jpg`;
        let savePath = join(orphanResultBackupFolder(), imageFileName);
        base64Util.saveImageToFIle(checkInOutArgs.detected_face, savePath);
        checkInOutArgs.savedFilePath = imageFileName;
        this.cachedUnknown.push(checkInOutArgs);
        let debugObject = null;
        if (checkInOutArgs.debug) {
            debugObject = new FaceResultDebugInfo(JSON.parse(checkInOutArgs.debug));
        }
        this.resultIntegrateService.integrateBatchUnknown([
            new UnknownBatchInsertModel({
                deviceId: checkInOutArgs.device_id,
                srcDeviceId: checkInOutArgs.srcDeviceId,
                image: checkInOutArgs.savedFilePath,
                time: checkInOutArgs.updatedAt,
                orphanId: v4(),
                trackingId: checkInOutArgs.tracking_id,
                cameraId: checkInOutArgs.channel_id,
                reId: checkInOutArgs.face_reid,
                base64Image: "",
                faceRecogConfident: debugObject?.recognize_conf,
                reIdConfident: debugObject?.reid_conf,
                humanTrackId: checkInOutArgs.human_tid,
                faceQuality: debugObject?.quality
            })
        ]);
    }

    async queueProcess() {
        if (this.cachedUnknown.length > 0 && this.isDebug)
            this.logger.debug(`Unknown cached size: ${this.cachedUnknown.length}`);

        if (this.cachedUnknown.length == 0) {
            this.isProcessing = false;
        } else {
            if (this.isProcessing) return;
            let checkInOutArgs: CheckInOutArgs = this.cachedUnknown.shift();
            if (!checkInOutArgs) {
                this.isProcessing = false;
                return;
            }
            this.isProcessing = true;

            // let checkFaceRes = await this.prisma.faceResult.findFirst({
            //     where: {
            //         trackingId: checkInOutArgs.tracking_id,
            //         time: checkInOutArgs.updatedAt,
            //         srcDeviceId: checkInOutArgs.srcDeviceId,
            //     }, select: { id: true }
            // });
            // if (!checkFaceRes) {
            let imageFileName = checkInOutArgs.savedFilePath;
            if (!imageFileName) {
                imageFileName = `${moment(checkInOutArgs.updatedAt ?? new Date()).format("YYYY-MM-DD")}/${checkInOutArgs.recognize_id}/${new Date(checkInOutArgs.updatedAt).getTime()}_${v4()}.jpg`;
                let savePath = join(orphanResultBackupFolder(), imageFileName);
                base64Util.saveImageToFIle(checkInOutArgs.detected_face, savePath);
            }
            let createData: Prisma.OrphanFaceResultCreateInput = {
                userId: checkInOutArgs.recognize_id,
                trackingId: checkInOutArgs.tracking_id ?? null,
                reId: checkInOutArgs.face_reid,
                type: checkInOutArgs.type,
                image: imageFileName,
                cameraIp: checkInOutArgs.src_id,
                deviceId: checkInOutArgs.device_id,
                cameraId: checkInOutArgs.channel_id,
                time: checkInOutArgs.updatedAt,
                userName: checkInOutArgs.recognize_name,
                humanTrackId: checkInOutArgs.human_tid,
                imageId: checkInOutArgs.messageid,
            };

            if (checkInOutArgs.srcDeviceId) {
                createData.SrcDevice = { connect: { id: checkInOutArgs.srcDeviceId } };
            } else {
                for (let [key, value] of this.cachedGlobalConfig.deviceInfo) {
                    if (
                        (
                            value.id == checkInOutArgs.device_id
                            || value.id == checkInOutArgs.src_id
                            || (value.srcIp == checkInOutArgs.src_id && value.type != DeviceType.AIBox && value.type != DeviceType.UpdaterAndMatcherOnly)
                        )
                        && value.type != DeviceType.Unidentified
                    ) {
                        createData.SrcDevice = { connect: { id: value.id } };
                    }
                }
            }

            if (checkInOutArgs.debug) {
                let debugObject = new FaceResultDebugInfo(JSON.parse(checkInOutArgs.debug));
                createData.detectDuration = debugObject.detect_duration;
                createData.vectorDuration = debugObject.feature_ex_duration;
                createData.headposePitch = debugObject.headpose_pitch;
                createData.headposeRoll = debugObject.headpose_roll;
                createData.headposeYaw = debugObject.headpose_yaw;
                createData.faceQualityScore = debugObject.quality;
                createData.recogConfident = debugObject.recognize_conf;
                createData.reqTimePoint = dateUtil.isValidDate(debugObject.recog_timepoint) ? debugObject.recog_timepoint : new Date(0);
                createData.reqExDuration = debugObject.req_ex_duration;
                createData.searchDuration = debugObject.search_duration;
                createData.reIdConf = debugObject.reid_conf;
                createData.facekpts = debugObject.face_kpts?.join(',');
                createData.maskConfident = debugObject.mask_conf;
                createData.maskDuration = debugObject.mask_duration;
                createData.headposeDuration = debugObject.headpose_duration;
                createData.qualityDuration = debugObject.quality_duration;
                createData.detectTimePoint = debugObject.detect_timepoint;
            }

            this.cachedCreateOrphan.push(createData)
            await this.upsertMany();
            this.isProcessing = false;
            this.queueProcess();
        }
    }

    async upsertMany() {
        if (this.isBatchInserting) return;
        this.isBatchInserting = true;

        if (this.cachedGlobalConfig.isDebugUnknownBatch) {
            this.logger.log(`Size: ${this.cachedCreateOrphan.length}
                Scan time: ${new Date().getTime()}
                Last insert time: ${this.lastBatchInsertTime.getTime()}
                Max batch size: ${this.cachedGlobalConfig.unknownInsertBatchSize}
                Max interval: ${this.cachedGlobalConfig.unknownInsertInterval}
                `, "Unknown batch scan");
        }

        if (this.cachedCreateOrphan.length >= this.cachedGlobalConfig.unknownInsertBatchSize || (new Date().getTime() - this.lastBatchInsertTime.getTime()) > this.cachedGlobalConfig.unknownInsertInterval) {
            if (this.cachedCreateOrphan.length > 0) {
                let rawTextValues = this.cachedCreateOrphan.map(t => `(
                    '${t.cameraIp ?? ""}','${t.cameraId ?? ""}',
                    '${t.deviceId ?? ""}','${t.image ?? ""}','${new Date(t.time).toISOString()}','${t.trackingId ?? ""}',
                    ${t.detectDuration ?? null},${t.detectTimePoint ?? null},
                    '${t.facekpts ?? ""}',${t.faceQualityScore ?? null},
                    '${t.hashCode ?? ""}',${t.headposeDuration ?? null},${t.headposePitch ?? null},'${t.humanTrackId ?? ""}',
                    ${t.maskConfident ?? null},${t.headposeRoll ?? null},${t.headposeYaw ?? null},${t.maskDuration ?? null},
                    ${t.qualityDuration ?? null},${t.recogConfident ?? null},'${t.reId ?? ""}',
                    ${t.reIdConf ?? null},${t.reqExDuration ?? null},'${new Date(t.reqTimePoint).toISOString()}','${t.userId ?? ""}',
                    ${t.searchDuration ?? null},${t.type ?? null},'${t.userName ?? ""}',
                    ${t.SrcDevice?.connect?.id ? `'` + t.SrcDevice?.connect?.id + `'` : null},${t.vectorDuration ?? null},
                    '${t.imageId ?? ""}'
                    )`);

                let queryText = `
                    INSERT INTO public."OrphanFaceResult"(
                        "cameraIp", "cameraId", 
                        "deviceId", "image", "time", "trackingId", 
                        "detectDuration", "detectTimePoint", 
                        "facekpts", "faceQualityScore", 
                        "hashCode", "headposeDuration", "headposePitch", "humanTrackId", 
                        "maskConfident", "headposeRoll", "headposeYaw", "maskDuration", 
                        "qualityDuration", "recogConfident", "reId", 
                        "reIdConf", "reqExDuration", "reqTimePoint", "userId", 
                        "searchDuration", "type", "userName", 
                        "srcDeviceId", "vectorDuration",
                        "imageId"
                    ) VALUES ${rawTextValues.join(',')} 
                    RETURNING id, "deviceId", "trackingId", "srcDeviceId", "image", "time", "cameraId", "reId", "imageId", "recogConfident", "reIdConf";
                `;

                try {
                    let batchInserted = await this.prisma.$queryRaw < {
                        id: number,
                        deviceId: string,
                        trackingId: string,
                        srcDeviceId: string,
                        image: string,
                        time: Date,
                        cameraId: string,
                        reId: string,
                        imageId: string,
                        recogConfident: number,
                        reIdConf: number,
                    }[]> `${Prisma.raw(queryText)}`;

                    if (this.cachedGlobalConfig.isDebugUnknownBatch) {
                        this.logger.log(batchInserted.length, "Unknown inserted")
                    }

                    //batch insert face track id
                    // Remove duplicate trackingId entries
                    const uniqueTrackIds = this.cachedCreateOrphan.reduce((acc, current) => {
                        const x = acc.find(item => item.trackingId === current.trackingId);
                        if (!x) {
                            return acc.concat([current]);
                        } else {
                            return acc;
                        }
                    }, []);

                    this.coreAiImageService.upsertMultipleTrackId(
                        uniqueTrackIds.map(t => {
                            return {
                                trackingId: t.trackingId,
                                deviceId: t.deviceId,
                                srcDeviceId: t.SrcDevice?.connect?.id,
                                cameraId: t.cameraId
                            }
                        })
                    );

                    //batch insert coreAiImage
                    // this.coreAiImageService.upsertMultipleCoreAiImage(batchInserted.map(b => {
                    //     return {
                    //         deviceId: b.deviceId,
                    //         imageId: b.id.toString(),
                    //         orphanEventId: b.id,
                    //         faceResultEventId: null,
                    //         trackId: b.trackingId
                    //     }
                    // }));

                    //batch unknown integration
                    // this.resultIntegrateService.integrateBatchUnknown(batchInserted.map(b => {
                    //     return new UnknownBatchInsertModel({
                    //         deviceId: b.deviceId,
                    //         srcDeviceId: b.srcDeviceId,
                    //         image: b.image,
                    //         time: b.time,
                    //         orphanId: b.id,
                    //         trackingId: b.trackingId,
                    //         cameraId: b.cameraId,
                    //         reId: b.reId,
                    //         base64Image: "",
                    //         faceRecogConfident: b.recogConfident,
                    //         reIdConfident: b.reIdConf,
                    //     })
                    // }));
                    this.isProcessing = false;
                } catch (err) {
                    this.logger.error(err);
                    this.isProcessing = false;
                }
                this.cachedCreateOrphan = [];
                this.lastBatchInsertTime = new Date();
            }
        }
        this.isBatchInserting = false;

        return this.cachedCreateOrphan.length;
    }

    async getUnkownDataByPage(
        startTime: Date,
        endTime: Date,
        page: number,
        perPage: number,
        trackingId: string,
        cameraIds: string[],
        currentUserId: string,
        currentUserRole: string,
    ): Promise<UnknownReportGql[]> {
        if (currentUserRole != RoleType.SUPER_ADMIN &&
            currentUserRole != RoleType.ADMIN
        ) throw new ForbiddenException();

        let allowDeviceIds = await this.deviceFilterService.filterDeviceByRole(currentUserId, currentUserRole);
        let allowCamIds = await this.camFilterService.filterCameraByRole(currentUserId, currentUserRole, cameraIds);

        let returnData: UnknownReportGql[] = [];

        //#region Create filter
        let filter: Prisma.OrphanFaceResultWhereInput = {
            userId: 'unknown',
            time: {
                gte: startTime,
                lte: endTime
            }
        };
        if (trackingId) filter.trackingId = trackingId;

        if (allowDeviceIds.length > 0 && allowCamIds.length > 0) {
            filter.OR = [
                { deviceId: { in: allowDeviceIds } },
                { srcDeviceId: { in: allowDeviceIds } },
                { cameraId: { in: allowCamIds } }
            ];
        } else {
            if (allowDeviceIds.length == 0 && allowCamIds.length == 0) {
                if (currentUserRole != RoleType.SUPER_ADMIN) {
                    return returnData;
                }
            } else {
                if (allowDeviceIds.length > 0) {
                    filter.OR = [
                        { deviceId: { in: allowDeviceIds } },
                        { srcDeviceId: { in: allowDeviceIds } },
                    ];
                }

                if (allowCamIds.length > 0) {
                    filter.cameraId = { in: cameraIds };
                }
            }
        }
        //#endregion

        //#region Get all orphan valid
        let unkonwnResults = await this.prisma.orphanFaceResult.findMany({
            where: filter,
            select: {
                trackingId: true,
                deviceId: true
            },
            orderBy: { time: 'desc' },
        });
        //#endregion

        //#region Group orphan by trackingId and deviceId
        let loopedTrackIds: {
            trackingId: string,
            deviceId: string
        }[] = [];
        //Get tracking id page
        for (let index = 0; index < unkonwnResults.length; index++) {
            const unknownData = unkonwnResults[index];
            let isExited = loopedTrackIds.some(data =>
                data.trackingId == unknownData.trackingId &&
                data.deviceId == unknownData.deviceId
            );

            if (!isExited)
                loopedTrackIds.push({
                    trackingId: unknownData.trackingId,
                    deviceId: unknownData.deviceId
                });

            if (loopedTrackIds.length >= (page * perPage))
                break;
        }
        //#endregion

        //#region Get page
        let cutTrackIds: {
            trackingId: string,
            deviceId: string
        }[] = [];
        let startIndex = perPage * (page - 1);
        if (startIndex > loopedTrackIds.length - 1) {
            cutTrackIds = [];
        } else {
            let endIndex = (startIndex + perPage) > (loopedTrackIds.length) ? (loopedTrackIds.length - 1) : (startIndex + perPage);
            if (startIndex == endIndex) {
                cutTrackIds = [loopedTrackIds[startIndex]]
            } else {
                cutTrackIds = loopedTrackIds.slice(startIndex, endIndex);
            }
        }
        //#endregion

        //#region Get data page
        for (let index = 0; index < cutTrackIds.length; index++) {
            const cutTrack = cutTrackIds[index];
            filter.trackingId = cutTrack.trackingId;
            filter.deviceId = cutTrack.deviceId;
            let selectedUnknown = await this.prisma.orphanFaceResult.findMany({
                where: filter,
                orderBy: { time: 'desc' }
            });

            let count = await this.prisma.orphanFaceResult.count({
                where: filter,
            });

            //Remove element with large time diff
            for (let index = 0; index < selectedUnknown.length; index++) {
                const element = selectedUnknown[index];
                const nextElement = selectedUnknown[index + 1];
                if (nextElement) {
                    const eDate = new Date(element.time);
                    const nextEDate = new Date(nextElement.time)

                    if ((eDate.getTime() - nextEDate.getTime()) > 60000) {
                        selectedUnknown.splice(index + 1);
                        break;
                    }
                }
            }

            //Add to return object
            let tmpObj = new UnknownReportGql({
                trackingId: trackingId,
                data: selectedUnknown.map(t => new UnknownFaceResultGql({
                    id: t.id,
                    image: fs.existsSync(join(orphanResultBackupFolder(), t.image)) ?
                        base64Util.encodeImage(join(orphanResultBackupFolder(), t.image)) :
                        t.image,
                    cameraIp: t.cameraIp,
                    deviceId: t.deviceId,
                    time: t.time
                })),
                count: count
            });
            returnData.push(tmpObj);
        }
        //#endregion

        return returnData;
    }

    async allUnkownGroupByTrackingId(
        startTime: Date,
        endTime: Date,
        page: number,
        perPage: number,
        search: string,
        currentUserId: string,
        currentUserRole: string,
    ): Promise<UnknownReportGql[]> {
        let returnData: UnknownReportGql[] = [];
        if (currentUserRole != RoleType.SUPER_ADMIN &&
            currentUserRole != RoleType.ADMIN
        ) throw new ForbiddenException();


        //#region Create filter
        let filter: Prisma.OrphanFaceResultWhereInput = {
            userId: 'unknown',
            time: {
                gte: startTime,
                lte: endTime
            }
        };
        if (search) filter.OR = [
            {
                userId: {
                    contains: search,
                    mode: 'insensitive'
                }
            },
            {
                userName: {
                    contains: search,
                    mode: 'insensitive'
                }
            },
            {
                trackingId: {
                    contains: search,
                    mode: 'insensitive'
                }
            },
            {
                deviceId: {
                    contains: search,
                    mode: 'insensitive'
                }
            },
            {
                cameraIp: {
                    contains: search,
                    mode: 'insensitive'
                }
            },
            // { userId: { search: search } },
            // { userName: { search: search } },
            // { trackingId: { search: search } },
            // { deviceId: { search: search } },
            // { cameraIp: { search: search } },
        ];


        if (currentUserRole == RoleType.ADMIN) {
            let allowDeviceIds = await this.deviceFilterService.filterDeviceByRole(currentUserId, currentUserRole);

            filter.deviceId = { in: allowDeviceIds };
            filter.srcDeviceId = { in: allowDeviceIds };
        }
        //#endregion
        let groupCount = (await this.prisma.orphanFaceResult.groupBy({
            by: [`deviceId`, `cameraIp`, `trackingId`],
            where: filter,
            _max: {
                time: true
            }, orderBy: {
                _max: {
                    time: 'desc'
                }
            }
        })).length;

        let trackingGroups = await this.prisma.orphanFaceResult.groupBy({
            by: [`deviceId`, `cameraIp`, `trackingId`],
            where: filter,
            _max: {
                time: true
            }, orderBy: {
                _max: {
                    time: 'desc'
                }
            },
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
        });

        for (let index = 0; index < trackingGroups.length; index++) {
            const trackingGroup = trackingGroups[index];
            let dataFilter: Prisma.OrphanFaceResultWhereInput = {
                userId: 'unknown',
                time: {
                    gte: startTime,
                    lte: endTime
                },
                deviceId: trackingGroup.deviceId,
                cameraIp: trackingGroup.cameraIp,
                trackingId: trackingGroup.trackingId
            }
            let unknownDatas = await this.prisma.orphanFaceResult.findMany({
                where: dataFilter,
                include: { SrcDevice: true },
                take: 10
            });
            let deviceRecog = await this.prisma.device.findFirst({
                where: { ip: trackingGroup.cameraIp },
            });
            returnData.push(new UnknownReportGql({
                trackingId: trackingGroup.trackingId,
                deviceId: trackingGroup.deviceId,
                cameraIp: trackingGroup.cameraIp,
                channelName: deviceRecog ? deviceRecog.name : "",
                images: unknownDatas.map(unknownData => unknownData.image),
                count: groupCount,
                SrcDevice: new SrcDeviceGql({
                    id: unknownDatas[0].SrcDevice.id,
                    name: unknownDatas[0].SrcDevice.name,
                }),
                time: unknownDatas[0].time
            }))
        };
        return returnData;
    }

    async removeRecognizedUnkown(
        trackingId: string,
        deviceId: string,
        cameraIp: string,
        recognizedTime: Date
    ): Promise<number> {
        let maxTimeAllow = new Date(recognizedTime);
        maxTimeAllow.setMinutes(recognizedTime.getMinutes() - 1);
        // let deleted = await this.prisma.orphanFaceResult.deleteMany({
        //     where: {
        //         trackingId: trackingId,
        //         deviceId: deviceId,
        //         cameraIp: cameraIp,
        //         time: { gte: maxTimeAllow }
        //     }
        // });
        // return deleted.count;
        return 0;
    }

    async getUnknown(
        startTime: Date,
        endTime: Date,
        deviceIds: string[],
        cameraIds: string[],
        page: number,
        perPage: number,
        currentUserId: string,
        currentUserRole: string,
    ): Promise<UnknownReportGql[]> {
        if (!dateUtil.isValidDate(startTime) || !dateUtil.isValidDate(endTime))
            return [];
        if (currentUserRole != RoleType.SUPER_ADMIN &&
            currentUserRole != RoleType.ADMIN
        ) throw new ForbiddenException();

        let filter: Prisma.OrphanFaceResultWhereInput = {
            time: {
                // gte: new Date(moment(startTime.setHours(startTime.getHours())).format(`YYYY - MM - DDTHH: mm: ss${ this.timeZoneOffset }`)),
                // lte: new Date(moment(endTime.setHours(endTime.getHours())).format(`YYYY - MM - DDTHH: mm: ss${ this.timeZoneOffset }`))
                gte: startTime,
                lte: endTime
            },
        }

        let allowDeviceIds = await this.deviceFilterService.filterDeviceByRole(currentUserId, currentUserRole, deviceIds);
        let allowCamIds = await this.camFilterService.filterCameraByRole(currentUserId, currentUserRole, deviceIds);

        if (currentUserRole == RoleType.ADMIN) {
            filter.srcDeviceId = { in: allowDeviceIds };

        } else {
            if (deviceIds.length !== 0) {
                filter.srcDeviceId = { in: deviceIds };
            }
        }

        if (currentUserRole == RoleType.ADMIN) {
            filter.cameraId = { in: allowCamIds };

        } else {
            if (allowCamIds.length !== 0) {
                filter.cameraId = { in: allowCamIds };
            }
        }

        let unknown = await this.prisma.orphanFaceResult.findMany({
            where: filter,
            orderBy: {
                time: 'desc'
            },
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            include: {
                SrcDevice: true
            },
        });
        let count = await this.prisma.orphanFaceResult.count({
            where: filter,
        });
        let result = unknown.map(el => {
            return {
                ...el,
                count: count,
            }
        })
        return result;
    }

    async getUnknownGroup(
        startTime: Date,
        endTime: Date,
        deviceIds: string[],
        cameraIds: string[],
        page: number,
        perPage: number,
        currentUserId: string,
        currentUserRole: string,
        search: string,
    ): Promise<UnknownReportGql[]> {
        if (!dateUtil.isValidDate(startTime) || !dateUtil.isValidDate(endTime))
            return [];

        if (currentUserRole != RoleType.SUPER_ADMIN &&
            currentUserRole != RoleType.ADMIN &&
            currentUserRole != RoleType.I_PARKING_ADMIN
        ) throw new ForbiddenException();

        let filter: Prisma.OrphanFaceResultWhereInput = {
            time: {
                // gte: new Date(moment(startTime.setHours(startTime.getHours())).format(`YYYY - MM - DDTHH: mm: ss${ this.timeZoneOffset }`)),
                // lte: new Date(moment(endTime.setHours(endTime.getHours())).format(`YYYY - MM - DDTHH: mm: ss${ this.timeZoneOffset }`))
                gte: startTime,
                lte: endTime,
            },
            reId: { notIn: Array.from(this.cachedGlobalConfig.cachedIgnoredReIdIs) },
            OR: [
                { trackingId: { contains: search, mode: 'insensitive' } },
                { reId: { contains: search, mode: 'insensitive' } }
            ]
        }
        if (currentUserRole == RoleType.ADMIN) {
            let allowDeviceIds = await this.deviceFilterService.filterDeviceByRole(currentUserId, currentUserRole, deviceIds);
            if (deviceIds.length !== 0) {
                let tmpDeviceIds: string[] = [];
                for (let index = 0; index < deviceIds.length; index++) {
                    const deviceId = deviceIds[index];
                    if (allowDeviceIds.includes(deviceId)) {
                        tmpDeviceIds.push(deviceId);
                    }
                }

                allowDeviceIds = tmpDeviceIds;
            }
            filter.srcDeviceId = { in: allowDeviceIds };
        } else {
            if (deviceIds.length > 0 && cameraIds.length > 0) {
                filter.OR = [
                    { srcDeviceId: { in: deviceIds } },
                    { cameraId: { in: cameraIds } }
                ];
            } else {
                if (deviceIds.length > 0) {
                    filter.srcDeviceId = { in: deviceIds };
                }

                if (cameraIds.length > 0) {
                    filter.cameraId = { in: cameraIds };
                }
            }
        }
        let unknownGroups = await this.prisma.orphanFaceResult.groupBy({
            by: ['trackingId'],
            where: filter,
            _min: {
                time: true
            },
            orderBy: {
                _min: { time: 'desc' }
            },
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            // include: {
            //     SrcDevice: true
            // }
        });
        let count = await this.prisma.orphanFaceResult.groupBy({
            by: ['trackingId'],
            where: filter,
        });

        let result: UnknownReportGql[] = []
        for (let index = 0; index < unknownGroups.length; index++) {
            const eachUnknownGroup = unknownGroups[index];
            let unknown = await this.prisma.orphanFaceResult.findFirst({
                where: {
                    trackingId: eachUnknownGroup.trackingId
                },
                orderBy: {
                    time: 'desc'
                },
                select: {
                    cameraIp: true,
                    time: true,
                    trackingId: true,
                    deviceId: true,
                    SrcDevice: { select: { name: true } },
                    cameraId: true,
                }
            })
            let gql = new UnknownReportGql({
                cameraIp: unknown.cameraIp,
                time: unknown.time,
                trackingId: unknown.trackingId,
                deviceId: unknown.deviceId,
                count: count.length,
            });

            if (unknown.SrcDevice) {
                gql.SrcDevice = new SrcDeviceGql({
                    name: unknown.SrcDevice.name
                });
            }

            if (unknown.cameraId) {
                let camera = this.cachedGlobalConfig.cameraInfo.get(unknown.cameraId);
                if (camera) {
                    gql.Camera = new CameraGql({
                        id: unknown.cameraId,
                        name: camera.name
                    })
                }
            }

            if (!unknown.SrcDevice && !unknown.cameraId) {
                let srcDevice = this.cachedGlobalConfig.deviceInfo.get(unknown.deviceId);

                gql.SrcDevice = new SrcDeviceGql({
                    name: srcDevice?.name ?? ""
                });
            }

            result.push(gql);

        }

        return result;
    }

    async clearOldOrphanResult() {
        let maxLogTimeConfig = await this.prisma.serverConfig.findFirst({
            where: { key: ServerConfigKey.LOG_MAX_TIME }
        });
        if (maxLogTimeConfig) {
            let maxMonth = parseInt(maxLogTimeConfig.value);
            let maxTime = new Date();
            maxTime.setMonth(maxTime.getMonth() - maxMonth);
            this.prisma.orphanFaceResult.deleteMany({
                where: { time: { lte: maxTime } }
            });
            return true;
        } else return false;
    }

    async getUnknownById(
        id: number
    ): Promise<FaceResultGql> {
        let orphanResult = await this.prisma.orphanFaceResult.findFirst({
            where: { id: id },
        });
        if (!orphanResult) throw new BadRequestException(StringUtils.generateExceptionString(ErrorEnum['ID not found']));

        let gql = new FaceResultGql({
            id: orphanResult.id,
            trackId: orphanResult.trackingId,
            userId: orphanResult.userId,
            deviceId: orphanResult.deviceId,
            type: orphanResult.type,
            time: orphanResult.time,
            cameraIp: orphanResult.cameraIp,
            dateCreated: orphanResult.dateCreated,
            dateModified: orphanResult.dateModified,
            // image: fs.existsSync(join(orphanResultBackupFolder(), orphanResult.image)) ?
            //     base64Util.encodeImage(join(orphanResultBackupFolder(), orphanResult.image)) :
            //     orphanResult.image,
            image: `/api/public/face-result/orphan/rotate/${orphanResult.image}`,
            DebugInfos: new FaceResultDebugInfoGql({
                detectDuration: orphanResult.detectDuration,
                faceQualityScore: orphanResult.faceQualityScore,
                headposePitch: orphanResult.headposePitch,
                headposeRoll: orphanResult.headposeRoll,
                headposeYaw: orphanResult.headposeYaw,
                vectorDuration: orphanResult.vectorDuration,
                recogConfident: orphanResult.recogConfident,
                reqExDuration: orphanResult.reqExDuration,
                reqTimePoint: orphanResult.reqTimePoint,
                searchDuration: orphanResult.searchDuration,
                reIdConf: orphanResult.reIdConf,
                faceKpts: orphanResult.facekpts,
                maskConfident: orphanResult.maskConfident,
                maskDuration: orphanResult.maskDuration,
                headposeDuration: orphanResult.headposeDuration,
                qualityDuration: orphanResult.qualityDuration,
                detectTimepoint: orphanResult.detectTimePoint,
            })
        });

        return gql;
    }
}
