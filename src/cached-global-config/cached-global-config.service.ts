import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Interval, SchedulerRegistry } from '@nestjs/schedule';
import { AccessControl, AiService, Camera, CameraRoi, CivamsCluster, Company, Department, Device, Guest, IparkingConfig, MqttConfigs, ReIdLabel, RoiAiService, ServerConfigKey, ShiftType, ShiftUser, User, UserFaceImage } from '@prisma/client';
import { CheckInOutArgs } from 'src/face-result/models/checkInOut.args';
import { PrismaService } from 'src/prisma/prisma.service';
import { time } from "src/utils/time";
import CoreFaceSignal from './models/models';

@Injectable()
export class CachedGlobalConfigService implements OnModuleInit {
    private logger = new Logger(CachedGlobalConfigService.name);
    private serverConfigLastSyncTime = new Date();
    private deviceInfoLastSyncTime = new Date(0);
    private mqttConfigLastSyncTime = new Date(0);
    private mqttConfigDeviceLimitLastSyncTime = new Date(0);
    private cameraInfoLastSyncTime = new Date(0);
    private reIdIgnoreLastSyncTime = new Date(0);
    private reIdLabelLastSyncTime = new Date(0);
    public isDupCamProcess = false;

    public defaultTimezoneOffset = "+07:00";
    public timeZoneName: string = time.timeZoneNameFrom(this.defaultTimezoneOffset);

    public defaultDbId = "CMC_CIST_60486a32eab84ec7589a1c1f";
    public defaultMatcherId = "face_id_matcher";
    public defaultPersonSearchId = "civams_person_reid_POC_matcher";
    public patientDbId = "";
    public isQualityBypass = false;
    public faceSimilarBypassThreshold = 0.55;
    public remoteFaceRecogTimeout = 50;

    public isSchedulerActive = false;

    public serverUniqueId = "";
    public serverChannelId: string = "";
    public serverIp = "localhost";

    public cachedFaceFps = 100;
    public cachedPersonFps = 100;
    public isSaveCachedFace = false;
    public isSaveCachedHuman = false;
    public isSaveHuman = false;

    public faceDupTimeFilter = 5;

    public logMaxBackupDays = 30;
    public videoBackupDays = 7;
    public isSaveEventVideo = false;

    public isFaceResMqttDebug = false;
    public isHumanResMqttDebug = false;
    public isEmotionDebug = false;
    public isHumanDebug = false;
    public isGeneralDebug = false;

    //iparking debug flag
    public iparkingCustomerSyncDebug = false;
    public iparkingIdentityGroupSyncDebug = false;
    public iparkingVehicleSyncDebug = false;
    public iparkingIdentitySyncDebug = false;

    public healthCheckDisk = 100;
    public healthCheckDiskThreshold = 0.8;
    public healCheckRam = 4;
    public healCheckRamThreshold = 0.8;

    public defaultGuestDepId = "2b2b91e3-e403-423d-b7f9-4416bc4deba5";

    public runningCoreFaces: Map<string, CoreFaceSignal> = new Map();
    public maxTimeWaithForCoreFaceSignal = 0.5;

    public isCoreAiSyncDebug = false;

    //unknown batch process
    public isDebugUnknownBatch = false;
    public unknownInsertBatchSize = 100;
    public unknownInsertInterval = 5000;

    //face result integration
    public faceResultIntegrationKeys: Set<number> = new Set();
    public jsonConfig: string = "";

    //users and guests
    public cachedAllUser: Map<string, User & {
        Company: Company;
        Department: Department;
        FaceImages: UserFaceImage[];
    }> = new Map();
    public cachedAllUserId: Set<string> = new Set();
    private lastUserSyncTime = new Date(0);
    private isSyncingUser = false;
    public cachedAllGuest: Map<string, Guest> = new Map();
    private lastGuestSyncTime = new Date(0);
    private isSyncingGuest = false;

    //civams cluster
    public cachedCivamsCluster: Map<string, CivamsCluster> = new Map();

    private serverConfigKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.SERVER_UINIQUE_ID,
        ServerConfigKey.SERVER_IP,
        ServerConfigKey.SERVER_CHANNEL_ID,
        ServerConfigKey.TZ,
    ]);

    private matcherConfigKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.IS_FACE_QUALITY_BYPASS,
        ServerConfigKey.FACE_QUALITY_SIMILARITY_THRESHOLD,
        ServerConfigKey.DEFAULT_FACE_IMAGE_MATCHER,
        ServerConfigKey.DEFAULT_DB_ID,
        ServerConfigKey.PERSON_SEARCH_DEFAULT_DEVICE_ID,
        ServerConfigKey.BROKER_REMOTE_FACE_RECOG_TIMEOUT,
    ]);

    private patientConfigKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.PATIENT_DB_ID,
    ]);

    private schedulerConfigKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.IS_SCHEDULER_THREAD_STAND_ALONE,
        ServerConfigKey.LOG_BACKUP_MAX_TIME,
        ServerConfigKey.EVENT_VIDEO_STORAGE_DAYS,
        ServerConfigKey.CACHED_FACE_FPS,
        ServerConfigKey.CACHED_PERSON_FPS,
        ServerConfigKey.IS_SAVE_EVENT_VIDEO,
    ]);

    private debugConfigKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.FACE_RES_MQTT_DEBUG,
        ServerConfigKey.HUMAN_RES_MQTT_DEBUG,
        ServerConfigKey.IS_DUPLICATE_CAMERA_PROCESS,
        ServerConfigKey.IS_DEBUG_EMOTION,
        ServerConfigKey.IS_DEBUG_HUMAN,
        ServerConfigKey.IS_DEBUG_GENERAL_LOG,
        ServerConfigKey.IPARKING_CUSTOMER_SYNC_DEBUG,
        ServerConfigKey.IPARKING_IDENTITY_GROUP_SYNC_DEBUG,
        ServerConfigKey.IPARKING_VEHICLE_SYNC_DEBUG,
        ServerConfigKey.IPARKING_IDENTITY_SYNC_DEBUG,
        ServerConfigKey.CORE_AI_SYNC_DEBUG,
        ServerConfigKey.IS_POPUP_ALARM_ENALBE,
        ServerConfigKey.DEVICE_API_SERVICE_DEBUG,
    ]);

    private healthCheckConfigKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.HEALTH_CHECK_STORAGE_SIZE,
        ServerConfigKey.HEALTH_CHECK_STORAGE_THRESHOLD,
        ServerConfigKey.HEALTH_CHECK_RAM_SIZE,
        ServerConfigKey.HEALTH_CHECK_RAM_THRESHOLD,
    ]);

    private cachedConfigKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.IS_SAVE_FACE_CACHED,
        ServerConfigKey.IS_SAVE_HUMAN_CACHED,
        ServerConfigKey.JSON_CUSTOME_CONFIG,
    ]);

    private batchUnknownKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.IS_DEBUG_UNKNOWN_BATCH,
        ServerConfigKey.UNKNOWN_INSERT_BATCH_SIZE,
        ServerConfigKey.UNKNOWN_INSERT_INTERVAL,
    ]);

    private securityConfigKeys: Set<ServerConfigKey> = new Set([
        ServerConfigKey.PERSON_COUNT_THRESHOLD_ALARM,
        ServerConfigKey.UNKNOWN_ALARM_CONFIDENCE_THRESHOLD,
        ServerConfigKey.UNKNOWN_ALARM_DELAY_SEC,
    ]);

    //#region cached values
    public deviceInfo: Map<string, Device> = new Map();
    public isDeviceApiServiceDebug: boolean = false;
    public cameraInfo: Map<string, Camera> = new Map();
    public cachedIgnoredReIdIs: Set<string> = new Set();
    public cachedReIdILabels: Map<string, ReIdLabel> = new Map();
    public cachedHumanRecheckId: Map<string, CheckInOutArgs> = new Map();
    public mqttAddinConfigs: Map<string, MqttConfigs> = new Map();
    public mqttAddinDeviceLimit: Map<string, Set<string>> = new Map();
    public cachedShiftUser: Map<string, (
        ShiftUser & {
            ShiftType: ShiftType;
        }
    )> = new Map();
    //#endregion

    //#region iprking integration configs
    public isIparkingEnabled = false;
    public cachedIparkingConfig: IparkingConfig = null;
    public cachedLicenseAllowed: Set<string> = new Set();
    private isCachedLicAllowSyncing = false;
    private cachedLicenseAllowedLastSyncTime = new Date(0);
    //#endregion

    //#region cached access control configs
    private isSyncingAccessControl = false;
    public cachedAccessControl: Map<string, AccessControl> = new Map();
    public cachedIdsInAccessControl: Map<string, Set<string>> = new Map();
    private acLastSyncTime = new Date(0);
    private acDeviceLastSyncTime = new Date(0);
    private acDeviceGroupLastSyncTime = new Date(0);
    private acCameraLastSyncTime = new Date(0);
    private acGuestLastSyncTime = new Date(0);
    private acUserLastSyncTime = new Date(0);
    private acDepLastSyncTime = new Date(0);
    private acComLastSyncTime = new Date(0);
    private acOrgLastSyncTime = new Date(0);

    //#endregion

    //#region security cached
    public personCrowAlarmThreshold = 5;
    public cachedAiServices: Map<string, AiService> = new Map();
    private isCachedAiServiceSyncing = false;
    private lastCachedAiServiceSyncTime = new Date(0);

    public cachedRoiAi: Map<string, Set<string>> = new Map();
    private isCachedRoiAiSyncing = false;
    private lastCachedRoiAiSyncTime = new Date(0);

    public isPopupAlarmEnable = false;
    public unknownAlarmThreshold = 0.3;
    public unknownQualityAlarmThreshold = 1.6;
    public unknownAlarmDelaySec = 4;
    //#endregion

    //#region video analytics cached
    public cachedUploadVideoIds: Set<string> = new Set();
    public lastCachedUploadVideoSyncTime = new Date(0);
    private isCachedUploadVideoSyncing = false;
    //#endregion

    async onModuleInit() {
        await this.loadConfigs([
            ...this.serverConfigKeys,
            ...this.matcherConfigKeys,
            ...this.patientConfigKeys,
            ...this.schedulerConfigKeys,
            ...this.debugConfigKeys,
            ...this.healthCheckConfigKeys,
            ...this.cachedConfigKeys,
            ...this.batchUnknownKeys,
            ...this.securityConfigKeys,
        ]);

        await this.autoLoadCachedValues();

        await this.checkIparkingActiveConfig();

        await this.autoCheckClusterConfig();

        if (!this.isSchedulerActive) {
            const autoScanCachedValues = setInterval(this.autoLoadCachedValues.bind(this), 60000);
            this.schedulerRegistry.addInterval("autoScanCachedValues", autoScanCachedValues);
        }
    }

    constructor(
        private prisma: PrismaService,
        private schedulerRegistry: SchedulerRegistry,
    ) { }

    //cached values
    async autoLoadCachedValues() {
        this.loadCacheUser();
        this.loadCacheGuest();
        this.loadCacheDeviceInfo();
        this.loadCachedIgnoredReIds();
        this.loadCachedReIdLabels();
        this.loadCacheMqttConfigs();
        this.loadCacheIdsInAccessControl();
        this.loadCacheAiService();
        this.loadCacheRoiAi();
        this.loadCachedLicenseAllowed();
        this.loadCacheUploadVideo();
    }

    async loadCacheUser() {
        if (this.isSyncingUser) { return; }
        this.isSyncingUser = true;
        try {
            let users = await this.prisma.user.findMany({
                where: { dateModified: { gt: this.lastUserSyncTime } },
                include: {
                    Company: true,
                    Department: true,
                    FaceImages: true,
                }, orderBy: { dateModified: 'asc' }
            });

            for (let index = 0; index < users.length; index++) {
                const user = users[index];
                this.cachedAllUser.set(user.id, user);
                this.cachedAllUserId.add(user.id);
                this.lastUserSyncTime = user.dateModified;
            }
            this.isSyncingUser = false;
        } catch (e) {
            this.lastUserSyncTime = new Date(0);
            this.isSyncingUser = false;
        }
    }

    async loadCacheGuest() {
        if (this.isSyncingGuest) { return; }
        this.isSyncingGuest = true;
        try {
            let guests = await this.prisma.guest.findMany({
                where: { dateModified: { gt: this.lastUserSyncTime } },
                orderBy: { dateModified: 'asc' }
            });

            for (let index = 0; index < guests.length; index++) {
                const guest = guests[index];
                this.cachedAllGuest.set(guest.id, guest);
                this.lastGuestSyncTime = guest.dateModified;
            }
            this.isSyncingGuest = false;
        } catch (e) {
            this.lastGuestSyncTime = new Date(0);
            this.isSyncingGuest = false;
        }

    }

    async loadCacheDeviceInfo() {
        let devices = await this.prisma.device.findMany({
            // where: { dateModified: { gt: this.deviceInfoLastSyncTime } },
            orderBy: { dateModified: 'asc' }
        });

        let allDeviceIds: string[] = [];
        devices.forEach(device => {
            allDeviceIds.push(device.id);
            this.deviceInfo.set(device.id, device);
            this.deviceInfoLastSyncTime = device.dateModified;
        });

        // Remove devices from deviceInfo if they are not in allDeviceIds
        this.deviceInfo.forEach((_, key) => {
            if (!allDeviceIds.includes(key)) {
                this.deviceInfo.delete(key);
            }
        });

        let cameras = await this.prisma.camera.findMany({
            // where: { dateModified: { gt: this.cameraInfoLastSyncTime } },
            orderBy: { dateModified: 'asc' }
        });

        let allCameraIds: string[] = [];
        cameras.forEach(camera => {
            allCameraIds.push(camera.id);
            this.cameraInfo.set(camera.id, camera);
            this.cameraInfoLastSyncTime = camera.dateModified;
        })

        // Remove cameras from cameraInfo if they are not in allCameraIds
        this.cameraInfo.forEach((_, key) => {
            if (!allCameraIds.includes(key)) {
                this.cameraInfo.delete(key);
            }
        });
    }

    async loadCachedIgnoredReIds() {
        let cachedIgnoredReIds = await this.prisma.ignoreReId.findMany({
            where: {
                dateModified: { gt: this.reIdIgnoreLastSyncTime }
            }, orderBy: { dateModified: 'asc' }
        });

        for (let index = 0; index < cachedIgnoredReIds.length; index++) {
            const reId = cachedIgnoredReIds[index];
            if (reId.isEnable) {
                this.cachedIgnoredReIdIs.add(reId.reId);
            } else {
                this.cachedIgnoredReIdIs.delete(reId.reId);
            }
            this.reIdIgnoreLastSyncTime = reId.dateModified;
        }
    }

    async loadCachedReIdLabels() {
        let cachedLabels = await this.prisma.reIdLabel.findMany({
            where: {
                dateModified: { gt: this.reIdLabelLastSyncTime }
            }, orderBy: { dateModified: 'asc' }
        });

        for (let index = 0; index < cachedLabels.length; index++) {
            const reIdLabel = cachedLabels[index];
            this.cachedReIdILabels.set(reIdLabel.reid, reIdLabel);
            this.reIdLabelLastSyncTime = reIdLabel.dateModified;
        }
    }

    async loadCacheMqttConfigs() {
        let mqttConfigs = await this.prisma.mqttConfigs.findMany({
            where: { dateModified: { gt: this.mqttConfigLastSyncTime } },
            include: { MqttConfigDevices: true },
            orderBy: { dateModified: 'asc' }
        });

        for (let index = 0; index < mqttConfigs.length; index++) {
            const mqttConfig = mqttConfigs[index];
            this.mqttAddinConfigs.set(mqttConfig.id, mqttConfig);
            if (mqttConfig.isDeviceLimit) {
                let deviceIds = mqttConfig.MqttConfigDevices?.filter(dev => !dev.isDeleted).map(dev => dev.deviceId);
                this.mqttAddinDeviceLimit.set(mqttConfig.id, new Set(deviceIds ?? []));
            }
            this.mqttConfigLastSyncTime = mqttConfig.dateModified;
        }

        let mqttDeviceLitmits = await this.prisma.mqttConfigDevices.findMany({
            where: { dateModified: { gt: this.mqttConfigDeviceLimitLastSyncTime } },
            orderBy: { dateModified: 'asc' }
        });

        for (let index = 0; index < mqttDeviceLitmits.length; index++) {
            const mqttDeviceLimit = mqttDeviceLitmits[index];
            let currentSet = this.mqttAddinDeviceLimit.get(mqttDeviceLimit.configId) ?? new Set();
            if (mqttDeviceLimit.isDeleted) {
                currentSet.delete(mqttDeviceLimit.deviceId);
            }
            else {
                currentSet.add(mqttDeviceLimit.deviceId);
            }
            this.mqttAddinDeviceLimit.set(mqttDeviceLimit.configId, currentSet);
            this.mqttConfigDeviceLimitLastSyncTime = mqttDeviceLimit.dateModified;
        }

        // this.mqttAddinDeviceLimit.forEach((deviceIds, configId) => {
        //     console.log(`Config ID: ${configId}, Device IDs: ${Array.from(deviceIds).join(', ')}`);
        // });
        // console.log(this.mqttAddinDeviceLimit)
    }

    async loadCacheIdsInAccessControl() {
        if (this.isSyncingAccessControl) { return; }
        this.isSyncingAccessControl = true;
        //cached access control info
        let accessControls = await this.prisma.accessControl.findMany({
            where: { dateModified: { gt: this.acLastSyncTime } },
            orderBy: { dateModified: 'asc' }
        });
        for (let index = 0; index < accessControls.length; index++) {
            const accessControl = accessControls[index];
            this.cachedAccessControl.set(accessControl.id, accessControl);
        }

        //cached access control devices
        let acDevices = await this.prisma.accessControlDevice.findMany({
            where: { dateModified: { gt: this.acDeviceLastSyncTime } },
            orderBy: { dateModified: 'asc' }
        });

        for (let index = 0; index < acDevices.length; index++) {
            const acDevice = acDevices[index];
            let cachedDeviceAc = this.cachedIdsInAccessControl.get(acDevice.deviceId);
            if (acDevice.isEnable) {
                if (cachedDeviceAc) {
                    cachedDeviceAc.add(acDevice.accessControlId);
                    this.cachedIdsInAccessControl.set(acDevice.deviceId, cachedDeviceAc);
                } else {
                    this.cachedIdsInAccessControl.set(acDevice.deviceId, new Set([acDevice.accessControlId]));
                }
            } else {
                if (cachedDeviceAc) {
                    cachedDeviceAc.delete(acDevice.accessControlId);
                    this.cachedIdsInAccessControl.set(acDevice.deviceId, cachedDeviceAc);
                }
            }

            this.acDeviceLastSyncTime = acDevice.dateModified;
        }

        //cached access control device groups
        let acDeiceGroups = await this.prisma.accessControlDeviceGroup.findMany({
            where: { dateModified: { gt: this.acDeviceGroupLastSyncTime } },
            orderBy: { dateModified: 'asc' }
        })

        for (let index = 0; index < acDeiceGroups.length; index++) {
            const acDeviceGroup = acDeiceGroups[index];
            let cachedDeviceGroupAc = this.cachedIdsInAccessControl.get(acDeviceGroup.deviceGroupInfoId);
            if (acDeviceGroup.isEnable) {
                if (cachedDeviceGroupAc) {
                    cachedDeviceGroupAc.add(acDeviceGroup.accessControlId);
                    this.cachedIdsInAccessControl.set(acDeviceGroup.deviceGroupInfoId, cachedDeviceGroupAc);
                } else {
                    this.cachedIdsInAccessControl.set(acDeviceGroup.deviceGroupInfoId, new Set(acDeviceGroup.accessControlId));
                }
            } else {
                if (cachedDeviceGroupAc) {
                    cachedDeviceGroupAc.delete(acDeviceGroup.accessControlId);
                    this.cachedIdsInAccessControl.set(acDeviceGroup.deviceGroupInfoId, cachedDeviceGroupAc);
                }
            }

            this.acDeviceGroupLastSyncTime = acDeviceGroup.dateModified;
        }

        //cached access control cameras
        let acCameras = await this.prisma.accessControlCamera.findMany({
            where: { dateModified: { gt: this.acCameraLastSyncTime } },
            orderBy: { dateModified: 'asc' }
        });

        for (let index = 0; index < acCameras.length; index++) {
            const acCamera = acCameras[index];
            let cachedCameraAc = this.cachedIdsInAccessControl.get(acCamera.cameraId);
            if (acCamera.isEnable) {
                if (cachedCameraAc) {
                    cachedCameraAc.add(acCamera.accessControlId);
                    this.cachedIdsInAccessControl.set(acCamera.cameraId, cachedCameraAc);
                } else {
                    this.cachedIdsInAccessControl.set(acCamera.cameraId, new Set([acCamera.accessControlId]));
                }
            } else {
                if (cachedCameraAc) {
                    cachedCameraAc.delete(acCamera.accessControlId);
                    this.cachedIdsInAccessControl.set(acCamera.cameraId, cachedCameraAc);
                }
            }

            this.acCameraLastSyncTime = acCamera.dateModified;
        }

        //cached access control guests
        // let acGuests = await this.prisma.accessControlGuest.findMany({
        //     where: { dateModified: { gt: this.acGuestLastSyncTime } },
        //     orderBy: { dateModified: 'asc' }
        // });

        // for (let index = 0; index < acGuests.length; index++) {
        //     const acGuest = acGuests[index];
        //     let cachedGuest = this.cachedIdsInAccessControl.get(acGuest.accessControlId);
        //     if (cachedGuest) {
        //         cachedGuest.add(acGuest.guestId);
        //         this.cachedIdsInAccessControl.set(acGuest.accessControlId, cachedGuest);
        //     } else {
        //         this.cachedIdsInAccessControl.set(acGuest.accessControlId, new Set([acGuest.guestId]));
        //     }

        //     this.acGuestLastSyncTime = acGuest.dateModified;
        // }

        //cached access control users
        // let acUsers = await this.prisma.accessControlUser.findMany({
        //     where: { dateModified: { gt: this.acUserLastSyncTime } },
        //     orderBy: { dateModified: 'asc' }
        // });

        // for (let index = 0; index < acUsers.length; index++) {
        //     const acUser = acUsers[index];
        //     let cachedUser = this.cachedIdsInAccessControl.get(acUser.accessControlId);
        //     if (cachedUser) {
        //         cachedUser.add(acUser.userId);
        //         this.cachedIdsInAccessControl.set(acUser.accessControlId, cachedUser);
        //     } else {
        //         this.cachedIdsInAccessControl.set(acUser.accessControlId, new Set([acUser.userId]));
        //     }

        //     this.acUserLastSyncTime = acUser.dateModified;
        // }

        //cached access control departments
        // let acDeps = await this.prisma.accessControlDepartment.findMany({
        //     where: { dateModified: { gt: this.acDepLastSyncTime } },
        //     orderBy: { dateModified: 'asc' }
        // });

        // for (let index = 0; index < acDeps.length; index++) {
        //     const acDep = acDeps[index];
        //     let cachedDep = this.cachedIdsInAccessControl.get(acDep.accessControlId);
        //     if (cachedDep) {
        //         cachedDep.add(acDep.departmentId);
        //         this.cachedIdsInAccessControl.set(acDep.accessControlId, cachedDep);
        //     } else {
        //         this.cachedIdsInAccessControl.set(acDep.accessControlId, new Set([acDep.departmentId]));
        //     }

        //     this.acDepLastSyncTime = acDep.dateModified;
        // }

        //cached access control companies
        // let acComs = await this.prisma.accessControlCompany.findMany({
        //     where: { dateModified: { gt: this.acComLastSyncTime } },
        //     orderBy: { dateModified: 'asc' }
        // });

        // for (let index = 0; index < acComs.length; index++) {
        //     const acCom = acComs[index];
        //     let cachedCom = this.cachedIdsInAccessControl.get(acCom.accessControlId);
        //     if (cachedCom) {
        //         cachedCom.add(acCom.companyId);
        //         this.cachedIdsInAccessControl.set(acCom.accessControlId, cachedCom);
        //     } else {
        //         this.cachedIdsInAccessControl.set(acCom.accessControlId, new Set([acCom.companyId]));
        //     }

        //     this.acComLastSyncTime = acCom.dateModified;
        // }

        //cached access control organizations
        // let acOrgs = await this.prisma.accessControlOrg.findMany({
        //     where: { dateModified: { gt: this.acOrgLastSyncTime } },
        //     orderBy: { dateModified: 'asc' }
        // });

        // for (let index = 0; index < acOrgs.length; index++) {
        //     const acOrg = acOrgs[index];
        //     let cachedOrg = this.cachedIdsInAccessControl.get(acOrg.accessControlId);
        //     if (cachedOrg) {
        //         cachedOrg.add(acOrg.orgId);
        //         this.cachedIdsInAccessControl.set(acOrg.accessControlId, cachedOrg);
        //     } else {
        //         this.cachedIdsInAccessControl.set(acOrg.accessControlId, new Set([acOrg.orgId]));
        //     }

        //     this.acOrgLastSyncTime = acOrg.dateModified;
        // }

        this.isSyncingAccessControl = false;
    }
    async loadCacheDeviceAcMap() { }

    async loadCacheAiService() {
        if (this.isCachedAiServiceSyncing) { return; }
        this.isCachedAiServiceSyncing = true;
        //cached access control info
        let aiServices = await this.prisma.aiService.findMany({
            where: { dateModified: { gt: this.lastCachedAiServiceSyncTime } },
            orderBy: { dateModified: 'asc' },
        });
        for (let index = 0; index < aiServices.length; index++) {
            const aiService = aiServices[index];
            this.cachedAiServices.set(aiService.id, aiService);
            this.lastCachedAiServiceSyncTime = aiService.dateModified;
        }

        this.isCachedAiServiceSyncing = false;
    }

    async loadCacheRoiAi() {
        if (this.isCachedRoiAiSyncing) { return; }
        this.isCachedRoiAiSyncing = true;
        //cached access control info
        let roiAis = await this.prisma.roiAiService.findMany({
            where: { dateModified: { gt: this.lastCachedRoiAiSyncTime } },
            orderBy: { dateModified: 'asc' },
        });
        for (let index = 0; index < roiAis.length; index++) {
            const roiAi = roiAis[index];
            let aiServiceSet = this.cachedRoiAi.get(roiAi.cameraRoiId);
            if (!aiServiceSet) {
                this.cachedRoiAi.set(roiAi.cameraRoiId, new Set([roiAi.aiServiceId]));
            } else {
                aiServiceSet.add(roiAi.aiServiceId);
                this.cachedRoiAi.set(roiAi.cameraRoiId, aiServiceSet);
            }
            this.lastCachedAiServiceSyncTime = roiAi.dateModified;
        }

        this.isCachedRoiAiSyncing = false;
    }

    //server configs
    async autoReloadConfig() {
        let changedConfigs = await this.prisma.serverConfig.findMany({
            where: { dateModified: { gt: this.serverConfigLastSyncTime } },
            select: {
                key: true,
                dateModified: true
            },
            orderBy: { dateModified: 'asc' }
        })

        if (changedConfigs.length > 0) {
            let changedKeys = changedConfigs.map(key => key.key);
            this.serverConfigLastSyncTime = changedConfigs[changedConfigs.length - 1].dateModified;
            await this.loadConfigs(changedKeys);
        }
    }

    async loadConfigs(
        changedKeys: ServerConfigKey[]
    ) {
        for (let index = 0; index < changedKeys.length; index++) {
            const key = changedKeys[index];
            let config = await this.prisma.serverConfig.findFirst({
                where: { key: key }
            });
            if (!config) continue;

            switch (key) {
                case ServerConfigKey.SERVER_UINIQUE_ID:
                    this.serverUniqueId = config.value;
                    break;
                case ServerConfigKey.SERVER_IP:
                    this.serverIp = config.value;
                    break;
                case ServerConfigKey.SERVER_CHANNEL_ID:
                    this.serverChannelId = config.value;
                    break;
                case ServerConfigKey.TZ:
                    const timeZoneName = time.timeZoneNameFrom(config.value);
                    if (timeZoneName) {
                        this.defaultTimezoneOffset = config.value;
                        this.timeZoneName = timeZoneName;
                    }
                    break;

                case ServerConfigKey.IS_FACE_QUALITY_BYPASS:
                    this.isQualityBypass = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.FACE_QUALITY_SIMILARITY_THRESHOLD:
                    this.faceSimilarBypassThreshold = isNaN(parseFloat(config.value)) ? 0.55 : parseFloat(config.value);
                    break;
                case ServerConfigKey.DEFAULT_DB_ID:
                    this.defaultDbId = config.value;
                    break;
                case ServerConfigKey.DEFAULT_FACE_IMAGE_MATCHER:
                    this.defaultMatcherId = config.value;
                    break;
                case ServerConfigKey.PERSON_SEARCH_DEFAULT_DEVICE_ID:
                    this.defaultPersonSearchId = config.value;
                    break;

                case ServerConfigKey.PATIENT_DB_ID:
                    this.patientDbId = config.value;
                    break;

                case ServerConfigKey.IS_SCHEDULER_THREAD_STAND_ALONE:
                    this.isSchedulerActive = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.CACHED_FACE_FPS:
                    let faceNumber = parseInt(config.value);
                    if (!isNaN(faceNumber)) {
                        this.cachedFaceFps = faceNumber;
                        // console.log("cachedFaceFps", this.cachedFaceFps);
                    }
                    break;
                case ServerConfigKey.CACHED_PERSON_FPS:
                    let personNumber = parseInt(config.value);
                    if (!isNaN(personNumber)) {
                        this.cachedPersonFps = personNumber;
                    }
                    break;
                case ServerConfigKey.DUPLICATE_RESULT_TIME_FILTER:
                    let timeDupFilterNumber = parseInt(config.value);
                    if (!isNaN(timeDupFilterNumber)) {
                        this.faceDupTimeFilter = timeDupFilterNumber;
                    }
                    break;

                case ServerConfigKey.BROKER_REMOTE_FACE_RECOG_TIMEOUT:
                    let timeout = parseInt(config.value);
                    if (!isNaN(timeout)) {
                        this.remoteFaceRecogTimeout = timeout;
                    }
                    break;
                case ServerConfigKey.LOG_BACKUP_MAX_TIME:
                    let logBackupTime = parseInt(config.value);
                    if (!isNaN(logBackupTime)) {
                        this.logMaxBackupDays = logBackupTime;
                    }
                    break;
                case ServerConfigKey.EVENT_VIDEO_STORAGE_DAYS:
                    let videoBackupTime = parseInt(config.value);
                    if (!isNaN(videoBackupTime)) {
                        this.videoBackupDays = videoBackupTime;
                    }
                    break;
                case ServerConfigKey.IS_SAVE_EVENT_VIDEO:
                    this.isSaveEventVideo = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.FACE_RES_MQTT_DEBUG:
                    this.isFaceResMqttDebug = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.HUMAN_RES_MQTT_DEBUG:
                    this.isHumanResMqttDebug = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.HEALTH_CHECK_STORAGE_SIZE:
                    this.healthCheckDisk = parseFloat(config.value);
                    break;
                case ServerConfigKey.HEALTH_CHECK_STORAGE_THRESHOLD:
                    this.healthCheckDiskThreshold = parseFloat(config.value);
                    break;
                case ServerConfigKey.HEALTH_CHECK_RAM_SIZE:
                    this.healCheckRam = parseFloat(config.value);
                    break;
                case ServerConfigKey.HEALTH_CHECK_RAM_THRESHOLD:
                    this.healCheckRamThreshold = parseFloat(config.value);
                    break;
                case ServerConfigKey.IS_DUPLICATE_CAMERA_PROCESS:
                    this.isDupCamProcess = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.IS_SAVE_HUMAN_CAPTURE:
                    this.isSaveHuman = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.IS_SAVE_HUMAN_CACHED:
                    this.isSaveCachedHuman = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.IS_SAVE_FACE_CACHED:
                    this.isSaveCachedFace = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.IS_DEBUG_EMOTION:
                    this.isEmotionDebug = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.IS_DEBUG_HUMAN:
                    this.isHumanDebug = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.IS_DEBUG_GENERAL_LOG:
                    this.isGeneralDebug = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.IS_DEBUG_UNKNOWN_BATCH:
                    this.isDebugUnknownBatch = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.UNKNOWN_INSERT_BATCH_SIZE:
                    this.unknownInsertBatchSize = parseInt(config.value);
                    break;
                case ServerConfigKey.UNKNOWN_INSERT_INTERVAL:
                    this.unknownInsertInterval = parseInt(config.value);
                    break;

                case ServerConfigKey.IPARKING_CUSTOMER_SYNC_DEBUG:
                    this.iparkingCustomerSyncDebug = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.IPARKING_IDENTITY_GROUP_SYNC_DEBUG:
                    this.iparkingIdentityGroupSyncDebug = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.IPARKING_VEHICLE_SYNC_DEBUG:
                    this.iparkingVehicleSyncDebug = config.value == "true" ? true : false;
                    break;
                case ServerConfigKey.IPARKING_IDENTITY_SYNC_DEBUG:
                    this.iparkingIdentitySyncDebug = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.PERSON_COUNT_THRESHOLD_ALARM:
                    this.personCrowAlarmThreshold = parseInt(config.value);
                    break;

                case ServerConfigKey.CORE_AI_SYNC_DEBUG:
                    this.isCoreAiSyncDebug = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.IS_POPUP_ALARM_ENALBE:
                    this.isPopupAlarmEnable = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.JSON_CUSTOME_CONFIG:
                    this.jsonConfig = config.value;
                    break;

                case ServerConfigKey.DEVICE_API_SERVICE_DEBUG:
                    this.isDeviceApiServiceDebug = config.value == "true" ? true : false;
                    break;

                case ServerConfigKey.UNKNOWN_ALARM_CONFIDENCE_THRESHOLD:
                    this.unknownAlarmThreshold = parseFloat(config.value);
                    break;

                case ServerConfigKey.UNKNOWN_ALARM_DELAY_SEC:
                    this.unknownAlarmDelaySec = parseInt(config.value);
                    break;

                case ServerConfigKey.UNKNOWN_ALARM_QUALITY_THRESHOLD:
                    this.unknownQualityAlarmThreshold = parseFloat(config.value);
                    break;

                default:
                    break;
            }
        }

        this.serverConfigLastSyncTime = new Date();
    }

    //iparking integration
    public async checkIparkingActiveConfig() {
        const activeConfigs = await this.prisma.iparkingConfig.findMany({});

        for (let index = 0; index < activeConfigs.length; index++) {
            const activeConfig = activeConfigs[index];
            if (activeConfig.isActive) {
                this.cachedIparkingConfig = activeConfig;
                this.isIparkingEnabled = true;
            }
        }
    }

    public async autoCheckClusterConfig() {
        let clusters = await this.prisma.civamsCluster.findMany({});
        for (let index = 0; index < clusters.length; index++) {
            const cluster = clusters[index];
            this.cachedCivamsCluster.set(cluster.clusterId, cluster);
        }
    }

    public async loadCachedLicenseAllowed() {
        if (this.isCachedLicAllowSyncing) { return; }
        this.isCachedLicAllowSyncing = true;
        //cached access control info
        let licensePlates = await this.prisma.vehicleParkingLicenseAllowed.findMany({
            where: { dateModified: { gt: this.cachedLicenseAllowedLastSyncTime } },
            orderBy: { dateModified: 'asc' },
        });
        for (let index = 0; index < licensePlates.length; index++) {
            const licensePlate = licensePlates[index];
            this.cachedLicenseAllowed.add(licensePlate.licensePlate);
            this.cachedLicenseAllowedLastSyncTime = licensePlate.dateModified;
        }

        this.isCachedLicAllowSyncing = false;
    }

    //analytic videos
    public async loadCacheUploadVideo() {
        if (this.isCachedUploadVideoSyncing) { return; }
        this.isCachedUploadVideoSyncing = true;
        //cached access control info
        let uploadVideos = await this.prisma.uploadVideoAnalytics.findMany({
            where: { dateModified: { gt: this.lastCachedUploadVideoSyncTime } },
            select: {
                id: true,
                dateModified: true,
            },
            orderBy: { dateModified: 'asc' },
        });
        for (let index = 0; index < uploadVideos.length; index++) {
            const uploadVideo = uploadVideos[index];
            this.cachedUploadVideoIds.add(uploadVideo.id)
            this.lastCachedAiServiceSyncTime = uploadVideo.dateModified;
        }

        this.isCachedUploadVideoSyncing = false;
    }
}
