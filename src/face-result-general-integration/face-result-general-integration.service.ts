import { HttpService } from '@nestjs/axios';
import { BadRequestException, HttpException, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { FaceResult, FaceResultIntegrationConfigs, GuestFaceResult, Prisma } from '@prisma/client';
import { catchError, lastValueFrom } from 'rxjs';
import { PrismaService } from 'src/prisma/prisma.service';
import { FaceImageErrEnum } from 'src/face-image-quality/models/face-image-err.enum';
import { checkFileExt, checkFileSize } from 'src/utils/file';
import { SocialSearchRequest } from './models/social-search/social-search.request';
import { FaceResultSignal } from 'src/face-result/models/checkInOut.args';
import { FaceResultGeneralIntegrationPublisher } from './face-result-general-integration.publisher';
import { ParkingService } from 'src/parking/parking.service';
import { FaceResultGateway } from 'src/socket-gateway/face-result.gateway';
import { OrphanFaceResultGateway } from 'src/socket-gateway/orphan-face-result.gateway';
import { IbtFaceResultIntegrationService } from './ibt/ibt-integration.service';
import { TelegramFaceResultIntegrationService } from './telegram/telegram-integraion.service';
import { NotificationService } from 'src/notification/notification.service';
import { AttFaceResultIntegrationService } from './attendant/att-integration.service';
import { AcLocationFaceResultIntegrationService } from './ac-location/access-location.service';
import { CustomeHookDataTypeEnum, FaceResultIntergrationKeyEnum } from './models/enums';
import { FaceIntegrationConfigGql } from './models/gqls';
import { MbankFaceResultIntegrationService } from './mbank/mbank.service';
import { FaceIntegrationSelfCustomeModel, FaceResIntegrattionJsonConfigModel, SelfCustomeDataModel } from './models/models';
import { SSCFaceResultIntegrationService } from './ssc/ssc.service';
import { SchedulerRegistry } from '@nestjs/schedule';
import { AlarmPopupModel } from 'src/socket-gateway/models/att-alarm-popup.model';
import { AlarmPopupGateway } from 'src/socket-gateway/alarm-popup.gateway';
import { base64Util } from 'src/utils/base64';
import { join } from 'path';
import { orphanResultBackupFolder } from 'src/utils/constants';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';
import { AiServiceType } from 'src/ai-service/models/ai-service-type.enum';
import { UnknownBatchInsertModel } from 'src/face-result-orphan/models/models';
import { StringUtils } from 'src/utils/string';
import { ErrorEnum } from 'src/data/enums/error.enum';
import { DeviceGql } from 'src/device/models/device.gql';
import { DeviceType } from 'src/data/enums/device.type.enum';
var FormData = require('form-data');

@Injectable()
export class FaceResultGeneralIntegrationService implements OnModuleInit {
    private logger = new Logger(FaceResultGeneralIntegrationService.name);
    private intergrationKeys: Map<number, {
        rawConfig: FaceResultIntegrationConfigs,
        jsonConfig: FaceResIntegrattionJsonConfigModel,
    }> = new Map();
    private selfCustomeConfig: FaceIntegrationSelfCustomeModel = null;
    private cachedUnknown: {
        data: UnknownBatchInsertModel,
        isSent: boolean,
        minConf: number,
    }[] = [];
    private cachedSaveAlarmEvent: Set<string> = new Set();

    async onModuleInit() {
        await this.checkIntegration();

        const autoSendUnknownPopupJob = setInterval(this.autoSendPopup.bind(this), this.cachedGlobalConfig.unknownAlarmDelaySec * 1000);
        this.schedulerRegistry.addInterval("autoSendUnknownPopup", autoSendUnknownPopupJob);

        const autoResetTrackingIdsJob = setInterval(this.autoResetTrackingIds.bind(this), 300000);
        this.schedulerRegistry.addInterval("autoResetUnknownTrackingIds", autoResetTrackingIdsJob);
    }

    constructor(
        private httpService: HttpService,
        private prisma: PrismaService,
        private notificationService: NotificationService,
        private publisher: FaceResultGeneralIntegrationPublisher,
        private parkingService: ParkingService,
        private faceResultGateway: FaceResultGateway,
        private attIntegrationService: AttFaceResultIntegrationService,
        private orphanFaceResultGateway: OrphanFaceResultGateway,
        private ibtService: IbtFaceResultIntegrationService,
        private telegramIntegrationService: TelegramFaceResultIntegrationService,
        private acLocationIntegrationService: AcLocationFaceResultIntegrationService,
        private mbankService: MbankFaceResultIntegrationService,
        private sscService: SSCFaceResultIntegrationService,
        private alarmPopupGw: AlarmPopupGateway,
        private schedulerRegistry: SchedulerRegistry,
        private cachedGlobalConfig: CachedGlobalConfigService,
    ) { }

    async integrateFaceResult(
        faceResult: FaceResult
    ) {
        //#region send refetch
        this.faceResultGateway.sendFaceResultRefetch()
        this.publisher.sendNewResultSignal();
        //#endregion

        //#region civams integrations
        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Iparking)) {
            this.parkingService.saveFaceResult(faceResult);
        }

        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Attendant)) {
            this.attIntegrationService.attUserCheckIn(faceResult.id, faceResult.time, faceResult.userId,
                faceResult.deviceId, faceResult.srcDeviceId, faceResult.cameraId);

            this.attIntegrationService.attOnUserFaceResult(faceResult);
        }

        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Telegram)) {
            this.telegramIntegrationService.integrateFaceResult(faceResult);
        }

        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Mobile)) {
            this.notificationService.sendFaceResultNotification(
                faceResult.userId,
                // checkType === FaceResultType['Check-in'] ? "Check-in" : "Check-out",
                // checkType === FaceResultType['Check-in'] ? "Check-in" : "Check-out",
                "Chấm công",
                "Chấm công",
                360000,
                "ic_baseline_notifications_active_24",
                "#f45342",
                42,
                faceResult.time,
                "",
                faceResult.id,
            ).then().catch(er => {
                this.logger.error(er)
            });
        }
        //#endregion

        //#region 3rd API integration
        this.onFaceResultIntegration(faceResult);
        //#endregion
    }

    async integrateGuestResult(
        guestFaceResult: GuestFaceResult
    ) {
        //#region send refetch
        this.faceResultGateway.sendFaceResultRefetch()
        //#endregion

        //#region i-parking
        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Iparking)) {
            this.parkingService.saveGuestResult(guestFaceResult);
        }
        //#endregion

        //#region att
        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Attendant)) {
            this.attIntegrationService.attGuestCheckIn(guestFaceResult.id, guestFaceResult.time, guestFaceResult.guestId,
                guestFaceResult.deviceId, guestFaceResult.srcDeviceId, guestFaceResult.cameraId);

            this.attIntegrationService.attOnGuestFaceResult(guestFaceResult);
        }
        //#endregion

        //#region access 
        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.GuestAC)) {
            this.acLocationIntegrationService.accessControlOnGuestResult(guestFaceResult);
        }
        //#endregion

        // this.ibtService.integrateGuestResult(guestFaceResult);
        // this.publisher.sendNewResultSignal();
    }

    //#region integration unknown faces
    async integrateBatchUnknown(
        datas: UnknownBatchInsertModel[]
    ) {
        //#region refetch signal
        this.orphanFaceResultGateway.sendOrphanFaceResultRefetch();
        //#endregion

        for (let index = 0; index < datas.length; index++) {
            const data = datas[index];
            if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Telegram)) {
                this.telegramIntegrationService.integrateUnknown(data.deviceId, data.srcDeviceId, data.image, new Date(data.time));
            }

            //#region att
            if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Attendant)) {
                this.attIntegrationService.attOnUnknownResult(data.deviceId, data.srcDeviceId, data.image, new Date(data.time), data.orphanId, data.trackingId, data.cameraId);
            }
            //#endregion

            //#region access 
            if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.UnkownAC)) {
                this.acLocationIntegrationService.acLocationOnUnknownResult(data.deviceId, data.srcDeviceId, data.image, new Date(data.time), data.orphanId, data.trackingId, data.cameraId);
            }
            //#endregion

            //#region access 
            if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.MBank)) {
                this.mbankService.integrateUnknown(data.deviceId, data.srcDeviceId, data.image, new Date(data.time), data.orphanId, data.base64Image);
            }
            //#endregion

            //#region AllUnknown 
            if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.AllUnknown)) {
                this.onUnknownResultIntegration(data);
            }
            //#endregion   
        }
    }

    autoSendPopup() {
        const now = new Date();
        for (let index = 0; index < this.cachedUnknown.length; index++) {
            let item = this.cachedUnknown[index];
            if (!item.isSent && (new Date(item.data.time).getTime() < (new Date(now.getTime() - this.cachedGlobalConfig.unknownAlarmDelaySec * 1000)).getTime())) {
                if (item.minConf < this.cachedGlobalConfig.unknownAlarmThreshold) {
                    // console.log("send unknown popup alarm on schedule");
                    this.unknownAlarmTrigger(item.data);
                    this.cachedUnknown[index].isSent = true;
                }
            }
        }
    }

    autoResetTrackingIds() {
        this.cachedUnknown = this.cachedUnknown.filter(item => !item.isSent);
        this.cachedSaveAlarmEvent.clear();
    }

    async onUnknownResultIntegration(
        data: UnknownBatchInsertModel
    ) {
        let integraionConfig = this.intergrationKeys.get(FaceResultIntergrationKeyEnum.AllUnknown);
        if (integraionConfig.jsonConfig) {
            try {
                if (!integraionConfig) return;
                if (integraionConfig.jsonConfig.alarm_re_id_only &&
                    (!data.reId && (data.faceRecogConfident > this.cachedGlobalConfig.unknownAlarmThreshold || data.faceQuality < this.cachedGlobalConfig.unknownQualityAlarmThreshold))
                ) return;
                if (integraionConfig.jsonConfig.is_device_filter &&
                    !(
                        integraionConfig.jsonConfig.device_filter.includes(data.deviceId)
                        || integraionConfig.jsonConfig.device_filter.includes(data.srcDeviceId)
                    )) return;
            } catch {
                this.logger.error("JSON unknown integration config error: " + integraionConfig.rawConfig);
            }
        }

        let cachedUnknownIndex = this.cachedUnknown.findIndex(
            item => item.data.trackingId === data.trackingId
            //  || (item.data.reId && item.data.reId === data.reId)
        );
        let cachedUnknownItem: {
            data: UnknownBatchInsertModel;
            isSent: boolean;
            minConf: number;
        } = {
            data: data,
            isSent: false,
            minConf: data.faceRecogConfident,
        };
        if (cachedUnknownIndex !== -1) {
            cachedUnknownItem = this.cachedUnknown[cachedUnknownIndex];
        } else {
            this.cachedUnknown.push({
                data: data,
                isSent: false,
                minConf: data.faceRecogConfident,
            });

            cachedUnknownIndex = this.cachedUnknown.findIndex(
                item => item.data.trackingId === data.trackingId
                // || (item.data.reId && item.data.reId === data.reId)
            );
        }
        if (cachedUnknownItem.isSent) return;
        // if (new Date(data.time).getTime() > (new Date(cachedUnknownItem.data.time).getTime() + this.cachedGlobalConfig.unknownAlarmDelaySec * 1000)) {
        // console.log("send unknown popup alarm on event");
        if (cachedUnknownItem.minConf > data.faceRecogConfident) {
            cachedUnknownItem.minConf = data.faceRecogConfident;
            this.cachedUnknown[cachedUnknownIndex].minConf = data.faceRecogConfident;
        }
        if (cachedUnknownItem.minConf <= this.cachedGlobalConfig.unknownAlarmThreshold) {
            //save alarm event
            this.unknownAlarmTrigger(data);
            this.cachedUnknown[cachedUnknownIndex].isSent = true;
        }
        // }
    }

    async unknownAlarmTrigger(
        data: UnknownBatchInsertModel
    ) {
        let deviceId = data.srcDeviceId ?? data.deviceId;
        let body = new AlarmPopupModel({
            id: `unknown_${data.orphanId}`,
            locationName: deviceId ? (this.cachedGlobalConfig.deviceInfo.get(deviceId)?.name ?? deviceId) : deviceId,
            image: data.image.includes(".jpg") ? base64Util.encodeImage(join(orphanResultBackupFolder(), data.image)) : data.image,
            time: data.time,
            userName: "UNKNOWN",
            humanTrackId: data.humanTrackId,
            deviceId: deviceId,
            type: AiServiceType.UnknownFace,
        });
        //send popup socket
        this.alarmPopupGw.sendImagePopupAlarm(body);
        //send alarm mqtt
        this.publisher.sendMqttAlarmEvent(body);
        //save alarm event
        if (!this.cachedSaveAlarmEvent.has(data.trackingId)) {
            this.cachedSaveAlarmEvent.add(data.trackingId);
            let newEvent = await this.prisma.coreAiAlarmEvent.create({
                data: {
                    deviceId: deviceId,
                    cameraIp: data.cameraId,
                    cameraId: data.cameraId,
                    trackId: data.trackingId,
                    type: AiServiceType.UnknownFace.toString(),
                    filePath: data.image,
                    dateCreated: data.time,
                    roiId: null
                }
            })
        }
    }

    async upsertUnknownIntegrationJsonConfig(
        jsonConfig: FaceResIntegrattionJsonConfigModel
    ): Promise<FaceIntegrationConfigGql> {
        let updated = await this.prisma.faceResultIntegrationConfigs.upsert({
            where: { endpointType: FaceResultIntergrationKeyEnum.AllUnknown },
            create: {
                endpointType: FaceResultIntergrationKeyEnum.AllUnknown,
                jsonConfig: JSON.stringify(jsonConfig),
            },
            update: {
                jsonConfig: JSON.stringify(jsonConfig)
            }
        });

        await this.checkIntegration();

        return new FaceIntegrationConfigGql({ ...updated });
    }

    async configIntegrationUnknown(
        isAlarmReIdOnly: boolean,
        arlarmDelayTime: number,
        isDeviceFilter: boolean,
        deviceFilter: string[],
    ): Promise<FaceIntegrationConfigGql> {
        let currentIntegraionConfig = this.intergrationKeys.get(FaceResultIntergrationKeyEnum.AllUnknown);
        let currentJsonConfig: FaceResIntegrattionJsonConfigModel = currentIntegraionConfig?.jsonConfig ?? new FaceResIntegrattionJsonConfigModel({});

        if (isAlarmReIdOnly != null && isAlarmReIdOnly != undefined) {
            currentJsonConfig.alarm_re_id_only = isAlarmReIdOnly;
        }

        if (arlarmDelayTime != null && arlarmDelayTime != undefined) {
            currentJsonConfig.alarm_delay_time = arlarmDelayTime;
        }

        if (isDeviceFilter != null && isDeviceFilter != undefined) {
            currentJsonConfig.is_device_filter = isDeviceFilter;
        }

        if (deviceFilter != null && deviceFilter != undefined) {
            currentJsonConfig.device_filter = deviceFilter;
        }

        return this.upsertUnknownIntegrationJsonConfig(currentJsonConfig);
    }

    async addDeviceFilterIntoUnknownAlarm(
        deviceIds: string[] = []
    ): Promise<FaceIntegrationConfigGql> {
        let currentIntegraionConfig = this.intergrationKeys.get(FaceResultIntergrationKeyEnum.AllUnknown);
        let currentJsonConfig: FaceResIntegrattionJsonConfigModel = currentIntegraionConfig?.jsonConfig ?? new FaceResIntegrattionJsonConfigModel({});
        if (!currentJsonConfig.device_filter) {
            currentJsonConfig.device_filter = deviceIds;
        } else {
            currentJsonConfig.device_filter = [...new Set([...currentJsonConfig.device_filter, ...deviceIds])];
        }

        return this.upsertUnknownIntegrationJsonConfig(currentJsonConfig);
    }

    async removeDeviceFilterIntoUnknownAlarm(
        deviceIds: string[] = []
    ): Promise<FaceIntegrationConfigGql> {
        let currentIntegraionConfig = this.intergrationKeys.get(FaceResultIntergrationKeyEnum.AllUnknown);
        let currentJsonConfig: FaceResIntegrattionJsonConfigModel = currentIntegraionConfig?.jsonConfig;
        if (!currentJsonConfig) {
            throw new BadRequestException(StringUtils.generateExceptionString(ErrorEnum['ID not found']));
        };

        if (!currentJsonConfig.device_filter) {
            return new FaceIntegrationConfigGql({ ...currentIntegraionConfig.rawConfig });
        }

        currentJsonConfig.device_filter = currentJsonConfig.device_filter.filter(deviceId => !deviceIds.includes(deviceId));

        return this.upsertUnknownIntegrationJsonConfig(currentJsonConfig);
    }

    async getAllUnknownAlarmEnabledDevices(): Promise<DeviceGql[]> {
        let currentIntegraionConfig = this.intergrationKeys.get(FaceResultIntergrationKeyEnum.AllUnknown);
        let currentJsonConfig: FaceResIntegrattionJsonConfigModel = currentIntegraionConfig?.jsonConfig ?? new FaceResIntegrattionJsonConfigModel({});
        let results: DeviceGql[] = [];
        const deviceTypeAllow = [
            DeviceType.AIBox,
            DeviceType.AICamera,
            DeviceType.FaceTerminal,
            DeviceType.DisplayOnly,
        ]
        if (!currentJsonConfig.is_device_filter) {
            return Array.from(this.cachedGlobalConfig.deviceInfo.values())
                .filter(device => deviceTypeAllow.includes(device.type))
                .map(device => new DeviceGql({
                    id: device.id,
                    deviceName: device.name,
                    name: device.name,
                }));
        }
        if (!currentJsonConfig.device_filter) {
            return [];
        }

        for (let index = 0; index < currentJsonConfig.device_filter.length; index++) {
            const deviceFilter = currentJsonConfig.device_filter[index];
            let deviceInfo = this.cachedGlobalConfig.deviceInfo.get(deviceFilter);
            if (deviceInfo) {
                results.push(new DeviceGql({
                    id: deviceInfo.id,
                    deviceName: deviceInfo.name,
                    name: deviceInfo.name,
                }));
            }
        }

        return results;
    }
    //#endregion

    //#region Socail search
    async socialSearch(
        files,
        req: SocialSearchRequest
    ) {
        if (files == null) throw new BadRequestException(FaceImageErrEnum.NoImageFile);
        if (!(await checkFileSize(files, 3000000))) throw new BadRequestException(FaceImageErrEnum.LimitedFileSize);
        if (!(await checkFileExt(files, ['png', 'jpg', 'jpeg', 'jfif']))) throw new BadRequestException(FaceImageErrEnum.InvalidFileType);

        var form = new FormData();
        for (const [key, value] of Object.entries(files)) {
            switch (key) {
                case 'image':
                    // form.append("file", value[0]);
                    form.append("file", value[0].buffer, "img.jpg");
                    break;
            }
        }
        form.append("fullname", req.fullName ? req.fullName : " ");
        form.append("strict_level", req.strict_level ? req.strict_level : "0");
        form.append("top_n", req.top_n ? req.top_n : "10");
        form.append("cosine_threshold", req.cosine_threshold ? req.cosine_threshold : "0.5");

        let request = this.httpService.post("http://***********:21919/get_contact/", form,
            {
                headers: {
                    ...form.getHeaders(),
                    'Content-Type': 'multipart/form-data'
                }, timeout: 1000 * 60 * 10
            }).pipe(
                catchError(e => {
                    throw new HttpException(e.response, 500);
                }),
            );
        let resp = await lastValueFrom(request);

        return resp.data

    }
    //#endregion

    //#region face recognized signal
    async onRecogSignalReceived(
        faceResult: FaceResultSignal
    ) {
        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Telegram)) {
            this.telegramIntegrationService.onRecogSignalReceived(faceResult);
        }

        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.Mobile)) {
            this.notificationService.sendFaceResultNotification(
                faceResult.userId,
                // checkType === FaceResultType['Check-in'] ? "Check-in" : "Check-out",
                // checkType === FaceResultType['Check-in'] ? "Check-in" : "Check-out",
                "Chấm công",
                "Chấm công",
                360000,
                "ic_baseline_notifications_active_24",
                "#f45342",
                42,
                faceResult.time,
                "",
            ).then().catch(er => {
                this.logger.error(er)
            });
        }
    }
    //#endregion

    //#region configs
    async checkIntegration() {
        let configs = await this.prisma.faceResultIntegrationConfigs.findMany({
            where: { isActive: true }
        });
        for (let index = 0; index < configs.length; index++) {
            const config = configs[index];
            try {
                this.intergrationKeys.set(config.endpointType, {
                    rawConfig: config,
                    jsonConfig: config.jsonConfig ? JSON.parse(config.jsonConfig) : {}
                });
            } catch (error) {
                this.logger.error(`Invalid jsonConfig for endpointType ${config.endpointType}: ${error.message}`);
                this.intergrationKeys.set(config.endpointType, {
                    rawConfig: config,
                    jsonConfig: {}
                });
            }
            if (config.endpointType == FaceResultIntergrationKeyEnum.SelfCustomeHook) {
                this.selfCustomeConfig = new FaceIntegrationSelfCustomeModel(JSON.parse(config.description));
            }
        }
    };

    async searchFaceIntegrationConfig(
        search: string,
        page: number,
        perPage: number,
        types: number[] = []
    ): Promise<FaceIntegrationConfigGql[]> {
        let results: FaceIntegrationConfigGql[] = [];
        let filter: Prisma.FaceResultIntegrationConfigsWhereInput = {
            description: { contains: search, mode: 'insensitive' }
        }

        if (types.length > 0) {
            filter.endpointType = { in: types };
        }

        let count = await this.prisma.faceResultIntegrationConfigs.count({
            where: filter
        });

        let configs = await this.prisma.faceResultIntegrationConfigs.findMany({
            where: filter,
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            orderBy: { endpointType: 'asc' }
        });
        for (let index = 0; index < configs.length; index++) {
            const config = configs[index];
            let gql = new FaceIntegrationConfigGql({
                ...config,
                count: count
            });
            results.push(gql);
        }

        return results;
    }

    async upsertFaceIntegrationConfig(
        endpointType: number,
        description: string = "",
        isActive: boolean = true,
    ): Promise<number> {
        if (isActive) {
            let upserted = await this.prisma.faceResultIntegrationConfigs.upsert({
                where: {
                    endpointType: endpointType
                }, create: {
                    endpointType: endpointType,
                    description: description
                }, update: {
                    description: description,
                    isActive: isActive,
                }
            })

            this.checkIntegration();
            return upserted.endpointType;
        } else {
            let deleted = await this.prisma.faceResultIntegrationConfigs.delete({
                where: { endpointType: endpointType }
            });

            this.checkIntegration();
            return deleted.endpointType;
        }
    }

    async onFaceResultIntegration(
        faceResult: FaceResult

    ) {
        let queueId = 0;
        if (
            this.intergrationKeys.has(FaceResultIntergrationKeyEnum.IBT) ||
            this.intergrationKeys.has(FaceResultIntergrationKeyEnum.MBank) ||
            this.intergrationKeys.has(FaceResultIntergrationKeyEnum.SSC) ||
            this.intergrationKeys.has(FaceResultIntergrationKeyEnum.SelfCustomeHook)
        ) {
            let queue = await this.prisma.faceResultIntegrationQueue.create({
                data: {
                    faceResultId: faceResult.id,
                }
            });
            queueId = queue.id;
        }

        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.IBT)) {
            this.ibtService.integrateFaceResult(faceResult, queueId);
        }

        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.SSC)) {
            this.sscService.integrateFaceResult(faceResult.userId, faceResult.time, faceResult.srcDeviceId ? faceResult.srcDeviceId : faceResult.deviceId, queueId);
        }

        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.MBank)) {
            this.mbankService.integrateFaceResult(faceResult, queueId);
        }

        if (this.intergrationKeys.has(FaceResultIntergrationKeyEnum.SelfCustomeHook)) {
            this.onSeftCustomeHookFaceResult(faceResult, queueId);
        }
    }
    //#endregion

    //#region self custome hook
    async upsertSelfCustomeService(
        url: string,
        headers: SelfCustomeDataModel[],
        datas: SelfCustomeDataModel[],
    ) {
        let headerConfig = {};
        for (let index = 0; index < headers.length; index++) {
            const header = headers[index];
            headerConfig[header.key] = header.value;
        }

        let dataConfig = {};
        for (let index = 0; index < datas.length; index++) {
            const data = datas[index];
            dataConfig[data.key] = data.value;
        }

        let config = new FaceIntegrationSelfCustomeModel({
            url: url,
            header: headerConfig,
            dataConfig: dataConfig,
        });

        await this.prisma.faceResultIntegrationConfigs.upsert({
            where: { endpointType: FaceResultIntergrationKeyEnum.SelfCustomeHook },
            create: {
                endpointType: FaceResultIntergrationKeyEnum.SelfCustomeHook,
                description: JSON.stringify(config)
            }, update: {
                description: JSON.stringify(config)
            }
        });

        this.checkIntegration();
        return true;
    }

    async onSeftCustomeHookFaceResult(
        faceResult: FaceResult,
        queueId: number
    ) {
        if (this.selfCustomeConfig) {
            let requestBody = {};
            let dataConfigs = Object.entries(this.selfCustomeConfig.dataConfig);
            let dataConfigTypes = dataConfigs.map(([key, value]) => value);
            let deviceId = faceResult.srcDeviceId ? faceResult.srcDeviceId : faceResult.deviceId;
            let device: { name: string; deviceIntegrationKey: string; } = null;
            let user: { name: string; email: string; integrationKey: string; } = null;
            if (
                dataConfigTypes.includes(CustomeHookDataTypeEnum.DeviceName) ||
                dataConfigTypes.includes(CustomeHookDataTypeEnum.LocationName) ||
                dataConfigTypes.includes(CustomeHookDataTypeEnum.DeviceIntegrationKey)
            ) {
                device = await this.prisma.device.findFirst({
                    where: { id: deviceId },
                    select: {
                        name: true,
                        deviceIntegrationKey: true,
                    }
                });
            }
            if (
                dataConfigTypes.includes(CustomeHookDataTypeEnum.UserName) ||
                dataConfigTypes.includes(CustomeHookDataTypeEnum.UserEmail) ||
                dataConfigTypes.includes(CustomeHookDataTypeEnum.UserIntegrationKey)
            ) {
                user = await this.prisma.user.findFirst({
                    where: { id: faceResult.userId },
                    select: {
                        name: true,
                        email: true,
                        integrationKey: true,
                    }
                });
            }
            for (let index = 0; index < dataConfigs.length; index++) {
                const [key, value] = dataConfigs[index];

                switch (value) {
                    case CustomeHookDataTypeEnum.Time:
                        requestBody[key] = faceResult.time;
                        break;
                    case CustomeHookDataTypeEnum.DeviceId:
                        requestBody[key] = deviceId;
                        break;
                    case CustomeHookDataTypeEnum.DeviceName:
                        requestBody[key] = device?.name;
                        break;
                    case CustomeHookDataTypeEnum.DeviceIntegrationKey:
                        requestBody[key] = device?.deviceIntegrationKey;
                        break;
                    case CustomeHookDataTypeEnum.LocationName:
                        break;
                    case CustomeHookDataTypeEnum.UserId:
                        requestBody[key] = faceResult.userId;
                        break;
                    case CustomeHookDataTypeEnum.UserName:
                        requestBody[key] = user?.name;
                        break;
                    case CustomeHookDataTypeEnum.UserEmail:
                        requestBody[key] = user?.email;
                        break;
                    case CustomeHookDataTypeEnum.UserIntegrationKey:
                        requestBody[key] = user?.integrationKey;
                        break;
                    default:
                        let fixedVal = String(value).split('._')[1];
                        requestBody[key] = fixedVal ? fixedVal : "";
                        break
                }
            }
            if (queueId > 0) {
                await this.prisma.faceResultIntegrationResult.upsert({
                    where: {
                        queueId_endpointType: {
                            endpointType: FaceResultIntergrationKeyEnum.SelfCustomeHook,
                            queueId: queueId
                        }
                    }, create: {
                        endpointType: FaceResultIntergrationKeyEnum.SelfCustomeHook,
                        queueId: queueId,
                        bodyData: JSON.stringify(requestBody),
                        sendRequestTime: new Date(),
                        isSend: true,
                    }, update: {
                        bodyData: JSON.stringify(requestBody),
                        sendRequestTime: new Date(),
                        isSend: true,
                    }
                });
            }
            try {
                let request = this.httpService.post(this.selfCustomeConfig.url,
                    requestBody, {
                    headers: this.selfCustomeConfig.header
                }).pipe(
                    catchError(e => {
                        // this.logger.error(e.response)
                        this.prisma.faceResultIntegrationResult.upsert({
                            where: {
                                queueId_endpointType: {
                                    endpointType: FaceResultIntergrationKeyEnum.SelfCustomeHook,
                                    queueId: queueId
                                }
                            }, create: {
                                endpointType: FaceResultIntergrationKeyEnum.SelfCustomeHook,
                                queueId: queueId,
                                bodyData: JSON.stringify(requestBody),
                                isSuccess: false,
                                receiveResponseTime: new Date(),
                                // lastResponse: JSON.stringify(e.response)
                            }, update: {
                                bodyData: JSON.stringify(requestBody),
                                isSuccess: false,
                                receiveResponseTime: new Date(),
                                // lastResponse: JSON.stringify(e.response)
                            }
                        });
                        return [];
                        // throw new HttpException(e.response.data, e.response.status);
                    }),
                );
                let response = await lastValueFrom(request);
                if (response.data) {
                    this.prisma.faceResultIntegrationResult.upsert({
                        where: {
                            queueId_endpointType: {
                                endpointType: FaceResultIntergrationKeyEnum.SelfCustomeHook,
                                queueId: queueId
                            }
                        }, create: {
                            endpointType: FaceResultIntergrationKeyEnum.SelfCustomeHook,
                            queueId: queueId,
                            bodyData: JSON.stringify(requestBody),
                            isSuccess: true,
                            receiveResponseTime: new Date(),
                            lastResponse: JSON.stringify(response.data)
                        }, update: {
                            bodyData: JSON.stringify(requestBody),
                            isSuccess: true,
                            receiveResponseTime: new Date(),
                            lastResponse: JSON.stringify(response.data)
                        }
                    });
                }
            } catch (e) { }
        }
    }

    async testOnSeftCustomeHookFaceResult(
    ) {
        console.log(this.selfCustomeConfig)
        let requestBody = {};
        requestBody["email"] = "<EMAIL>";
        requestBody["meetingId"] = "81bd4637-0c49-4815-802b-4401112b0e21";
        console.log(requestBody)
        console.log(JSON.stringify(requestBody))
        let request = this.httpService.post(this.selfCustomeConfig.url,
            requestBody, {
            headers: this.selfCustomeConfig.header
        }).pipe(
            catchError(e => {
                console.log(e);
                return [];
            }),
        );

        let response = await lastValueFrom(request);
        console.log(response.data);

    }

    async testGuestAcIntegration(
        deviceId: string,
        cameraId: string,
        guestId: string,
    ) {
        this.acLocationIntegrationService.accessControlOnGuestResult({
            id: 0,
            deviceId: deviceId,
            srcDeviceId: deviceId,
            cameraId: cameraId,
            guestId: guestId,
            cameraIp: "",
            dateCreated: new Date(),
            dateModified: new Date(),
            image: "image",
            time: new Date(),
            isClusterSync: false,
            mask: "0",
            temperature: "0",
            trackingId: "",
            type: 0,
        });

    }
    //#endregion
}
