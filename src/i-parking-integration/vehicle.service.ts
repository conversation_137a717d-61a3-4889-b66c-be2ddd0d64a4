import { BadRequestException, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';
import { IparkingVehicleGql, ReportVehicleByDay, VehicleInOutGql } from './models/i-parking-vehicle.gql';
import { Prisma } from '@prisma/client';
import { IParkingIdentityService } from './identity.service';
import { IparkingApiCallerService } from './api-caller.service';
import { lastValueFrom } from 'rxjs';
import { IparkingGroupCodeEnum } from './models/enums';
import { IParkingIdentityGroupService } from './identity-group.service';
import { IParkingIdentityType } from './models/i-parking-identity-type.enums';
import { IParkingCustomerService } from './customer.service';
import { excelUtils } from 'src/utils/excel';
import * as XLSX from 'xlsx';
import { IParkingVehicleType } from './models/i-parking-vehicle-type.enums';

@Injectable()
export class IParkingVehicleService implements OnModuleInit {
    private logger = new Logger(IParkingVehicleService.name);
    private isVehicleSyning: boolean = false;

    constructor(
        private prisma: PrismaService,
        private cachedGlobalConfigService: CachedGlobalConfigService,
        private iparkingIdentityService: IParkingIdentityService,
        private apiCallerService: IparkingApiCallerService,
        private idGroupService: IParkingIdentityGroupService,
        private iParkingCustomerService: IParkingCustomerService,
    ) { }

    async onModuleInit() { }

    async upsertIparkingVehicle(
        name: string,
        plateNumber: string,
        type: number,
        customerCode: string,
        expiredDate: Date,
        lastActivatedDate: Date,
        checkInByPlate: boolean,
        checkOutByPlate: boolean
    ): Promise<IparkingVehicleGql> {

        //check customer
        let customerExists = await this.prisma.iparkingCustomer.findFirst({
            where: { code: customerCode },
        });
        if (!customerExists) {
            await this.iParkingCustomerService.syncUserToCustomer();
            await this.iParkingCustomerService.syncGuestToCustomer();
            customerExists = await this.prisma.iparkingCustomer.findFirst({
                where: { code: customerCode },
            });

            if (!customerExists) {
                return null;
                throw new BadRequestException(`Customer not found`);
            }
        }

        //upsert idenity for license plate
        let identityGroup = await this.idGroupService.getCivamsIdentityGroupIdByVehicleType(type);
        let identityId = await this.iparkingIdentityService.upsertIdentity(plateNumber, plateNumber, IParkingIdentityType.PlateNumber, identityGroup.id);

        //upsert iparking vehicle
        const vehicleData = {
            name: name,
            plateNumber: plateNumber,
            type: type,
            customerId: customerExists.id,
            expiredDate: expiredDate,
            lastActivatedDate: lastActivatedDate,
            checkInByPlate: type == IParkingVehicleType.Car ? true : false,
            checkOutByPlate: type == IParkingVehicleType.Car ? true : false,
        };

        const existingVehicle = await this.prisma.iparkingVehicle.findFirst({
            where: { plateNumber }
        });

        const vehicle = existingVehicle
            ? await this.prisma.iparkingVehicle.update({
                where: { id: existingVehicle.id },
                data: vehicleData,
            })
            : await this.prisma.iparkingVehicle.create({
                data: {
                    ...vehicleData,
                },
            });

        await this.prisma.deletedVehicle.deleteMany({
            where: { code: plateNumber }
        });

        const vehicleIdentity = await this.prisma.iparkingVehicleIdentity.upsert({
            where: {
                identityId_vehicleId: {
                    vehicleId: vehicle.id,
                    identityId: identityId,
                },
            },
            create: {
                Vehicle: { connect: { id: vehicle.id } },
                Identity: { connect: { id: identityId } },
            },
            update: {
                iparkingSyncTime: new Date(0),
            },
        });

        await this.prisma.iparkingVehicleIdentity.updateMany({
            where: { vehicleId: vehicle.id },
            data: {
                iparkingSyncTime: new Date(0),
            }
        });

        return new IparkingVehicleGql({
            id: vehicle.id,
            ...vehicleData,
        });
    }

    async getIparkingVehicle(
        search: string,
        page: number,
        perPage: number,
        customerIds: string[],
        type: number,
    ): Promise<IparkingVehicleGql[]> {
        let filter: Prisma.IparkingVehicleWhereInput = {
            OR: [
                { plateNumber: { contains: search, mode: 'insensitive' } },
                { name: { contains: search, mode: 'insensitive' } },
                {
                    IparkingCustomer: {
                        OR: [
                            { code: { contains: search, mode: 'insensitive' } },
                            { name: { contains: search, mode: 'insensitive' } }
                        ]
                    }
                }
            ],
        };

        if (customerIds.length > 0) {
            filter.customerId = { in: customerIds };
        };

        if (type) {
            filter.type = type;
        };


        let count = await this.prisma.iparkingVehicle.count({ where: filter });

        let vehicles = await this.prisma.iparkingVehicle.findMany({
            where: filter,
            skip: (page - 1) * perPage,
            take: perPage,
            orderBy: { dateCreated: 'asc' },
            include: { IparkingCustomer: true }
        });

        let results: IparkingVehicleGql[] = [];
        for (let index = 0; index < vehicles.length; index++) {
            const vehicle = vehicles[index];
            let gql = new IparkingVehicleGql({
                ...vehicle,
                Customer: vehicle.IparkingCustomer,
                count: count
            });
            results.push(gql);
        }

        return results;
    }

    async deleteIparkingVehicle(
        id: string
    ): Promise<boolean> {
        let checkVehicleExist = await this.prisma.iparkingVehicle.findFirst({
            where: {
                id: id
            }
        });

        if (!checkVehicleExist) {
            throw new BadRequestException('Vehicle not found');
        } else {
            await this.iparkingIdentityService.deleteIdentityVehicle(id);
            const searchResponse = await this.apiCallerService.searchExactlVehicle(
                this.cachedGlobalConfigService.cachedIparkingConfig,
                checkVehicleExist.name,
                checkVehicleExist.plateNumber,
                false,
                0,
                1
            );
            const searchResult = await lastValueFrom(searchResponse);
            const vehicleData = searchResult?.data?.data;
            const vehicle = vehicleData[0];
            if (vehicle) {
                const params = {
                    name: vehicle.name,
                    plateNumber: vehicle.plateNumber,
                    vehicleType: vehicle.type,
                    customerId: vehicle.customerId,
                    expireUtc: vehicle.expiredDate,
                    lastActivateUtc: vehicle.lastActivatedDate,
                    checkInByPlate: vehicle.checkInByPlate,
                    checkOutByPlate: vehicle.checkOutByPlate,
                    enabled: false,
                    identities: [],
                }

                const disableVehicleResponse = await this.apiCallerService.updateVehicle(
                    this.cachedGlobalConfigService.cachedIparkingConfig,
                    params,
                    vehicle.id
                );
                const disableVehicleResult = await lastValueFrom(disableVehicleResponse);
            }

            await this.prisma.iparkingVehicle.delete({
                where: {
                    id: id
                }
            });

            await this.prisma.deletedVehicle.upsert({
                where: { code: checkVehicleExist.plateNumber },
                create: {
                    code: checkVehicleExist.plateNumber,
                    iparkingSyncTime: null
                },
                update: { iparkingSyncTime: null }
            });
            return true;
        }
    }

    async syncVehicles() {
        if (!this.cachedGlobalConfigService.isIparkingEnabled) return;
        if (this.isVehicleSyning) return;

        this.isVehicleSyning = true;

        let tmpRequestTime = new Date();
        const vehicles = await this.prisma.$queryRaw<IparkingVehicleGql[]>(Prisma.sql`
            SELECT "IparkingVehicle".id, "IparkingVehicle".name, "IparkingVehicle"."plateNumber", 
                   "IparkingVehicle".type, "IparkingVehicle"."customerId", "IparkingVehicle"."expiredDate", 
                   "IparkingVehicle"."lastActivatedDate", "IparkingVehicle"."checkInByPlate", "IparkingVehicle"."checkOutByPlate",
                   "IparkingVehicle"."iparkingSyncTime", "IparkingVehicle"."dateCreated", "IparkingVehicle"."dateModified"
            FROM "IparkingVehicle"
            WHERE "iparkingSyncTime" IS NULL OR "iparkingSyncTime" < "dateModified"
            `);
        for (const vehicle of vehicles) {
            if (this.cachedGlobalConfigService.iparkingVehicleSyncDebug) {
                this.logger.log(vehicle);
            }
            try {
                //get iparking vehicle ID
                const searchVehicleResponse = await this.apiCallerService.searchExactlVehicle(
                    this.cachedGlobalConfigService.cachedIparkingConfig,
                    vehicle.name,
                    vehicle.plateNumber,
                    false,
                    0,
                    1
                );
                const searchVehicleResult = await lastValueFrom(searchVehicleResponse);
                let searchPlateNumberId: string = searchVehicleResult?.data?.data[0]?.id;
                let searchPlateNumberPlateNumber: string = searchVehicleResult?.data?.data[0]?.plateNumber;

                //get iparking customer ID
                const searchCustomerResponse = await this.apiCallerService.searchExactlCustomer(
                    this.cachedGlobalConfigService.cachedIparkingConfig,
                    vehicle.customerId,
                    false,
                    0,
                    1
                );
                const searchCustomerResult = await lastValueFrom(searchCustomerResponse);
                let searchCustomerId: string = searchCustomerResult?.data?.data[0]?.id;

                const vehicleData = {
                    name: vehicle.name,
                    plateNumber: vehicle.plateNumber,
                    vehicleType: vehicle.type,
                    customerId: searchCustomerId,
                    expireUtc: vehicle.expiredDate,
                    lastActivateUtc: vehicle.lastActivatedDate,
                    checkInByPlate: vehicle.checkInByPlate,
                    checkOutByPlate: vehicle.checkOutByPlate,
                    enabled: true,
                    identities: [],
                };

                let upsertedVehicleId: string = null;
                if (searchPlateNumberPlateNumber === vehicle.plateNumber) {
                    if (this.cachedGlobalConfigService.iparkingVehicleSyncDebug) {
                        this.logger.log(`Update Vehicle : ${vehicleData.name}_${vehicleData.plateNumber}`);
                    }
                    let updateResponse = await this.apiCallerService.updateVehicle(
                        this.cachedGlobalConfigService.cachedIparkingConfig,
                        vehicleData,
                        searchPlateNumberId
                    );

                    const updateResult = await lastValueFrom(updateResponse);
                    upsertedVehicleId = updateResult?.data?.id;
                } else {
                    if (this.cachedGlobalConfigService.iparkingVehicleSyncDebug) {
                        this.logger.log(`Create Vehicle : ${vehicleData.name}_${vehicleData.plateNumber}`);
                    }
                    let createResponse = await this.apiCallerService.createVehicle(
                        this.cachedGlobalConfigService.cachedIparkingConfig,
                        vehicleData
                    );

                    const createResult = await lastValueFrom(createResponse);
                    upsertedVehicleId = createResult?.data?.id;
                }

                if (this.cachedGlobalConfigService.iparkingVehicleSyncDebug) {
                    this.logger.log(`Upserted Vehicle Id: ${upsertedVehicleId}`);
                }
                // update map vehicle - identity
                await this.prisma.iparkingVehicleIdentity.updateMany({
                    where: { vehicleId: vehicle.id },
                    data: {
                        iparkingSyncTime: new Date(0),
                    }
                });
                //update time sync group
                if (upsertedVehicleId) {
                    await this.prisma.iparkingVehicle.update({
                        where: { id: vehicle.id },
                        data: {
                            iparkingSyncTime: tmpRequestTime,
                            dateModified: tmpRequestTime
                        }
                    });

                    //find identity group id
                    const idGroupSearchText = vehicle.type === 0 ? IparkingGroupCodeEnum.car : vehicle.type === 1 ? IparkingGroupCodeEnum.motobike : IparkingGroupCodeEnum.bike;
                    let civamsIdGroup = await this.prisma.iparkingGroup.findFirst({
                        where: { code: idGroupSearchText }
                    });
                    // const searchIdentityGroup = await this.apiCallerService.searchGroup(
                    //     this.cachedGlobalConfigService.cachedIparkingConfig,
                    //     idGroupSearchText,
                    //     false,
                    //     0,
                    //     1
                    // );
                    // const searchResultIdentityGroup = await lastValueFrom(searchIdentityGroup);
                    // console.log(searchResultIdentityGroup?.data?.data)
                    // const identityGroupId = searchResultIdentityGroup?.data?.data[0]?.id;
                    // if (this.cachedGlobalConfigService.iparkingVehicleSyncDebug) {
                    //     this.logger.log(`Upsert PlateNumber Identity for : ${upsertedVehicleId}`);
                    // }
                    await this.iparkingIdentityService.upsertIdentity(vehicle.plateNumber, vehicle.plateNumber, IParkingIdentityType.PlateNumber, civamsIdGroup.id);
                }
            } catch (error) {
                if (this.cachedGlobalConfigService.iparkingVehicleSyncDebug) {
                    this.logger.error(error);
                }
            }
        }
        this.isVehicleSyning = false;
    }

    async countVehicleInParking(
        startTime: Date,
        endTime: Date,
    ): Promise<ReportVehicleByDay[]> {

        let searchResponse = await this.apiCallerService.countVehicleInParking(
            this.cachedGlobalConfigService.cachedIparkingConfig,
            startTime,
            endTime,
            false,
            0,
            1
        );

        const searchResult = await lastValueFrom(searchResponse);
        const data = searchResult?.data?.countByVehicleType;

        let results: ReportVehicleByDay[] = [];
        for (let index = 0; index < data?.length; index++) {
            const countByType = data[index];
            let gql = new ReportVehicleByDay({
                day: startTime,
                countByVehicleType: countByType
            });
            results.push(gql);
        }

        return results;
    }

    async getVehicleInOut(
        startTime: Date,
        endTime: Date,
    ): Promise<VehicleInOutGql[]> {
        let searchVehicleInResponse = await this.apiCallerService.getVehicleInOut(
            this.cachedGlobalConfigService.cachedIparkingConfig,
            startTime,
            endTime,
            false,
            0,
            1
        );

        const searchVehicleInResult = await lastValueFrom(searchVehicleInResponse);
        const data = searchVehicleInResult?.data?.data;
        const count = searchVehicleInResult?.data?.totalCount;

        let results: VehicleInOutGql[] = [];
        for (let index = 0; index < data?.length; index++) {
            const vehicleInOut = data[index];
            let gql = new VehicleInOutGql({
                ...vehicleInOut,
                count: count
            });
            results.push(gql);
        }

        return results;
    }

    async importVehicle(
        file: Express.Multer.File,
    ) {
        //Read Excel file
        let wb = excelUtils.readExcelFile(file)
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const data: {
            name: string,
            vehicle: string,
            plate: string,
            depId: string,
        }[] = XLSX.utils.sheet_to_json(ws);
        let validRows: {
            code: string,
            name: string,
            vehicle: string,
            plate: string,
            depId: string,
        }[] = [];
        let errRows = [];
        for (let index = 0; index < data.length; index++) {
            const row = data[index];
            let isUserFound = false;
            console.log(row)
            for (let [key, value] of this.cachedGlobalConfigService.cachedAllUser) {
                if (
                    value.name == row.name.toUpperCase()
                    && value.Department.id == row.depId.trim().toLowerCase()
                ) {
                    const sanitizedPlate = row.plate.replace(/[^a-zA-Z0-9]/gi, '');
                    validRows.push({
                        code: key,
                        name: value.name,
                        vehicle: row.vehicle,
                        plate: sanitizedPlate.toUpperCase(),
                        depId: value.Department.id,
                    });
                    isUserFound = true;
                    break;
                }
            }
            if (!isUserFound) {
                errRows.push(row);
            }
        }

        for (let row of validRows) {
            let vehicleType = row.vehicle.includes("mô tô") ? IParkingVehicleType.Motorbike : IParkingVehicleType.Car;
            let upserted = await this.upsertIparkingVehicle(
                row.name,
                row.plate,
                row.vehicle.includes("mô tô") ? IParkingVehicleType.Motorbike : IParkingVehicleType.Car,
                row.code,
                new Date('2099-12-31'),
                new Date(),
                vehicleType == IParkingVehicleType.Car ? true : false,
                vehicleType == IParkingVehicleType.Car ? true : false
            );
            if (!upserted) {
                errRows.push(row);
            }
        }

        this.logger.error(`Vehicle import err rows: ${JSON.stringify(errRows)}`);

        return {
            errors: errRows
        };
    }
}