# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type ACAdminGql {
  avatar: String
  locationId: String!
  userId: String!
  userName: String
}

type ACDepTimeGql {
  Company: CompanyGql
  Department: DepartmentGql
  acTimes: [AcTimeGql!]
  count: Int
  departmentId: String!
  departmentName: String
  id: String
  locationId: String!
  locationName: String
  status: String
}

type ACDeviceGql {
  count: Float
  dateCreated: Date!
  deviceId: String!
  deviceName: String
  deviceType: Float
  locationId: String!
}

input ACGuestInput {
  guestId: String
  locationId: String
  timeId: String
}

type ACLocationGql {
  AcTimes: [AcTimeGql!]
  count: Int
  dateCreated: Date!
  dateModified: Date!
  id: String!
  isAlwayOpen: Boolean
  name: String!
  status: String
}

input ACPatientInput {
  locationId: String
  patientId: String
  timeId: String
}

type AcCameraGql {
  Camera: CameraGql!
  acLocationId: String!
  count: Int
}

type AcGuestTimeGql {
  acTime: AcTimeGql!
  count: Int
  guestId: String!
  guestName: String
  locationId: String!
  locationName: String
}

type AcPatientTimeGql {
  acTime: AcTimeGql
  count: Int
  id: String!
  locationId: String!
  locationName: String
  patientId: String
  patientName: String
}

type AcTimeGql {
  endDate: Date!
  endTime: TimeOfDay!
  id: String!
  name: String!
  startDate: Date!
  startTime: TimeOfDay!
  status: String
  weekDays: [Float!]!
}

type AcUserTimeGql {
  acTimes: [AcTimeGql!]!
  avatarBase64: AvatarBase64GQL
  count: Int
  locationId: String!
  locationName: String
  userId: String!
  userName: String
}

type AccessControlCameraGql {
  Camera: CameraGql
  accessControlId: String!
  cameraId: String!
  count: Float
  dateCreated: Date
  dateModified: Date
  isEnable: Boolean!
}

type AccessControlCompanyGql {
  Company: CompanyGql
  accessControlId: String!
  companyId: String!
  count: Float
  dateCreated: Date
  dateModified: Date
  isEnable: Boolean!
}

type AccessControlDepartmentGql {
  Department: DepartmentGql
  accessControlId: String!
  count: Float
  dateCreated: Date
  dateModified: Date
  departmentId: String!
  isEnable: Boolean!
}

type AccessControlDeviceGql {
  Device: DeviceGql
  accessControlId: String!
  count: Float
  dateCreated: Date
  dateModified: Date
  deviceId: String!
  inOutMode: Float
  isEnable: Boolean!
}

type AccessControlDeviceGroupGql {
  DeviceGroupInfo: DeviceGroupInfoGql
  accessControlId: String!
  count: Float
  dateCreated: Date
  dateModified: Date
  deviceGroupInfoId: String!
  isEnable: Boolean!
}

type AccessControlGql {
  AcTimes: [AccessControlTimeGql!]
  BindingCompany: CompanyGql
  BindingDepartment: DepartmentGql
  BindingOrg: OrganizationGQL
  acTimeCount: Float
  bindingCompanyId: String
  bindingDepartmentId: String
  bindingOrgId: String
  cameraCount: Float
  comCount: Float
  count: Float
  dateCreated: Date
  dateModified: Date
  depCount: Float
  deviceCount: Float
  deviceGroupCount: Float
  doorHoldTime: Int
  id: String!
  isAcValid: Boolean
  isDoorOpen: Boolean
  isUseAdvance: Boolean
  multipleUserWaitTime: Int
  name: String!
  orgCount: Float
  totalIn: Float
  totalOut: Float
  userCount: Float
  userNumberRequired: Int
  userWaittingCount: Float
}

type AccessControlInfoModalGql {
  AccessUserList: [AccessControlUserGql!]
  FirstUser: AccessControlUserGql
  SecondUser: AccessControlUserGql
  ThirdUser: AccessControlUserGql
}

type AccessControlOpenLogGql {
  AcLocations: [ACLocationGql!]
  InvalidUsers: [UserGql!]
  MissingUsers: [UserGql!]
  OpenDevice: DeviceGql
  OpenUsers: [UserGql!]
  ScanUsers: [UserGql!]
  count: Float
  invalidUserIds: [String!]
  isOpen: Boolean!
  localId: String!
  missingUserIds: [String!]
  openDeviceId: String!
  openUserIds: [String!]
  scanUserIds: [String!]
  time: Date
  totalIn: Float
  totalOut: Float
  type: Float
}

type AccessControlOrgGql {
  Org: OrganizationGQL
  accessControlId: String!
  count: Float
  dateCreated: Date
  dateModified: Date
  isEnable: Boolean!
  orgId: String!
}

type AccessControlTimeGql {
  accessControlId: String!
  count: Float
  dateCreated: Date
  dateModified: Date
  endDate: Date!
  endTime: String!
  id: String!
  isEnable: Boolean!
  name: String!
  startDate: Date!
  startTime: String!
  weekDays: [Float!]!
}

type AccessControlUserGql {
  AccessControl: AccessControlGql
  Replacer: UserGql
  SystemLog: SystemLogGql
  User: UserGql
  accessControlId: String!
  actionType: Int
  count: Float
  dateCreated: Date
  dateModified: Date
  endTime: Date
  invalidType: Int
  isEnable: Boolean!
  isSentAlert: Boolean
  replaceUserId: String
  startTime: Date
  userId: String!
  userRank: Int
}

type AccessControlUserLogGql {
  FaceResult: FaceResultGql
  acId: String
  checkType: Int
  count: Int
  faceResultId: Int
  id: String!
  invalidType: Int
  isValid: Boolean
  time: Date!
  userId: String
}

type AdminInfoGql {
  avatar: String
  birthday: Date!
  companyId: String
  dateCreated: Date
  dateModified: Date
  departmentId: String
  email: String!
  gender: Gender!
  id: String!
  integrationKey: String
  name: String!
  phone: String!
}

type AgeReportGql {
  Device: DeviceGql
  count: Float
  countSameAge: Float!
  deviceId: String!
  endTime: Date!
  startTime: Date!
  step: Float!
}

type AiBoxConfigGql {
  Device: DeviceGql
  DeviceRois: [DeviceRoiGql!]
  cameraFps: Int
  cameraIp: String
  cameraPass: String
  cameraRtsp: String
  cameraUser: String
  count: Int
  dateCreated: Date!
  dateModified: Date!
  deviceId: String!
  deviceJsonConfig: String
  doorHoldTime: Int!
  lastSyncTime: Date!
}

type AiServiceConfigGql {
  aiServiceId: String
  configType: Float!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  isActive: Boolean
  value: String
}

input AiServiceConfigInput {
  aiServiceId: String
  configType: Int!
  value: String!
}

type AiServiceCountGql {
  count: Float!
  type: Float!
}

type AiServiceGql {
  AiServiceConfigs: [AiServiceConfigGql!]
  CameraRois: [CamRoiGql!]
  Cameras: [CameraGql!]
  alertDelaySecs: Float
  cameraCount: Int
  configCount: Int
  count: Int
  dateCreated: Date!
  dateModified: Date!
  dropFps: Float!
  endTime: String
  id: String!
  isActive: Boolean!
  isPopupAlert: Boolean
  isTimeLimit: Boolean
  name: String!
  roiCount: Int
  startTime: String
  type: Float!
}

type AiServiceInDeviceGql {
  AiServices: [AiServiceCountGql!]!
  deviceId: String!
  totalService: Float!
}

type AlarmEventSumaryGQL {
  count: Int
  type: String
}

type AlertMessageGql {
  AcOpenLog: AccessControlOpenLogGql
  AccessControl: AccessControlGql
  AccessControlUser: AccessControlUserGql
  AccessControlUserLog: AccessControlUserLogGql
  Unknown: UnknownFaceResultGql
  UserRegis: UserGql
  acDeviceIds: [String!]
  acUserId: String
  accessControlId: String
  accessControlOpenLogLocalId: String
  accessControlOpenLogOpenDeviceId: String
  alertRuleId: String!
  cachedImagePath: String
  cachedRelationRawData: String
  count: Int
  deviceId: String
  faceResultId: Int
  id: Float!
  isRead: Boolean!
  messeage: String!
  time: Date!
  userId: String
}

type AlertRuleGql {
  alertLevel: Int!
  count: Int
  description: String!
  endDate: Date
  endHour: String
  id: String!
  isRangeLimit: Boolean!
  isTimeLimit: Boolean!
  name: String!
  startDate: Date
  startHour: String
  type: Int!
  weekDays: String
}

type AlertScriptGql {
  User: UserGql
  alertRuleId: String!
  count: Int
  receiverEmail: String
  receiverId: String
  type: Int!
}

type AttDeviceGql {
  Camera: CameraGql
  Device: DeviceGql
  attInfoId: String!
  cameraId: String
  count: Int
  deviceId: String
  id: String!
}

type AttGuestGql {
  FaceResult: FaceResultGql
  Guest: UserGuestGql
  attInfoId: String!
  count: Int
  guestId: String!
  isCheck: Boolean!
}

type AttInfoGql {
  count: Int
  endTime: Date!
  id: String!
  name: String!
  startTime: Date!
}

type AttUserGql {
  FaceResult: FaceResultGql
  User: UserGql
  attInfoId: String!
  count: Int
  isCheck: Boolean!
  userId: String!
}

type AttandantExplainGql {
  dateCreated: Date!
  dateModified: Date!
  detail: String!
  id: Float!
  status: Float!
  time: Date
  type: Float!
  userId: String!
}

type AttandantTimeGql {
  count: Int
  deviceCount: Int
  devices: [DeviceGql!]!
  endHour: String!
  id: String!
  isActive: Boolean!
  isNoTimeLimit: Boolean!
  isNoUserLimit: Boolean!
  name: String!
  startHour: String!
  timezoneOffset: String!
}

type AttendantReport {
  companyId: String!
  departmentId: String!
  message: String!
  name: String!
  title: String!
  userId: String!
}

type AudioAnalyticResultGql {
  confidentScore: Float
  content: String
  count: Int
  emotion: String
  end: Float!
  humanReadableEnd: String
  humanReadableStart: String
  listEnd: [Float!]
  listStart: [Float!]
  meanVolume: Float
  sectionNumber: Int!
  speakerId: String
  start: Float!
  uploadAudioId: String!
}

type AvatarBase64GQL {
  data: String
  path: String!
}

type BackupJobGql {
  backupFilePath: String!
  backupTimeStamp: Date!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  fileType: String
  id: String!
  isClusterSync: String!
  isRunning: String!
  processedRow: Int
  tableName: String!
  totalRow: Int
}

type BidvDashboardGql {
  Location: LocationGQL
  count: Int
  locationId: String!
  totalGuest: Int!
  totalNoUniform: Int!
  totalUniform: Int!
  totalUnknown: Int!
  totalUser: Int!
}

type BidvUserMovementHistoryGql {
  Device: DeviceGql!
  Location: LocationGQL
  count: Int
  deviceId: String!
  image: String!
  locationId: String
  time: Date!
}

type CachedImageResultGql {
  cameraId: String
  count: Int
  dateCreated: Date
  dateModified: Date
  deviceId: String!
  id: String!
  image: String!
  isCoreAiResponse: Boolean!
  isCoreAiSent: Boolean!
  time: Date!
  trackingId: String!
}

type CalculatedWorkMonthGql {
  cardViolationCount: Float!
  congCheDo: Float!
  congPhep: Float!
  congThucTe: Float!
  count: Float
  cycleDate: String!
  days: String!
  month: Float!
  phatTien: Float!
  tongCong: Float!
  totalViolationCount: Float!
  uniformViolationCount: Float!
  userId: String!
  year: Float!
}

type CamRoiGql {
  Camera: CameraGql
  aiServiceId: String!
  cameraId: String!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  id: String!
  isActive: Boolean!
  name: String!
  xs: String!
  ys: Float!
}

type CameraGql {
  cameraType: Float
  count: Int
  dateCreated: Date
  dateModified: Date
  distanceMin: String
  ezPrivateStreamPass: String
  ezPrivateStreamUrl: String
  ezPrivateStreamUserName: String
  ezPublicStreamPass: String
  ezPublicStreamUrl: String
  ezPublicStreamUserName: String
  ezState: Float
  ezStreamMode: Float
  faceService: String
  frameRate: String
  id: String!
  inputCodec: String
  ip: String
  isActive: Boolean!
  isEzCam: Boolean!
  name: String
  nxFirmware: String
  nxIp: String
  nxLogicalId: String
  nxModel: String
  nxName: String
  nxShareId: String
  nxSystemId: String
  nxUrl: String
  outputCodec: String
  personService: String
  recordDuration: String
  rtsp: String
  snapShot: String
  streamMode: String
  subRtsp: String
}

type CameraROIGql {
  AiServices: [AiServiceGql!]
  Camera: CameraGql
  Device: DeviceGql
  cameraId: String!
  count: Int
  dateCreated: Date
  dateModified: Date
  deviceId: String!
  id: String!
  isActive: Boolean!
  name: String!
  xs: String!
  ys: String!
}

type CameraSnapshotGql {
  cameraId: String!
  dateCreated: Date
  dateModified: Date
  deviceId: String!
  faceMinHeight: Float!
  faceMinWidth: Float!
  faceRoiEnable: DeviceCameraStatus!
  faceRoiHeight: Float!
  faceRoiWidth: Float!
  faceRoiX: Float!
  faceRoiY: Float!
  faceZoomEnable: DeviceCameraStatus!
  faceZoomMaxHeight: Float!
  faceZoomMaxWidth: Float!
  frame: String!
  id: Int!
}

type CheckAdUserGQL {
  adName: String!
  isActive: Boolean!
  userId: String!
}

type CheckInOutResultSql {
  day: Date!
  isAfternoon: Boolean!
  isMorning: Boolean!
  userId: String!
}

type CheckLastOutGql {
  isCheckIn: Boolean!
  isCheckOut: Boolean!
  userId: String!
}

type ComRemoteLimitGQL {
  Company: CompanyGql
  companyId: String!
  count: Float!
  dateLimit: Date
  limitTimes: Float
}

type CompanyExportGql {
  name: String!
  oldId: Float!
  uniqueId: String!
}

type CompanyGql {
  companyType: Int
  count: Float
  id: String!
  name: String!
  sortKey: String
}

type CompanyOwnerGql {
  companyId: String!
  ownerId: String!
}

type CoreAiAlarmEventGql {
  Camera: CameraGql
  base64Image: String
  cameraId: String!
  cameraIp: String!
  count: Float
  dateCreated: Date!
  deviceId: String!
  filePath: String
  id: String!
  trackId: String!
  type: String!
}

type CoreAiQueueInfoGql {
  detect_queue: Float!
  feature_queue: Float!
  headpose_queue: Float!
  mask_queue: Float!
  quality_queue: Float!
  request_queue: Float!
  search_queue: Float!
}

type CountByVehicleTypeGql {
  count: Int!
  vehicleType: Int!
}

type CronJobMonitorGql {
  _idleStart: Float
  _repeat: Float
  _timerArgs: Float
  count: Float
  lastDate: Date
  name: String!
  nextDate: Date
  running: Boolean
}

type CtelHubSyncResultGQL {
  countLockUsers: Float!
  countNewUsers: Float!
  countSkipUsers: Float!
  countUpdateUsers: Float!
  errLogs: [String!]
  importedLog: Float
  skipOrg: Float
  totalLog: Float
  totalOrg: Float
  totalUser: Float!
  upsertedOrg: Float
}

type CustomeReportJobGql {
  companyIds: String
  dateCreated: Date
  dateModified: Date
  departmentIds: String
  id: String!
  isClusterSync: Boolean
  isRunning: Boolean
  processedUser: Float
  reportFileName: String!
  totalUser: Float
  userIds: String
}

type DataImportJobGql {
  count: Int
  dateCreated: Date!
  dateModified: Date!
  errRowsNumber: Int
  id: String!
  importFileName: String
  importerId: String
  isOverwrite: Boolean
  jobType: Int!
  processedRowNumber: Int
  totalRowNumber: Int
}

type DataImportRowGql {
  count: Int
  dataImportJobId: String!
  dateCreated: Date!
  dateModified: Date!
  errs: String
  id: String!
  isOverwrite: Boolean
  processTime: Date
  processed: Boolean!
  rawData: String!
  resultType: Int
}

"""Date custom scalar type"""
scalar Date

type DayInOutTimeGql {
  acNo: String
  checkInTimes: Float
  checkOutTimes: Float
  clockIn: String
  clockOut: String
  day: String!
  depName: String
  early: String
  integrationKey: String
  late: String
  maxTime: String
  minTime: String
  offDuty: String
  onDuty: String
  shiftName: String
  undefinedTimes: Float
  userId: String!
}

type DayInvalidReasonGql {
  count: Float
  day: String!
  note: String
  reasonType: Float!
  type: Float!
  userId: String!
}

type DefaultNotAssignedGql {
  endDate: Date!
  shiftId: String!
  startDate: Date!
  userId: String!
}

type DepRemoteLimitGQL {
  Department: DepartmentGql
  count: Float!
  dateLimit: Date
  departmentId: String!
  limitTimes: Float
}

type DepartmentAdminGql {
  adminId: String!
  company: String!
  count: Int
  department: String!
  departmentId: String!
  status: String!
  user: AdminInfoGql!
}

type DepartmentExportGql {
  companyUniqueId: String!
  name: String!
  oldCompanyId: Float
  oldId: Float!
  parentId: Float
  uniqueId: String!
}

type DepartmentGql {
  company: CompanyGql
  count: Float
  id: String!
  name: String!
  numEmployees: Float
  parentId: String
}

type DeviceAttandantTimeGql {
  attandantTimeId: String!
  count: Int
  device: DeviceGql!
  deviceId: String!
  isActive: Boolean!
}

type DeviceCameraGql {
  Camera: CameraGql!
  cameraId: String!
  cameraIp: String
  cameraName: String
  cameraRtsp: String
  count: Int
  dateCreated: Date
  dateModified: Date
  deviceId: String!
  deviceName: String
  deviceType: DeviceType
  isDisplay: Boolean!
}

enum DeviceCameraStatus {
  Disable
  Enable
}

input DeviceCustomeConfigGql {
  displayFps: Int = 10
  showFaceBoundingBox: Boolean = true
  showFaceTrackingNumber: Boolean = true
  showHumanBoundingBox: Boolean = true
  showHumanTrackingLine: Boolean = true
  showHumanTrackingNumber: Boolean = true
}

type DeviceDetectorConfigStatusGql {
  alignMode: String!
  bBoxMargin: String!
  deviceId: String!
  faceBestShotDuration: String!
  faceBestShotEnable: String!
  faceConfident: String!
  fakeFaceEnable: String!
  fasConfident: String!
  fasValidationCount: String!
  kptConfident: String!
  maxSize: String!
  namedSentDuration: String!
  personBestShotDuration: String!
  personDetectConfident: String!
  serverUrl: String!
  timeout: String!
}

type DeviceGql {
  Cameras: [DeviceCameraGql!]
  coreAiUuid: String
  count: Float
  cpuUsage: String
  dateCreated: Date
  dateModified: Date
  deviceName: String!
  deviceUserSyned: Float
  faceCheckType: Float
  freeDisk: String
  id: String!
  ip: String!
  isAleart: Boolean
  isAuthenticated: Boolean
  isDefaultDevice: Boolean
  isFaceResultEditable: Boolean
  isFirmwareAutoUpdate: Boolean
  isTimekeepingDevice: Boolean
  lastTimeSignal: Date
  lastUpdatedTime: Date!
  licenseDeviceId: String
  licenseExpireDate: String
  licenseNumberUser: Int
  macAddress: String
  memFree: String
  memTotal: String
  name: String!
  relayCurrentMode: Float
  runStatus: [DeviceRunStatusGql!]
  status: String
  statusUpdate: String!
  totalDeviceUser: Float
  totalUser: Float
  type: String!
  uptime: String
  userSyncProgress: Float
  userSyned: Float
}

type DeviceGroupInfoGql {
  Devices: [DeviceGql!]
  count: Int
  id: String!
  name: String!
  piority: Float
  totalDevice: Float
}

type DeviceLocationGql {
  Location: LocationGQL
  count: Float
  deviceId: String!
  devices: DeviceGql!
  locationId: String!
}

type DeviceRoiGql {
  AiServices: [AiServiceGql!]
  Device: DeviceGql
  count: Int
  dateCreated: Date
  dateModified: Date
  deviceId: String!
  id: String!
  isActive: Boolean
  name: String!
  xs: String!
  ys: String!
}

type DeviceRunStatusGql {
  current_vol: Float!
  detect_queue: Float!
  deviceId: String!
  feature_queue: Float!
  headpose_queue: Float!
  isAuthen: Boolean!
  isClusterSync: Boolean
  lived: Float!
  mask_queue: Float!
  maxAge: Float!
  quality_queue: Float!
  request_queue: Float!
  resetAuthenCount: Float!
  runningStatus: Float!
  search_queue: Float!
  serialNumber: String!
  time: Date!
  versionCode: Float!
  versionName: String!
  vol: Float!
}

type DeviceSignalLogGql {
  Device: DeviceGql
  count: Int
  deviceId: String!
  id: Float!
  isOnline: Boolean!
  time: Date!
}

type DeviceStreamLogGql {
  cameraIp: String
  dateCreated: Date!
  deviceId: String
  flows: Float!
  id: String!
  status: NumberStatus!
}

type DeviceSyncLogGql {
  User: UserGql
  count: Int
  dateCreated: Date!
  dateModified: Date!
  dbId: String
  deviceId: String!
  id: String!
  isSuccess: Boolean!
  logType: String!
  status: String
  syncLogDetail: String
  userId: String
  userName: String
  userType: String
  videoAnalyticId: String
}

enum DeviceType {
  AIBox
  AICamera
  AI_FACE_SEARCH_SERVER
  AI_HUMAN_SERVER
  AI_VEHICLE_SERVER
  CloudCamera
  DisplayOnly
  DisplayOnlyWpf
  FaceTerminal
  FcardTerminal
  FlowDetectOnly
  FullFunctions
  Robot
  ServerRecogChannel
  Unidentified
  UpdaterAndMatcherOnly
  VMS
  WPFFaceRecog
  WPFFaceTerminal
}

type DeviceType4ConfigGql {
  camIp: String!
  camRtsp: String!
  dateCreated: Date!
  dateModified: Date!
  deviceId: String!
  httpApi: String!
  httpBrokerIp: String!
  httpBrokerPort: Float!
  isDoorAccess: Boolean!
  isPlaySound: Boolean!
  language: String!
  lastSyncTime: Date!
  serverIp: String!
  serverPort: Float!
}

type DeviceUserGql {
  avatar: String
  companyId: String
  companyName: String
  count: Int
  dateCreated: Date
  dateModified: Date
  departmentId: String
  departmentName: String
  deviceCallSyncTime: Date
  deviceId: String!
  deviceSynedTime: Date
  isAdmin: Boolean!
  isDeviceSyned: Boolean!
  status: String!
  userId: String!
  userName: String
}

type EkycIntegrationGql {
  count: Int
  id: String!
  name: String!
  token: String!
}

type EkycIntegrationUserGql {
  User: UserGql
  count: Int
  ekycUrl: String!
  token: String!
  userId: String!
}

type EmotionReportGql {
  Device: DeviceGql
  avgNegative: Float!
  avgNeutral: Float!
  avgPositive: Float!
  count: Float
  deviceId: String!
  endTime: Date!
  negativeGt20Le40: Float!
  negativeGt40Le60: Float!
  negativeGt60Le80: Float!
  negativeGt80: Float!
  negativeLe20: Float!
  neutralGt20Le40: Float!
  neutralGt40Le60: Float!
  neutralGt60Le80: Float!
  neutralGt80: Float!
  neutralLe20: Float!
  positiveGt20Le40: Float!
  positiveGt40Le60: Float!
  positiveGt60Le80: Float!
  positiveGt80: Float!
  positiveLe20: Float!
  startTime: Date!
}

type EmployeeDataGql {
  avatar: String
  cameraIp: String
  company: String
  dateCreated: Date
  dateModified: Date
  day: Date
  department: String
  deviceId: String
  earlyTime: Int
  email: String
  fullName: String
  id: String
  image: String
  integrationKey: String
  isCheckedIn: Boolean
  isMaxTimeValid: Boolean
  isMinTimeValid: Boolean
  lastTime: Date
  lateTime: Int
  maxTime: Date
  minTime: Date
  textNote: String
  time: Date
  type: Int
  userId: String
  workTime: Int
}

type EventVideoAiBoxGql {
  cameraId: String
  cameraIp: String
  count: Float
  deviceId: String!
  duration: Float!
  endTime: Date!
  metaData: String
  path: String!
  srcVideoId: String!
  startTime: Date!
  thumnail: String
}

type EventVideoGql {
  cameraId: String
  cameraIp: String
  count: Float
  deviceId: String!
  duration: Float!
  endTime: Date!
  metaData: String
  path: String!
  srcVideoId: String!
  startTime: Date!
  videoType: Float
}

type ExportAllGql {
  companies: [ExportComGql!]!
  departments: [ExportDepGql!]!
  users: [UserGql!]!
}

type ExportComGql {
  name: String!
  oldId: String!
  uniqueId: String!
}

type ExportDepGql {
  companyUniqueId: String!
  name: String!
  oldId: String!
  parentId: Float
  uniqueId: String!
}

type FaceAttributeGql {
  deviceId: String!
  emotion: String!
  emotionConfident: Float!
  id: String!
  trackId: String!
}

type FaceImageBase64GQL {
  data: String!
  dateCreated: Date
  dateModified: Date
  faceImageType: String!
  id: Int
  isClusterSync: Boolean
  path: String!
  patientId: String
  userId: String
}

"""Face Image custom scalar type"""
scalar FaceImageGraphQL

type FaceImageRescanJobGql {
  count: Int
  dateCreated: Date!
  error: [String!]
  id: String!
  isFinished: String!
  totalImage: Float!
  totalUser: Float!
}

type FaceIntegrationConfigGql {
  count: Float
  description: String
  endpointType: Float!
  isActive: Boolean!
  jsonConfig: String
}

type FaceMatchingResultGql {
  confident: Float!
  eventId: String!
  finised: Boolean!
  unknownEvent: UnknownFaceResultGql!
}

type FaceResultDebugInfoGql {
  detectDuration: Float
  detectTimepoint: Float
  faceKpts: String
  faceQualityScore: Float
  headposeDuration: Float
  headposePitch: Float
  headposeRoll: Float
  headposeYaw: Float
  maskConfident: Float
  maskDuration: Float
  qualityDuration: Float
  reIdConf: Float
  recogConfident: Float
  reqExDuration: Float
  reqTimePoint: Date
  searchDuration: Float
  topResultDebug: String
  vectorDuration: Float
  vectorHash: String
}

type FaceResultGeneralReportGql {
  avatar: String
  checkInOutTimes: DayInOutTimeGql!
  company: String
  department: String
  earlyTime: Int
  email: String
  fullName: String
  integrationKey: String
  lateTime: Int
  userId: String!
  workTime: Int!
}

type FaceResultGql {
  DebugInfos: FaceResultDebugInfoGql
  SrcDevice: SrcDeviceGql
  User: UserGql
  avatar: String
  cameraIp: String
  company: String
  count: Int
  dateCreated: Date
  dateModified: Date
  day: Date
  department: String
  deviceId: String
  earlyTime: Int
  email: String
  fullName: String
  id: Float
  image: String
  integrationKey: String
  isCheckedIn: Boolean
  isMaxTimeValid: Boolean
  isMinTimeValid: Boolean
  lastTime: Date
  lateTime: Int
  maxTime: Date
  minTime: Date
  reId: String
  recognitionType: String
  remoteDetail: String
  remoteStatus: Float
  srcDeviceId: String
  textNote: String
  time: Date
  trackId: String
  type: Int
  userId: String
  workTime: Int
}

type FaceResultGuestGql {
  AccessLocations: [AccessControlGql!]
  Company: CompanyGql
  Department: DepartmentGql
  Device: DeviceGql
  avatar: String
  cameraIp: String
  company: String
  count: Int
  dateCreated: Date
  dateModified: Date
  day: Date
  department: String
  deviceId: String
  earlyTime: Int
  fullName: String
  guestId: String
  id: Float
  image: String
  isCheckedIn: Boolean
  isMaxTimeValid: Boolean
  isMinTimeValid: Boolean
  lastTime: Date
  lateTime: Int
  maxTime: Date
  minTime: Date
  textNote: String
  time: Date
  type: Int
  workTime: Int
}

type FaceResultResponseModelGql {
  defaultValues: [String!]!
  id: String!
  messageKeys: [String!]!
  messageValues: [Float!]!
  modifyValues: [String!]!
}

type FaceSearchEventGql {
  dateCreated: Date!
  dateModified: Date!
  endSearchDate: Date
  id: String!
  imageSearched: Float!
  isFinished: Boolean!
  srcFaceImage: String!
  srcFaceImageBase64: String!
  startSearchDate: Date
  topResult: FaceMatchingResultGql
  totalImageSearch: Float!
}

type FaceTerminalConfigGql {
  Device: DeviceGql
  apiUrl: String!
  autoRestart: Boolean!
  autoStart: Boolean!
  checkMask: Boolean!
  checkTemperature: Boolean!
  companyLabel: String!
  debugModeOn: Boolean!
  defaultCompanyId: String!
  departmentLabel: String!
  deviceId: String!
  doorHoldTime: Float!
  fasIr: Boolean!
  fasRgb: Boolean!
  fasStrictModOn: Boolean!
  gRpcPort: Float!
  gRpcUrl: String!
  imageImprove: Boolean!
  integrationKeyLabel: String!
  isChangeLogo: Boolean!
  language: Float!
  lastSyncTime: Date!
  ledLevel: Float!
  ledOn: Boolean!
  logSaveDays: Float!
  logoBase64: String
  logoHeight: Float
  logoId: String
  logoWidth: Float
  logoX: Float
  logoY: Float
  maxUserWaitTime: Float!
  mqttBrokerIp: String!
  mqttBrokerPort: Float!
  numberUserRequired: Float!
  recogDistance: Float!
  recogMode: Float!
  samePersonDelay: Float!
  screenOnTime: Float!
  showAddCard: Boolean!
  showAddFinger: Boolean!
  showCompanyLabel: Boolean!
  showCompanyName: Boolean!
  showDepartmentLabel: Boolean!
  showDoorAccess: Boolean!
  showIntegrationKey: Boolean!
  showIr: Boolean!
  showQrCode: Boolean!
  showResultErrCall: Boolean!
  showTimekeeping: Boolean!
  socketUrl: String!
  soundLevel: Float!
  speakerOn: Boolean!
  temOnFaceOnly: Boolean!
  temperatureThreshold: Float!
  unknownDefineTime: Float!
  updateUrl: String!
}

type FaceTrackAttributeGql {
  FaceAttributeResults: [FaceAttributeGql!]
  User: UserGql
  ageConfident: Float
  ageFrom: Float
  ageTo: Float
  avgEmotion: String
  avgEmotionConfident: Float
  cameraId: String!
  cameraIp: String!
  deviceId: String!
  gender: String
  genderConfident: Float
  trackId: String!
}

type FaceTrackGql {
  Device: DeviceGql
  FaceTrackAtt: FaceTrackAttributeGql
  count: Float
  dateCreated: Date!
  deviceId: String!
  srcDeviceId: String
  trackId: String!
}

type FakeFaceResultGql {
  Device: DeviceGql
  avatar: String
  cameraIp: String
  company: String
  count: Int
  dateCreated: Date
  dateModified: Date
  department: String
  deviceId: String
  email: String
  fullName: String
  id: Float
  image: String
  integrationKey: String
  recognitionType: String
  srcDeviceId: String
  time: Date
  type: Int
  userId: String
}

type FcardTimeZoneGql {
  FcardTZ: Float
  Friday: [FcardTzSeg!]!
  Monday: [FcardTzSeg!]!
  Saturday: [FcardTzSeg!]!
  Sunday: [FcardTzSeg!]!
  TZ: String!
  Thursday: [FcardTzSeg!]!
  Tuesday: [FcardTzSeg!]!
  Wednesday: [FcardTzSeg!]!
}

type FcardTzSeg {
  endTime: String!
  startTime: String!
}

type FcardUserGql {
  TZ: FcardTimeZoneGql
  cardData: Float!
  cardStatus: Float!
  cardType: Float!
  dateCreated: Date!
  dateModified: Date!
  dept: String!
  deviceId: String!
  enterStatus: Float!
  expiry: Date!
  fcardTzId: Float
  fcardUserId: Float!
  fingerPrintFeatureCodeCount: Float!
  holiday: String!
  identity: Float!
  isFaceFeatureCode: Boolean!
  job: String!
  openTime: Float!
  pCode: String!
  pName: String!
  palmCount: Float!
  password: String!
  recordTime: Date!
  status: String!
  timeGroup: Float!
  userId: String
}

type FingerPrintGql {
  Student: StudentGql
  User: UserGql
  count: Int
  data: String!
  dateCreated: Date!
  dateModified: Date!
  expiredDate: Date
  id: String!
  image: String
  isActive: Boolean!
  isClusterSync: Boolean
  studentId: String
  type: Float!
  userId: String
}

type FunctionGql {
  count: Int!
  id: String!
  name: String!
}

enum Gender {
  Female
  Male
  Other
  Unknown
}

type HumanActionGql {
  Camera: CameraGql
  action: Float!
  base64Image: String!
  cameraId: String!
  cameraIp: String!
  confident: Float!
  count: Float!
  deviceId: String!
  endTime: Date!
  id: String!
  listImage: String
  startTime: Date!
  trackId: String!
}

type HumanTrackInfoGql {
  BestShot: HumanTrackResultGql
  Camera: CameraGql
  Location: LocationGQL
  User: UserGql
  attribute: [Float!]!
  cameraId: String!
  cameraIp: String
  confidents: [Float!]!
  count: Float
  dateModified: Date
  deviceId: String!
  firstIn: Date
  firstSeenTime: Date
  isUniform: Boolean
  isWearingCard: Boolean
  lastOut: Date
  lastOutDevice: DeviceGql
  nxCameraId: String
  trackId: String
  userId: String
}

type HumanTrackResultGql {
  adult_conf: Float
  backpack_conf: Float
  bad_kpt_body: Float
  bad_kpt_head: Float
  bad_kpt_lower: Float
  bodyB: String
  bodyG: String
  bodyPercent: Float
  bodyR: String
  child_conf: Float
  count: Float
  deviceId: String!
  emp_card_conf: Float
  female_conf: Float
  hat_conf: Float
  headPercent: Float
  humanTrackId: String!
  imageBase64: String!
  imageId: String!
  index: Float!
  isHaveFace: Boolean!
  long_hair_conf: Float
  long_sleeve_conf: Float
  lowerB: String
  lowerG: String
  lowerPercent: Float
  lowerR: String
  male_conf: Float
  path: String!
  short_hair_conf: Float
  short_sleeve_conf: Float
  shorts_conf: Float
  skirt_conf: Float
  time: Date!
  trouser_jeans_conf: Float
  uniform_conf: Float
}

type IdCardGQL {
  count: Float
  data: String
  expiredDate: Date
  id: String!
  isActive: Boolean
  serialNumber: String!
  studentId: String
  type: Float!
  userId: String
}

type IgnoreReIdGql {
  count: Int
  reId: String!
}

type InOutDataGql {
  in: Float!
  out: Float!
}

type IntegrationApiTokenGQL {
  count: Float
  description: String
  id: String!
  integrationId: String!
  isActive: Boolean!
  token: String!
}

type IparkingConfigGql {
  authen_url: String
  client_id: String!
  client_secret: String!
  configs: String!
  count: Int
  isActive: Boolean!
  url: String!
}

type IparkingCustomerGql {
  address: String
  code: String!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  dob: Date
  id: String!
  iparkingSyncTime: Date
  name: String!
  phone: String
}

type IparkingCustomerIdentityGql {
  code: String!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  id: String!
  iparkingGroupId: String!
  iparkingSyncTime: Date
  isClusterSync: Boolean!
  name: String!
  note: String!
  status: String!
  type: Int!
}

type IparkingEventCountGql {
  count: Int!
  groupName: String!
}

type IparkingGroupGql {
  code: String!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  id: String!
  iparkingSyncTime: Date
  isClusterSync: Boolean!
  name: String!
  plateNumberComparison: Int!
  plateNumberValidation: Int!
  type: String!
  vehicleType: Int!
}

type IparkingLaneGql {
  code: String!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  id: String!
  iparkingSyncTime: Date
  isClusterSync: Boolean!
  name: String!
  type: Int!
}

type IparkingReportGql {
  endDate: Date!
  eventIn: [IparkingEventCountGql!]!
  eventOut: [IparkingEventCountGql!]!
  startDate: Date!
}

type IparkingVehicleGql {
  Customer: IparkingCustomerGql
  checkInByPlate: Boolean!
  checkOutByPlate: Boolean!
  count: Int
  customerId: String!
  dateCreated: Date!
  dateModified: Date!
  expiredDate: Date!
  id: String!
  iparkingSyncTime: Date
  isClusterSync: Boolean!
  lastActivatedDate: Date!
  name: String!
  plateNumber: String!
  type: Int!
}

type LateReport {
  checkinTime: Date
  checkoutTime: Date
  companyId: String!
  departmentId: String!
  message: String!
  name: String!
  title: String!
  userId: String!
}

type LayoutGql {
  count: Int
  path: String!
  roleId: String!
}

type LocationGQL {
  Devices: [DeviceLocationGql!]
  count: Float
  description: String
  deviceCount: Float
  id: String!
  name: String!
  parentId: String
}

type MbUserRankConfigGql {
  companyType: Int!
  rank: Int!
  titles: [String!]!
}

type MobileGql {
  deviceName: String
  id: Int
  operatingSystem: String
  operatingSystemVersion: Int
  token: String
  tokenType: Int
}

type MqttConfigDevicesGql {
  Device: DeviceGql
  configId: String!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  deviceId: String!
  isClusterSync: Boolean!
  isDeleted: Boolean!
}

type MqttConfigGql {
  address: String!
  count: Int
  dataFormat: String
  id: String!
  isDeviceLimit: Boolean!
  isMqtts: Boolean!
  password: String
  topic: String
  type: Int!
  user: String
}

type Mutation {
  DeleteFileReport(id: String!): String!
  addACAdmins(locationIds: [String!]!, userIds: [String!]!): [ACAdminGql!]!
  addACDevice(deviceIds: [String!]!, locationId: String!): [ACDeviceGql!]!
  addACLocation(deviceIds: [String!]!, locationName: String!): ACLocationGql!
  addACTime(endDate: Date!, endTime: TimeOfDay!, name: String!, startDate: Date!, startTime: TimeOfDay!, weekDays: [Float!]!): AcTimeGql!
  addAcDepTimes(acTimeIds: [String!]!, departmentIds: [String!]!, locationIds: [String!]!): [ACDepTimeGql!]!
  addAcGuestTimes(access: [ACGuestInput!]!): Boolean!
  addAcPatientTimes(access: [ACPatientInput!]!): Boolean!
  addAcTimeToLocation(acTimeId: String!, locationId: String!): AcTimeGql!
  addAcUserTimes(acTimeIds: [String!]!, locationIds: [String!]!, userIds: [String!]!): [AcUserTimeGql!]!
  addAdUser(adNames: [String!]!): [UserGql!]!
  addAiServiceToRoi(aiServiceId: String, aiServiceType: Float, name: String, roiId: String!): String!
  addCamerasToDevice(cameraIds: [String!]!, deviceId: String!, isDisplay: [Boolean!]!): [DeviceCameraGql!]!
  addDeviceAttandantTime(attandantId: String!, deviceIds: [String!]!): [DeviceAttandantTimeGql!]!
  addDeviceFilterIntoUnknownAlarm(deviceIds: [String!]!): FaceIntegrationConfigGql!
  addDeviceIntoLocation(deviceIds: [String!]!, locationId: String!): Int!
  addDeviceToGroup(deviceGroupId: String, deviceIds: [String!]!): [DeviceGql!]!
  addDeviceToParkingLane(cameraId: String, channel: Int!, deviceId: String!, deviceType: Int!, parkingId: String!): VehiclePackingDeviceGql!
  addGuestIntoLocation(guestIds: [String!]!, locationId: String!): Int!
  addGuestsToDevice(deviceIds: [String!]!, guestIds: [String!]!): Float!
  addIgnoreRemoteUser(userId: String!): Boolean!
  addMbAcReplace(accessControlId: String!, endTime: Date!, replaceOpenUserId: String!, startTime: Date!, userId: String!): AccessControlUserGql!
  addOrgQuickly(companyIds: [String!], departmentIds: [String!], organizationId: String!): [String!]!
  addOrganizationShifts(shifts: [ShiftUserInput!]!): Boolean!
  addReplaceOpenUser(accessControlId: String!, endTime: Date!, replaceOpenUserId: String!, startTime: Date!, userId: String!): AccessControlUserGql!
  addShiftType(actualWorkHour: Float!, autoDays: String, breakBonus: Float!, breakCode: String!, breakwithPaid: Float!, breakwithoutPaid: Float!, code: String!, description: String, endBreak: String!, endHour: String!, overnight: Float!, startBreak: String!, startHour: String!, totalWorkHour: Float!, work: Float!): ShiftTypeGQL!
  addUser(input: UserAddInput!): UserGql!
  addUserAttendants(attendantId: String!, comIds: [String!], depIds: [String!], endDate: String!, isActive: Boolean!, orgIds: [String!], startDate: String!, userIds: [String!]): Boolean!
  addUserShift(endDate: String!, shiftId: String!, startDate: String!, userId: String!): Boolean!
  addUserShifts(shifts: [ShiftUserInput!]!): Boolean!
  approveDepartmentAdmin(adminId: [String!]!, departmentId: [String!]!): [DepartmentAdminGql!]!
  approveMbAcUserWaitting(accessControlId: String!, userId: String!): Boolean!
  approveRemoteResult(remoteId: Float!, status: Float!): Float!
  approveShiftUserExplain(explainIds: [String!]!): Float!
  assignDefaultShiftToNoShiftUser(endDate: Date!, startDate: Date!): Float!
  autoBackups(tables: String): String!
  autoCreateAllShiftDefaultNotAssigned(endDate: String!, startDate: String!): Float!
  autoCreateShiftUserFromDefault: Float!
  autoRecalculateAllShiftUser(endDate: Date!, password: String!, startDate: Date!, tokenType: Float): Float!
  autoUpdateSystemId(id: String!): NxSystemGQL!
  backupCompany(lastSaveDate: Date): Float!
  backupCompanyAuto: Float!
  backupDefaultShiftAuto: Float!
  backupDepartment(lastSaveDate: Date): Float!
  backupDepartmentAuto: Float!
  backupFaceResult(lastSaveDate: Date): Float!
  backupFaceResultAuto: Float!
  backupShiftType(lastSaveDate: Date): Float!
  backupShiftTypeAuto: Float!
  backupShiftUserAuto: Float!
  backupShiftUserExplainAdminTypeAuto: Float!
  backupShiftUserExplainAuto: Float!
  backupUser(lastSaveDate: Date): Float!
  backupUserAuto: Float!
  calculateWorkMonth(companyIds: [String!], month: Float!, userIds: [String!], year: Float!): [CalculatedWorkMonthGql!]!
  changeCardStatus(ids: [String!], isActive: Boolean!, sns: [String!]): Float!
  changeEzCamStreamMode(cameraId: String!, streamMode: Float!): Float!
  changeFingerStatus(ids: [String!], isActive: Boolean!): Float!
  changeGuestStatus(companyIds: [String!], departmentIds: [String!], guestIds: [String!], isActive: Boolean!): Float!
  changePassword(newPassword: String!, oldPassword: String!, userId: String!): Boolean!
  changeTimeStatusAcLocation(isAlwayOpen: Boolean!, locationId: String!): ACLocationGql!
  checkUpsertUserToFcard(deviceId: String!, fCardServer: String): Float!
  cleanEventVideo: Boolean!
  clearOldOrphanResult: Boolean!
  clusterUpsertUser(userId: String!): Float!
  configIntegrationUnknown(alarmDelayTime: Int!, deviceFilter: [String!]!, isAlarmReIdOnly: Boolean!, isDeviceFilter: Boolean!): FaceIntegrationConfigGql!
  confirmByPass(password: String!, tokenType: Float): Boolean!
  convertFcardTzByAcId(acLocationId: String!): FcardTimeZoneGql!
  convertFcardTzByUserId(deviceId: String, userId: String!): FcardTimeZoneGql!
  create3RdApiToken(description: String, integrationId: String!): IntegrationApiTokenGQL!
  createCompany(name: String!): CompanyGql!
  createDepartmentTree(companyId: String!, name: String!, parentId: String!): DepartmentGql!
  createShiftUserExplain(endExplainTime: Date!, explainText: String!, explainType: Float!, startExplainTime: Date!): ShiftUserExplainGql!
  createUserTimeReportMannualy(endDate: String!, startDate: String!): Float!
  createfaceMatchingEvent(delay: Float!, endDate: Date!, eventId: String!, faceImage: String!, startDate: Date!): String!
  delAcGuestTime(id: String!): AcGuestTimeGql!
  delAcPatientTime(id: String!): AcPatientTimeGql!
  delAcTimeInLocation(acTimeIds: [String!]!, locationIds: [String!]!): Float!
  delPermission(functionId: String!, roleId: String!): Float!
  deleteACAdmins(locationIds: [String!]!, userIds: [String!]!): Float!
  deleteACLocation(locationId: String!): Boolean!
  deleteALlShiftUser: Int!
  deleteAiService(id: String!): String!
  deleteAiServiceConfig(aiServiceId: String!, configType: Float!): Boolean!
  deleteAiServiceInRoi(aiServiceId: String!, roiId: String!): Boolean!
  deleteAlertScript(receiverEmails: [String!], receiverIds: [String!], ruleId: String!): Float!
  deleteAllAcDepTimesAtLocation(departmentIds: [String!]!, locationIds: [String!]!): Float!
  deleteAllAcUserTimes(locationIds: [String!]!, userIds: [String!]!): Float!
  deleteAttDevice(id: String!): Boolean!
  deleteAttInfo(attInfoId: String): Boolean!
  deleteAttandantTime(attTimeId: String!): Boolean!
  deleteBackup(jobId: String): Boolean!
  deleteCamera(cameraId: String!): Boolean!
  deleteCameraRoi(id: String!): String!
  deleteCivamsCustomer(id: String!): String!
  deleteCivamsIdentity(id: String!): String!
  deleteCompany(companyId: String!): CompanyGql!
  deleteDefaultShiftType(defaultShiftId: String!): String!
  deleteDepartment(departmentId: String!): DepartmentGql!
  deleteDevice(deviceId: String!): Float!
  deleteDeviceGroupInfo(deviceGroupId: String!): Float!
  deleteDeviceRoi(id: String!): String!
  deleteDeviceinDeviceGroup(deviceGroupId: String!, deviceIds: [String!]!): Float!
  deleteFakeResult(fakeId: Float!): Boolean!
  deleteGuests(companyIds: [String!], departmentIds: [String!], guestIds: [String!]): Float!
  deleteIparkingIntegration(url: String!): Boolean!
  deleteIparkingVehicle(vehicleId: String!): Boolean!
  deleteMbAc(acId: String!): String!
  deleteMbAcUserWaitting(accessControlId: String!, userId: String!): Boolean!
  deleteMqttAddIn(configId: String!): Boolean!
  deleteNotificationTime(id: String!): String!
  deleteOrgTemplate(templateIds: [String!]!): Float!
  deleteOrganization(organizationId: String!): String!
  deleteParkingLane(id: String!): Float!
  deletePatientImageById(patientId: String!): Boolean!
  deleteRemoteResult(remoteId: Float!): Boolean!
  deleteReportUserFilter(id: String): Boolean!
  deleteReportUserFilterCompany(companyIds: [String!]!, filterId: String!): Boolean!
  deleteReportUserFilterDepartment(departmentIds: [String!]!, filterId: String!): Boolean!
  deleteReportUserFilterExRuleUser(filterId: String!, userIds: [String!]!): Boolean!
  deleteReportUserFilterExUser(filterId: String!, userIds: [String!]!): Boolean!
  deleteReportUserFilterUser(filterId: String!, userIds: [String!]!): Boolean!
  deleteSearchFeatureImageEvent(eventId: String!): String!
  deleteShiftType(code: String!, force: Boolean): Boolean!
  deleteShiftUser(endDate: Date!, shiftId: String!, startDate: Date!, userId: String!): Boolean!
  deleteShiftUserVip(userIds: [String!]!): Float!
  deleteTelegramBot(id: String!): String!
  deleteUploadVideoAnalytics(videoId: String!): String!
  deleteUserAttendantTime(attandantId: String!, endDate: Date!, startDate: Date!, userId: String!): Boolean!
  deleteVideoAnalyticGroup(groupIds: [String!]!): Int!
  deviceCameraSnapshotRequest(cameraId: String!, deviceId: String!): Float!
  deviceStreamConfig(cameraId: String!, deviceId: String!, faceCheckType: Float!, faceMinHeight: Float!, faceMinWidth: Float!, faceRoiEnable: Boolean!, faceRoiHeight: Float!, faceRoiWidth: Float!, faceRoiX: Float!, faceRoiY: Float!, faceZoomEnable: Boolean!, faceZoomMaxHeight: Float!, faceZoomMaxWidth: Float!): Float!
  editCompany(companyType: Int, id: String!, name: String!): CompanyGql!
  editDepartment(id: String!, name: String!): DepartmentGql!
  editImportJobRow(data: String!, rowId: String!): Boolean!
  editShift(checkinKey: Float, checkoutKey: Float, endDate: Date!, shiftId: String!, startDate: Date!, userId: String): ShiftUserGql!
  exportAllFaceResult: String!
  exportAllUserZipFile: String!
  exportCompanyToJson: Float!
  exportDefaultShiftToJson: Float!
  exportDepartmentToJson: Float!
  exportFaceResultToFile(endDate: Date, startDate: Date): Float!
  exportGeneralData(companyIds: [String!], departmentIds: [String!], userIds: [String!]): [UserGql!]!
  exportPublic(lastSaveDate: String, name: String!): Float!
  exportShiftTypeToJson: Float!
  exportShiftUserExplainAdminToJson: Float!
  exportShiftUserExplainToJson: Float!
  exportShiftUserToJson: Float!
  exportUserZipFileByFilter(companyId: String, departmentId: String, userId: String): String!
  extendTime(duration: String!, guestId: String!): String!
  fullDeleteRemoteAdmin(adminId: String!): Boolean!
  fullDeleteRemoteApprove(approverId: String!): Boolean!
  fullDeleteUser(password: String!, tokenType: Float, userIds: [String!]!): Float!
  genDeviceAuthenToken(deviceId: String!, isNoTimeLimit: Boolean): String!
  generateSessionToken(nxUserId: String!): NxUserLoginResultGQL!
  generateUserOtp(userId: String!): String!
  getAllPublic: Float!
  getCtelAllEmployee: CtelHubSyncResultGQL!
  getCtelOrganizationFullInfo: CtelHubSyncResultGQL!
  getCtelShiftExplainByMonth(date: String!): Float!
  importCtelMonthWorkTime(date: String!): CtelHubSyncResultGQL!
  importCtelTimeKeepingLog(date: String!): CtelHubSyncResultGQL!
  importCtelTimeKeepingLogByMonth(month: String!): CtelHubSyncResultGQL!
  lockAllUser: Float!
  lockAllUserGraphql(comIds: [String!], depIds: [String!], isActive: Boolean!, userIds: [String!]): Float!
  lockDepartmentAdmin(adminId: [String!]!, departmentId: [String!]!): [DepartmentAdminGql!]!
  lockUpdateFace(userId: String!): String!
  lockUser(isLock: Boolean!, userId: String!): Boolean!
  logout: String!
  mannualOpenBarrie(laneId: String!): VehiclePackingResultGql!
  markAllReadMess: Float!
  markReadMess(messId: Float!): Boolean!
  pullAllShiftUser: Int!
  pushUserToFcard(deviceId: String!, fCardServer: String, userId: String!): String!
  reCAlculateReportUserFilterResult(filterId: String!): Boolean!
  reCalculateShiftUser(endDate: Date!, password: String!, startDate: Date!, tokenType: Float, userId: String!): Float!
  reScanAllUserFaceImages: Float!
  reScanUserFaceImages(companyIds: [String!]!, departmentIds: [String!]!, userIds: [String!]!): Float!
  reSearchEvent(smartSearchEventId: String!): String!
  reSearchVehicleEvent(vehicleSearchEventId: String!): String!
  recalculateShiftValid(endDate: String!, startDate: String!): Float!
  recoverFakeResult(fakeId: Float!): Float!
  refetchAccessToken(refreshToken: String!): UserGql!
  regisFullShiftForUsers(comIds: [String!]!, depIds: [String!]!, endDate: String!, orgIds: [String!]!, startDate: String!, userIds: [String!]!): Float!
  rejectShiftUserExplain(explainIds: [String!]!): Float!
  removeACDevice(deviceIds: [String!]!, locationId: String!): Float!
  removeAlertUser(aleartIds: [String!]!, userIds: [String!]!): Float!
  removeCamerasFromDevice(cameraIds: [String!]!, deviceId: String!): Float!
  removeCompanyOwner(companyIds: [String!]!, ownerIds: [String!]!): Float!
  removeDepartmentAdmin(adminId: [String!]!, departmentId: [String!]!): Float!
  removeDeviceAttandantTime(attandantId: String!, deviceIds: [String!]!): [DeviceAttandantTimeGql!]!
  removeDeviceFilterIntoUnknownAlarm(deviceIds: [String!]!): FaceIntegrationConfigGql!
  removeDeviceFromLocation(deviceIds: [String!]!, locationId: String!): Int!
  removeDeviceFromParkingLane(id: String!): Float!
  removeGuestFromLocation(guestIds: [String!]!, locationId: String!): Int!
  removeIgnoreReId(reId: String!): String!
  removeIgnoreRemoteUser(userId: String!): Float!
  removeLicenseAllow(licensePlate: String!): Float!
  removeLocation(locationId: String!): Boolean!
  removeMbAcReplace(accessControlId: String!, userId: String!): Boolean!
  removeMbAcUser(acId: String!, userId: String!): Boolean!
  removeMobileToken: [MobileGql!]!
  removeReIdLabel(reIds: [String!]!): Int!
  removeRemoteLimit(comIds: [String!], depIds: [String!], orgIds: [String!], userIds: [String!]): Float!
  removeReplacer(accessControlId: String!, userId: String!): Boolean!
  removeShiftUserExplain(explainIds: [String!]!): Float!
  removeShiftUserExplainAdmin(configId: String!): Boolean!
  removeStudent(studentId: String, userId: String): [String!]!
  removeUserEkycIntegration(userId: String!): String!
  removeUserFaceImages(faceImagePaths: [String!]!, userId: String!): Float!
  removeUserShift(endDate: String!, shiftId: String!, startDate: String!, userId: String!): Boolean!
  resetAllDeviceToken: Float!
  resetAllUserInDevice(deviceIds: [String!]!): Float!
  resetCtelAuthenTokken: Float!
  resetDeviceToken(deviceId: String!): String!
  resetPassRequest(email: String!): String!
  resetPassWithToken(email: String!, newPassword: String!, token: String!): String!
  resetPassword(userId: String!): String!
  responseFeedback(detail: String!, status: Float!, userFeedbackId: String!): UserFeedbackGql!
  restartBackEnd(password: String!, tokenType: Float): Boolean!
  restoreBackup(jobId: String): Float!
  searchByServerPath(cameraIds: [String!]!, endTime: Date!, isSearchFace: Boolean!, isSearchMct: Boolean!, select: Float!, serverFilePath: String!, startTime: Date!, threshold: Float!, topN: Float!): String!
  searchShiftUserVip(page: Int!, perPage: Int!, search: String!): [UserGql!]!
  searchVehicleByServerPath(cameraIds: [String!]!, deviceId: String, endTime: Date!, serverFilePath: String!, startTime: Date!, topN: Float!): String!
  sendAiCoreConfigData(deviceId: String!): String!
  sendAiCoreConfigRequest(deviceId: String!): String!
  sendCameraAuthenToken(cameraId: String!): String!
  sendCameraRoiConfigData(cameraId: String!, cameraRoiId: String, deviceId: String!): String!
  sendCameraRoiConfigRequest(cameraId: String!, cameraRoiId: String, deviceId: String!): String!
  sendDeviceCameraConfigRequest(deviceId: String!): Boolean!
  sendDeviceCmd(cmd: String!, deviceId: String!): String!
  sendDeviceReboot(deviceId: String!): Boolean!
  sendDeviceStreamConfigData(deviceId: String!): Boolean!
  sendDeviceSyncSignal(dbTables: [String!], deviceId: String!): Boolean!
  sendTokenSignal(deviceId: String!): String!
  setCompanyOwner(companyIds: [String!]!, ownerIds: [String!]!): [CompanyOwnerGql!]!
  setDepartmentAdmin(adminId: [String!]!, departmentId: [String!]!): [DepartmentAdminGql!]!
  setDeviceToTelegramGroup(deviceIds: [String!]!, groupId: String!, isEnable: Boolean!): Boolean!
  setGstConfig(alignMode: String!, bBoxMargin: String!, deviceId: String!, faceBestShotDuration: String!, faceBestShotEnable: String!, faceConfident: String!, fakeFaceEnable: String!, fasConfident: String!, fasValidationCount: String!, kptConfident: String!, maxSize: String!, namedSentDuration: String!, personBestShotDuration: String!, personDetectConfident: String!, serverUrl: String!, timeout: String!): String!
  setReIdLabel(label: String, reId: String!, userId: String): ReIdLabelGql!
  setReceiverToTelegramGroup(groupId: String!, isEnable: Boolean!, telegramIds: [String!]!): Boolean!
  setRemoteLimit(comIds: [String!], dateLimit: Date, depIds: [String!], orgIds: [String!], timesLimit: Float!, userIds: [String!]): Float!
  setRemoteRecognizeUser(companyIds: [String!]!, departmentIds: [String!]!, isEnable: Boolean!, organizationIds: [String!]!, userIds: [String!]!): Float!
  setTimeToTelegramGroup(groupId: String!, isEnable: Boolean!, timeIds: [String!]!): Boolean!
  setUsersToTelegramGroup(companyIds: [String!]!, departmentIds: [String!]!, groupId: String!, isEnable: Boolean!, userIds: [String!]!): Boolean!
  setWpfFaceTerPass(deviceId: String!, pass: String!): Boolean!
  testWpfDevice(deviceId: String!, pass: String!): Boolean!
  testWpfDeviceAuto(lastPullAt: Date!): Boolean!
  toggleAiService(id: String!, isEnable: Boolean!): Boolean!
  toggleAiServiceConfig(aiServiceId: String!, configType: Float!, isEnable: Boolean!): Boolean!
  toggleBlacklistUser(isBlacklist: Boolean!, userIds: [String!]!): Int!
  toggleCameraRoi(id: String!, isEnable: Boolean!): CameraROIGql!
  toggleCameraStatus(cameraId: String!, status: Boolean!): Boolean!
  toggleDeviceRoi(id: String!, isEnable: Boolean!): DeviceRoiGql!
  toggleMqttConfigDevices(configId: String!, deviceIds: [String!]!, isDeleted: Boolean!): Float!
  toggleVideoAnalyticInGroup(groupId: String!, isInGroup: Boolean!, videoIds: [String!]!): VideoAnalyticGroupGql!
  toggleVipUser(isVip: Boolean!, userIds: [String!]!): Int!
  toogleIparkingIntegration(isActive: Boolean!, url: String!): IparkingConfigGql!
  unlockLogin(userId: String!): Boolean!
  unlockUpdateFace(userId: String!): String!
  updateACLocationName(locationId: String!, locationName: String!): ACLocationGql!
  updateACTime(acTimeId: String!, endDate: Date!, endTime: TimeOfDay!, name: String!, startDate: Date!, startTime: TimeOfDay!, weekDays: [Float!]!): AcTimeGql!
  updateAttandantType(detail: String!, faceResultId: Float!, type: Float!): AttandantExplainGql!
  updateAutoDays(autoDays: String!, code: String!): ShiftTypeGQL!
  updateCameraConfig(cameraId: String, distanceMin: String!, faceService: String!, frameRate: String!, inputCodec: String!, outputCodec: String!, personService: String!, recordDuration: String!, streamMode: String!): Boolean!
  updateDevice(deviceId: String!, deviceName: String!, deviceStatus: NumberStatus!, isDefaultDevice: Boolean!, isFaceResultEditable: Boolean!, isFirmwareAutoUpdate: Boolean!, isTimekeepingDevice: Boolean!): DeviceGql!
  updateDeviceFaceCheckType(deviceId: String!, faceCheckType: Float!): Float!
  updateDeviceType4Config(camIp: String, camRtsp: String, deviceId: String!, httpApi: String, httpBrokerIp: String, httpBrokerPort: Float, isDoorAccess: Boolean, isPlaySound: Boolean, language: String, lastSyncTime: Date, serverIp: String, serverPort: Float): DeviceType4ConfigGql!
  updateFaceTerminalConfig(apiUrl: String, autoRestart: Boolean, autoStart: Boolean, checkMask: Boolean, checkTemperature: Boolean, companyLabel: String, debugModeOn: Boolean, defaultCompanyId: String, departmentLabel: String, deviceId: String!, doorHoldTime: Float, fasIr: Boolean, fasRgb: Boolean, fasStrictModOn: Boolean, gRpcPort: Float, gRpcUrl: String, imageImprove: Boolean, integrationKeyLabel: String, language: Float, ledLevel: Float, ledOn: Boolean, logSaveDays: Float, maxUserWaitTime: Int, mqttBrokerIp: String, mqttBrokerPort: Float, numberUserRequired: Int, recogDistance: Float, recogMode: Float, samePersonDelay: Float, screenOnTime: Float, showAddCard: Boolean, showAddFinger: Boolean, showCompanyLabel: Boolean, showCompanyName: Boolean, showDepartmentLabel: Boolean, showDoorAccess: Boolean, showIntegrationKey: Boolean, showIr: Boolean, showQrCode: Boolean, showResultErrCall: Boolean, showTimekeeping: Boolean, socketUrl: String, soundLevel: Float, speakerOn: Boolean, temOnFaceOnly: Boolean, temperatureThreshold: Float, unknownDefineTime: Float, updateUrl: String): FaceTerminalConfigGql!
  updateGuestInfo(address: String!, birthday: Date!, cardId: String!, companyId: String!, departmentId: String!, gender: String!, guestId: String!, nationality: String!, placeOfBirth: String!, userName: String!): String!
  updateMultiShiftUser(endDate: Date!, newShiftIds: [String!]!, oldShiftIds: [String!]!, startDate: Date!, userIds: [String!]!): Boolean!
  updateServerConfig(key: String!, value: String!): ServerConfigGql!
  updateShiftUser(endDate: Date!, newShiftId: String!, oldShiftId: String!, startDate: Date!, userId: String!): Boolean!
  updateUser(input: UserAddInput!): UserGql!
  updateUserAttendantTime(endDate: Date!, newAttandantId: String!, oldAttandantId: String!, startDate: Date!, userId: String!): Boolean!
  updateUserFaceRecogThreshold(comIds: [String!], depIds: [String!], maskThreshold: Float!, noMaskThreshold: Float!, userIds: [String!]): Int!
  updateUserRole(roleId: String!, userId: String!): UserRoleGql!
  upsert3rdApiToken(description: String, id: String, integrationId: String!, isActive: Boolean, token: String!): IntegrationApiTokenGQL!
  upsertAc(acId: String, doorHoldTime: Float, isUseAdvance: Boolean, multipleUserWaitTime: Float, name: String!, userNumberRequired: Float): AccessControlGql!
  upsertAcCamera(acLocationId: String!, cameraIds: [String!]!, isActive: Boolean): Float!
  upsertAcCom(acId: String, comIds: [String!]!, isEnable: Boolean): [AccessControlCompanyGql!]!
  upsertAcDep(acId: String, depIds: [String!]!, isEnable: Boolean): [AccessControlDepartmentGql!]!
  upsertAcDevice(acId: String, deviceIds: [String!]!, inOutMode: Float, isEnable: Boolean): [AccessControlDeviceGql!]!
  upsertAcDeviceGroup(acId: String, deviceGroupIds: [String!]!, isEnable: Boolean): [AccessControlDeviceGroupGql!]!
  upsertAcOrg(acId: String, isEnable: Boolean, orgIds: [String!]!): [AccessControlOrgGql!]!
  upsertAcTime(acId: String!, acTimeId: String, endDate: Date!, endTime: String!, isEnable: Boolean!, name: String!, startDate: Date!, startTime: String!, weekDays: [Int!]!): AccessControlTimeGql!
  upsertAcUser(acId: String, isEnable: Boolean, userIds: [String!]!): [AccessControlUserGql!]!
  upsertAccessControlCamera(acId: String, cameraIds: [String!]!, isEnable: Boolean): [AccessControlCameraGql!]!
  upsertAiBoxConfig(cameraFps: Float, cameraIp: String, cameraPass: String, cameraRtsp: String, cameraUser: String, customeConfig: DeviceCustomeConfigGql, deviceId: String!, doorHoldTime: Float): AiBoxConfigGql!
  upsertAiService(alertDelaySecs: Float, configs: [AiServiceConfigInput!], dropFps: Float, endTime: String, id: String, isPopupAlert: Boolean, isTimeLimit: Boolean, name: String!, startTime: String, type: Float!): AiServiceGql!
  upsertAiServiceConfigs(aiServiceId: String!, configs: [AiServiceConfigInput!]): AiServiceConfigGql!
  upsertAlertRule(alertLevel: Float!, description: String!, endDate: Date, endHour: String, isRangeLimit: Boolean!, isTimeLimit: Boolean!, name: String!, ruleID: String, startDate: Date, startHour: String, type: Float!, weekDays: [String!]): AlertRuleGql!
  upsertAlertScript(receiverEmails: [String!], receiverIds: [String!], ruleId: String, type: Float!): Boolean!
  upsertAttDevice(attInfoId: String!, cameraId: String, deviceId: String!): AttDeviceGql!
  upsertAttGuests(attInfoId: String!, guestIds: [String!]!, isActive: Boolean): Float!
  upsertAttInfo(attInfoId: String, endTimeString: String!, name: String!, startTimeString: String!): AttInfoGql!
  upsertAttUsers(attInfoId: String!, isActive: Boolean, userIds: [String!]!): Float!
  upsertAttandantTime(attandantId: String, deviceIds: [String!], endHour: String!, isActive: Boolean!, isNoTimeLimit: Boolean!, isNoUserLimit: Boolean!, name: String!, startHour: String!): AttandantTimeGql!
  upsertBlackListAlertUser(userIds: [String!]!): [UserAlertGQL!]!
  upsertCamera(cameraId: String, ip: String!, name: String!, rtsp: String!, subRtsp: String): CameraGql!
  upsertCameraRoi(aiServiceName: String, aiServiceTypes: [Float!], cameraId: String!, deviceId: String!, id: String, name: String!, xs: [String!]!, ys: [String!]!): CameraROIGql!
  upsertDefaultShiftType(companyId: String, defaultShiftId: String, departmentId: String, shiftId: String!, userId: String): ShiftTypeDefaultGql!
  upsertDeviceAdmin(adminId: String!, deviceId: String!, isActive: Boolean): DeviceUserGql!
  upsertDeviceCompany(companyId: String!, deviceIds: [String!]!): Float!
  upsertDeviceGroupInfo(deviceGroupId: String, name: String!, piority: Float): DeviceGroupInfoGql!
  upsertDeviceRoi(aiServiceIds: [String!], deviceId: String!, id: String, name: String!, xs: [String!]!, ys: [String!]!): DeviceRoiGql!
  upsertFaceIntegrationConfig(description: String, endpointType: Int!, isActive: Boolean): Float!
  upsertFaceResultResponseModel(defaultValues: [String!]!, keys: [String!]!, modelId: String, modifyValues: [String!]!, values: [Float!]!): FaceResultResponseModelGql!
  upsertFcardUserTz(userIds: [String!]): [FcardUserGql!]!
  upsertIgnoreReId(reId: String!): String!
  upsertIparkingIntegration(authen_url: String, clientId: String!, clientSecret: String!, configs: String!, url: String!): IparkingConfigGql!
  upsertIparkingVehicle(checkInByPlate: Boolean!, checkOutByPlate: Boolean!, customerId: String!, expiredDate: Date!, lastActivatedDate: Date!, name: String!, plateNumber: String!, type: Int!): IparkingVehicleGql!
  upsertLicenseAllow(guestId: String, licensePlate: String!, userId: String): VehiclePackingLicenseAllowGql!
  upsertLocation(description: String, locationId: String, locationName: String!, parentId: String): LocationGQL!
  upsertMbAc(bindingCompanyId: String!): AccessControlGql!
  upsertMbAcUser(acId: String, userId: String!, userRank: Int!): AccessControlUserGql!
  upsertMobileToken(token: String!): [MobileGql!]!
  upsertMqttAddIn(address: String!, configId: String, dataFormat: String!, isDeviceLimit: Boolean!, isMqtts: Boolean!, topic: String!, type: Float!): MqttConfigGql!
  upsertNotiTime(endDate: Date!, endHour: Float!, endMin: Float!, endSec: Float!, id: String, isClusterSync: Boolean!, name: String!, startDate: Date!, startHour: Float!, startMin: Float!, startSec: Float!): NotificationTimeGql!
  upsertNotificationTime(endHour: Float, endMin: Float, endSec: Float, endTime: Date, name: String!, startHour: Float, startMin: Float, startSec: Float, startTime: Date, timeId: String): NotificationTimeGql!
  upsertNxSystem(baseUrl: String!, id: String, name: String!): NxSystemGQL!
  upsertNxUser(nxSystemId: String!, nxUserId: String, userName: String!, userPass: String!): NxUserGQL!
  upsertOrgTemplate(description: String, isAdd: Boolean, name: String!, orgIds: [String!]!, templateId: String): Float!
  upsertOrganization(organizationId: String, organizationName: String!, parentId: String, type: Float!): OrganizationGQL!
  upsertOrganizationMasters(isEnable: Boolean!, organizationId: String!, userIds: [String!]!): Float!
  upsertOrganizationUsers(companyIds: [String!]!, departmentIds: [String!]!, isEnable: Boolean!, organizationId: String!, userIds: [String!]!): Float!
  upsertParentId(organizationId: String!, parentId: String!): OrganizationGQL!
  upsertParkingLane(barrieMode: Int!, laneId: String, laneType: Int!, name: String!, vehicleType: Int!): VehicleParkingLaneGQL!
  upsertPermission(create: Boolean!, del: Boolean!, functionId: String!, newRoleId: String!, oldRoleId: String!, read: Boolean!, update: Boolean!): PermissionGql!
  upsertRemoteAdmin(adminIds: [String!]!, companyIds: [String!]!, departmentIds: [String!]!, isEnable: Boolean!, orgIds: [String!]!, userIds: [String!]!): String!
  upsertRemoteApprover(aproverIds: [String!]!, companyIds: [String!]!, departmentIds: [String!]!, isEnable: Boolean!, orgIds: [String!]!, userIds: [String!]!): String!
  upsertReportUserFilter(companyIds: [String!], departmentIds: [String!], excludeFilterUserIds: [String!], excludeRuleUserIds: [String!], id: String, name: String!, userIds: [String!]): ReportUserFilterGql!
  upsertReportUserFilterCompany(companyIds: [String!]!, filterId: String!): Float!
  upsertReportUserFilterDepartment(departmentIds: [String!]!, filterId: String!): Float!
  upsertReportUserFilterExRuleUser(filterId: String!, userIds: [String!]!): Float!
  upsertReportUserFilterExUser(filterId: String!, userIds: [String!]!): Float!
  upsertReportUserFilterUser(filterId: String!, userIds: [String!]!): Float!
  upsertSelfCustomeService(dataKeys: [String!]!, dataVals: [String!]!, headerKeys: [String!]!, headerVals: [String!]!, url: String!): Boolean!
  upsertShiftType(autoDays: String, breakwithPaid: Float!, code: String!, description: String, endBreak: String, endHour: String!, startBreak: String, startHour: String!, totalWorkHour: Float!): ShiftTypeGQL!
  upsertShiftUserExplainAdmin(adminId: String!, companyId: String, configId: String, departmentId: String): Boolean!
  upsertShiftUserVip(userIds: [String!]!): Float!
  upsertStudent(classCode: String!, className: String!, grade: String!, majorCode: String!, majorName: String!, userId: String!, validUntil: Date!): String!
  upsertTelegramBot(first_name: String!, id: String!, token: String!, userName: String!): String!
  upsertTelegramGroup(groupId: String, isEnable: Boolean!, name: String!, type: Float!): TelegramGroupGQL!
  upsertUserEkycIntegration(ekycUrl: String!, token: String!, userId: String!): EkycIntegrationUserGql!
  upsertUserTimeReportByDayGql(date: String!, userId: String!): Float!
  upsertUserTimeReportManually(date: String!): Float!
  upsertVideoGroup(ageGenderEnable: Boolean, description: String!, faceIdEnable: Boolean, faceReIdEnable: Boolean, faceSmartSearchEnable: Boolean, groupId: String!, humanAttEnable: Boolean, humanSmartSearchEnable: Boolean, name: String!, s2tEnable: Boolean, type: String!, vehicleAttEnable: Boolean, vehicleSmartSearchEnable: Boolean, videoAbnormalEnable: Boolean, videoIds: [String!]!): VideoAnalyticGroupGql!
  upsertVipAlertUser(userIds: [String!]!): [UserAlertGQL!]!
}

type NotificationGql {
  FaceResult: RecordGql
  FaceResultId: Int
  count: Int
  dateCreated: Date
  dateModified: Date
  id: Int
  message: String
  userId: String
}

type NotificationTimeGql {
  count: Int
  endDate: Date
  endHour: Int
  endMin: Int
  endSec: Int
  id: String!
  name: String!
  startDate: Date
  startHour: Int
  startMin: Int
  startSec: Int
}

enum NumberStatus {
  Active
  InActive
}

type NxSystemGQL {
  baseUrl: String!
  dateCreated: Date
  dateModified: Date
  id: String!
  name: String!
  nxLocalId: String!
}

type NxUserGQL {
  NxSystem: NxSystemGQL!
  authenToken: String!
  dateCreated: Date
  dateModified: Date
  id: String!
  password: String!
  userName: String!
}

type NxUserLoginResultGQL {
  ageS: Float!
  expiresInS: Float!
  token: String!
  username: String!
}

type OffTimeUserReportGql {
  Company: CompanyGql
  Department: DepartmentGql
  DeviceIn: DeviceGql
  DeviceOut: DeviceGql
  Reason: DayInvalidReasonGql
  avatar: String
  day: String!
  earlyMinutes: Float
  email: String!
  firstIn: Date
  id: String!
  integrationKey: String
  lastOut: Date
  lastTime: Date
  lateMinutes: Float
  name: String!
  title: String
}

type OrgRemoteLimitGQL {
  Organization: OrganizationGQL
  count: Float
  dateLimit: Date
  limitTimes: Float
  orgId: String!
}

type OrganizationGQL {
  Approvers: [UserGql!]
  ChildOrgs: [OrganizationGQL!]
  Masters: [UserGql!]
  avgTotalWorkTime: Float
  count: Float
  id: String!
  maxInvalid: Float
  minWorkTime: Float
  name: String!
  parentId: String
  totalEarlySecs: Float
  totalInvalid: Float
  totalLateSecs: Float
  totalUser: Float
  totalWorkTime: Float
  type: Float!
}

type OrganizationTemplateGQL {
  Organizations: [OrganizationGQL!]!
  count: Float
  description: String
  id: String!
  name: String!
}

type OrganizationUserGQL {
  Organization: OrganizationGQL
  User: UserGql
  count: Float
  isMaster: Boolean!
  organizationId: String!
  userId: String!
}

type OrganizationUserReportGQL {
  Organization: OrganizationGQL
  User: UserGql
  avgTotalWorkTime: Float
  count: Float
  isMaster: Boolean
  maxInvalid: Float
  minimalWorkTime: Float
  organizationId: String
  totalEarlySecs: Float
  totalEarlyTimes: Float
  totalInvalid: Float
  totalLateSecs: Float
  totalLateTimes: Float
  totalWorkDay: Float
  totalWorkTime: Float
  userId: String!
  userRank: Float
}

type PackingReportGQL {
  count: Float
  countIn: Float!
  countOut: Float!
  ocrPlate: String!
}

type PageGql {
  count: Int
  icon: String!
  index: Int!
  isCollapsible: Boolean!
  isNoTimeLimit: Boolean!
  label: String!
  layout: [LayoutGql!]
  path: String!
}

type PatientFaceImageBase64GQL {
  data: String!
  dateCreated: Date
  dateModified: Date
  faceImageType: String!
  id: String
  isClusterSync: Boolean
  path: String!
  patientId: String
  userId: String
}

type PatientGql {
  avatar: String!
  avatarBase64: String
  birthDay: Date!
  company: CompanyGql
  companyId: String
  count: Float
  dateCreated: Date
  dateModified: Date
  department: DepartmentGql
  departmentId: String
  faceImages: [FaceImageGraphQL!]
  faceImagesArrayBase64: [PatientFaceImageBase64GQL!]
  gender: Gender!
  id: String!
  integrationKey: String!
  name: String!
  status: String
}

type PermissionGql {
  canCread: Boolean!
  canDelete: Boolean!
  canRead: Boolean!
  canUpdate: Boolean!
  functionId: String!
  roleId: String!
}

type Query {
  DepartmentAdmins(departmentId: String!): [DepartmentAdminGql!]!
  DownloadFileReport(id: String!): CustomeReportJobGql!
  GetProcessedReport(page: Float!, perPage: Float!, search: String!): [CustomeReportJobGql!]!
  acAdmins(locationId: String!): [ACAdminGql!]!
  acDepTimes(departmentId: String!, locationId: String!): ACDepTimeGql!
  acDeps(locationId: String!, page: Float!, perPage: Float!, search: String!): [ACDepTimeGql!]!
  acDepsUsingTime(acTimeId: String!): [DepartmentGql!]!
  acDevicesInLocation(locationId: String!): [ACDeviceGql!]!
  acDevicesInLocationPage(locationId: String!, page: Float!, perPage: Float!, search: String!): [ACDeviceGql!]!
  acLocationById(locationId: String!): ACLocationGql!
  acLocations(page: Float!, perPage: Float!, search: String!): [ACLocationGql!]!
  acLocationssUsingTime(acTimeId: String!): [ACLocationGql!]!
  acUserTimes(locationId: String!, userId: String!): AcUserTimeGql!
  acUsers(locationId: String!, page: Float!, perPage: Float!, search: String!): [AcUserTimeGql!]!
  acUsersUsingTime(acTimeId: String!): [UserGql!]!
  adminShiftPage(departmentIds: [String!]!, endTime: Date!, listAll: Boolean!, page: Float!, perPage: Float!, shiftCodes: [String!], startTime: Date!, userIds: [String!]!): [ShiftUserGql!]!
  alertRulePage(alertLevels: [Float!]!, page: Float!, perPage: Float!, search: String!, types: [Float!]!): [AlertRuleGql!]!
  allACTimes: [AcTimeGql!]!
  allDevices: [DeviceGql!]!
  allRunningDevices: [DeviceGql!]!
  allServerConfig: [ServerConfigGql!]!
  allShiftUserReport(endDate: String, page: Float!, perPage: Float!, search: String!, startDate: String): [OrganizationUserReportGQL!]!
  allShiftUserWorkTimeReport(endDate: String, page: Float!, perPage: Float!, search: String!, startDate: String): [OrganizationUserReportGQL!]!
  allUnkownGroupByTrackingId(endTime: Date!, page: Float!, perPage: Float!, search: String!, startTime: Date!): [UnknownReportGql!]!
  autoCalculateAllFaceResults(companyIds: [String!], departmentIds: [String!], endDate: String, startDate: String): Float!
  calculateShiftWorkTimeTest: Float!
  cameraPage(page: Float!, perPage: Float!, search: String!): [CameraGql!]!
  camerasNotInDevice(deviceId: String!): [CameraGql!]!
  checkClusterConfig: Boolean!
  checkDisabledUser(adName: String!): CheckAdUserGQL!
  checkEmptyOrg: [OrganizationGQL!]!
  checkFaceMatchingResult(eventId: String!, limit: Float!): [FaceMatchingResultGql!]!
  checkFaceResultType(deviceIp: String!, time: Date!, userId: String!): Float!
  checkFcardUserId(deviceId: String!, userId: String!): Float!
  checkLastOut(deviceIdIn: String!, deviceIdOut: String!): [CheckLastOutGql!]!
  checkLdapUser(email: String!): Boolean!
  checkServerDiskHealth: Boolean!
  checkServerRamHealth: Boolean!
  checkTelegroup(deviceId: String!, time: Date!, types: [Float!]!, userId: String!): [String!]!
  checkUserAuthen: Boolean!
  companies(name: String = ""): [CompanyGql!]!
  companyByDepartment(departmentId: String!): CompanyGql!
  countACDepInLocation(locationId: String!): Float!
  countACDeviceInLocation(locationId: String!): Float!
  countACPatientInLocation(locationId: String!): Float!
  countACUserInLocation(locationId: String!): Float!
  countAcCameraByLocation(acLocationId: String!): Float!
  countActiveAiService: Float!
  countActiveCamera: Float!
  countActiveROI: Float!
  countActiveUser: Float!
  countAiServiceInDevice(cameraId: String!, deviceId: String!): AiServiceInDeviceGql!
  countAllUserOrgById(organizationId: String!): Float!
  countBidvActiveGuestByLocation(endTime: Date, locationId: String!, startTime: Date): Int!
  countBidvReidByLocation(endTime: Date, locationId: String!, startTime: Date): Int!
  countCamera: Float!
  countDeactiveUser: Float!
  countDevice: Float!
  countDeviceInLocation(locationId: String!): Int!
  countDisplay: Float!
  countFaceResUniqueByLocation(endTime: Date!, locationId: String!, startTime: Date!): Int!
  countGuest: Float!
  countGuestInDay(day: Date): Float!
  countGuestInLocation(locationId: String!): Int!
  countGuestInMonth(day: Date): Float!
  countMbAcUserWaitting(acId: String!): Int!
  countNonUniformByLocation(endTime: Date, locationId: String!, startTime: Date): Int!
  countProcessor: Float!
  countReIdInDay(day: Date): Float!
  countReIdInMonth(day: Date): Float!
  countUser: Float!
  countUserInDay(day: Date): Float!
  countUserInMonth(day: Date): Float!
  countVehicleInParking(endTime: Date!, startTime: Date!): [ReportVehicleByDay!]!
  currentUserCom: CompanyGql!
  departmentsInCompanies(companyIds: [String!]!): [DepartmentGql!]!
  departmentsInCompany(companyId: String!): [DepartmentGql!]!
  deviceCameraSnapshot(cameraId: String!, deviceId: String!): CameraSnapshotGql!
  deviceCatergory: [DeviceGql!]!
  devicePage(deviceStatus: Float!, deviceTypes: [Float!]!, deviceUpdateStatus: Float!, isAlive: Boolean, order: String!, page: Float!, perPage: Float!, search: String!, sortBy: String!): [DeviceGql!]!
  deviceRelayControl(channel: Int, deviceId: String!, mode: Int!): Float!
  exportAllCenterImage: Float!
  exportAllEmptyUser: [UserGql!]!
  exportFaceResultOnCall(companyIds: String!, departmentIds: String!, deviceIds: String!, endTime: String!, order: String!, startTime: String!, textSearch: String!, timekeepingOnly: String!, type: Float!): String!
  exportFaceResultToJSON(companyIds: String!, departmentIds: String!, deviceIds: String!, endTime: String!, startTime: String!, textSearch: String!, timekeepingOnly: String!, type: Float!): String!
  exportGuestFaceResult(companyIds: [String!], departmentIds: [String!], deviceIds: [String!], endTime: String!, order: [String!], startTime: String!, textSearch: String, timeZoneOffset: String, timekeepingOnly: Boolean, type: Float!): String!
  exportUserWithAllImage(companyId: String!): ExportAllGql!
  fCardTzToGql(tzId: String!): FcardTimeZoneGql!
  faceMatchingEventPage(page: Float!, perPage: Float!): [FaceSearchEventGql!]!
  faceResultGuestPage(cameraIds: [String!], companyIds: [String!], departmentIds: [String!], deviceIds: [String!], endTime: Date!, page: Float, perPage: Float, startTime: Date!, textSearch: String, type: Float!): [FaceResultGuestGql!]!
  filterCameraByRole(cameraIds: [String!]!): [String!]!
  filterDeviceByRole(deviceIds: [String!]!, userId: String!): [String!]!
  findAlertRuleDevice(page: Float!, perPage: Float!, ruleId: String!, search: String, type: Float!): [AlertScriptGql!]!
  findAllNoShiftUser(endDate: Date!, startDate: Date!): [UserGql!]!
  findAllShiftDefaultNotAssigned(endDate: String!, startDate: String!): [DefaultNotAssignedGql!]!
  findCompanies(name: String!): [CompanyGql!]!
  findDepartments(companyIds: [String!]!, query: String!): [DepartmentGql!]!
  findFeedbackByUserId(endTime: Date!, page: Float!, perPage: Float!, search: String!, startTime: Date!): [UserFeedbackGql!]!
  findMultipleShiftUserInDay(endDate: Date!, startDate: Date!): [ShiftUserGql!]!
  findOrganizationTemplate(page: Float!, perPage: Float!, search: String!): [OrganizationTemplateGQL!]!
  findUsersRankByTitle(organizationIds: [String!], userRank: Float!): [OrganizationUserGQL!]!
  getAcGuestTimesByGuestId(guestId: String!, locationIds: [String!]!, page: Float!, perPage: Float!, timeIds: [String!]!): [AcGuestTimeGql!]!
  getAcGuestTimesByLocationId(guestIds: [String!]!, locationId: String!, page: Float!, perPage: Float!, timeIds: [String!]!): [AcGuestTimeGql!]!
  getAcLogDetail(deviceId: String!, localId: String!): AccessControlOpenLogGql!
  getAcPatientTimesByLocationId(locationId: String!, page: Float!, patientIds: [String!]!, perPage: Float!, timeIds: [String!]!): [AcPatientTimeGql!]!
  getAcPatientTimesByPatientId(locationIds: [String!]!, page: Float!, patientId: String!, perPage: Float!, timeIds: [String!]!): [AcPatientTimeGql!]!
  getAcTimeInLocation(locationIds: [String!]!): [AcTimeGql!]!
  getAcUserById(acId: String!, userId: String!): AccessControlUserGql!
  getActiveTelegramBot: TelegramBotGql!
  getAiBoxConfig(deviceId: String!): AiBoxConfigGql!
  getAiBoxConfigDisplay(deviceId: String!): AiBoxConfigGql!
  getAlarmEventByFilter(endTime: Date, page: Float, perPage: Float, startTime: Date, text: String, types: [Float!]): [CoreAiAlarmEventGql!]!
  getAllAttandantTime: [AttandantTimeGql!]!
  getAllBackupJobs(page: Float!, perPage: Float!, tables: String, type: String!): [BackupJobGql!]!
  getAllBackupTables: [String!]!
  getAllChildOrgById(organizationId: String!): [OrganizationGQL!]!
  getAllCronJobs(search: String!): [CronJobMonitorGql!]!
  getAllDeviceRunningCamera(cameraId: String!): [DeviceGql!]!
  getAllDevicesInParkingLane(parkingId: String!): [VehiclePackingDeviceGql!]!
  getAllFaceResultInCached(deviceId: String!, page: Int, perPage: Int, time: Date, trackId: String!): [CachedImageResultGql!]!
  getAllFaceResultInReId(deviceId: String!, endTime: Date, page: Int, perPage: Int, startTime: Date, time: Date, trackId: String!): [FaceResultGql!]!
  getAllFaceResultInTrack(deviceId: String!, endTime: Date, page: Int, perPage: Int, startTime: Date, time: Date, trackId: String!): [FaceResultGql!]!
  getAllIntegrationLogFiles: [String!]!
  getAllInterval(search: String!): [CronJobMonitorGql!]!
  getAllNewAlertMess(page: Float!, perPage: Float!, search: String!, type: String!): [AlertMessageGql!]!
  getAllOrgByUserId(page: Float!, perPage: Float!, userId: String!): [OrganizationGQL!]!
  getAllOrgTemplateOrgId(templateId: String!): [String!]!
  getAllScanJob(page: Float!, perPage: Float!): [FaceImageRescanJobGql!]!
  getAllTelegramBot(page: Float!, perPage: Float!, search: String): [TelegramBotGql!]!
  getAllTelegramReceiverInfo(page: Float!, perPage: Float!, search: String): [TelegramInfoGql!]!
  getAllUnknownAlarmEnabledDevices: [DeviceGql!]!
  getAttandantTimeByPage(page: Int!, perPage: Int!, search: String!): [AttandantTimeGql!]!
  getAttandantTimeProfile(attendantTimeId: String!): AttandantTimeGql!
  getBidvDashboard(endTime: Date, startTime: Date): [BidvDashboardGql!]!
  getBidvUserMovementHistory(endDate: Date!, filterUserId: String!, page: Float!, perPage: Float!, startDate: Date!): [BidvUserMovementHistoryGql!]!
  getCalculatedWorkMonth(month: String!, userId: String): CalculatedWorkMonthGql!
  getCalculatedWorkRange(endDay: String!, startDay: String!, userId: String): CalculatedWorkMonthGql!
  getCameraProfile(cameraId: String!): CameraGql!
  getCameraRoiConfig(aiServiceType: Float, cameraId: String!, cameraRoiId: String, deviceId: String!): [CameraROIGql!]!
  getCameraSnapshot(cameraId: String!): String!
  getChilOrganiztionById(organizationId: String!): [OrganizationGQL!]!
  getComRemoteLimit(page: Float!, perPage: Float!, search: String!): [ComRemoteLimitGQL!]!
  getCoreAiQueueInfo(deviceId: String!): CoreAiQueueInfoGql!
  getDataToJSON: [Float!]!
  getDepRemoteLimit(page: Float!, perPage: Float!, search: String!): [DepRemoteLimitGQL!]!
  getDetailReIdInVideo(cameraId: String!, reId: String!): [VideoAnalyticReIdResultGql!]!
  getDeviceAdmins(deviceId: String!, search: String!): [UserGql!]!
  getDeviceByCompany(page: Int!, perPage: Int!, search: String!): [DeviceGql!]!
  getDeviceCameras(deviceId: String!, search: String!): [DeviceCameraGql!]!
  getDeviceGroupById(deviceGroupId: String!): DeviceGroupInfoGql!
  getDeviceInLocation(locationId: String!): [DeviceGql!]!
  getDeviceNotUsingAttandantTime(attandantId: String!): [DeviceGql!]!
  getDeviceRoiConfig(deviceId: String!, deviceRoiId: String): [DeviceRoiGql!]!
  getDeviceSignalLog(deviceId: String!, endTime: String!, startTime: String!): [DeviceSignalLogGql!]!
  getDeviceSnapshot(cameraId: String, deviceId: String!): String!
  getDeviceType4Config(deviceId: String!): DeviceType4ConfigGql!
  getDeviceUsingAttandantTime(attandantId: String!): [DeviceAttandantTimeGql!]!
  getEventFaceCapturedHumans(confident: Float = 0, eventId: String!, page: Float!, perPage: Float!): [SmartSearchFaceResponseGql!]!
  getEventFaceResults(eventId: String!, page: Float!, perPage: Float!): [SmartSearchFaceResultGql!]!
  getEventFaceTrackInfoResults(confident: Float = 0, eventId: String!, page: Float!, perPage: Float!, personCaptureId: String!): SmartSearchFaceResponseGql!
  getEventFaceTrackResults(confident: Float = 0, eventId: String!, page: Float!, perPage: Float!, personCaptureId: String!, trackId: String!): SmartSearchFaceResponseGql!
  getEventMctCapturedHumans(confident: Float = 0, eventId: String!, page: Float!, perPage: Float!): [SmartSearchMctResponseGql!]!
  getEventMctResults(eventId: String!, page: Float!, perPage: Float!): [SmartSearchMctResponseGql!]!
  getEventMctTrackInfoResults(confident: Float = 0, eventId: String!, page: Float!, perPage: Float!, personCaptureId: String!): SmartSearchMctResponseGql!
  getEventMctTrackResults(confident: Float = 0, eventId: String!, page: Float!, perPage: Float!, personCaptureId: String!, trackId: String!): SmartSearchMctResponseGql!
  getEventUserResults(confident: Float = 0, eventId: String!, page: Float!, perPage: Float!): [SmartSearchUserResultGql!]!
  getFaceReIdGroup(cameraIds: [String!]!, deviceIds: [String!]!, endTime: Date!, maxReIdConf: Float, minReIdConf: Float, page: Float!, perPage: Float!, search: String, startTime: Date!): [UnknownReportGql!]!
  getFaceResultById(id: Float!): FaceResultGql!
  getFaceResultInTrack(deviceId: String!, trackId: String!): FaceResultGql!
  getFaceTerminalConfig(deviceId: String!): FaceTerminalConfigGql!
  getFaceTrackAttribute(deviceId: String!, trackId: String!): [FaceTrackGql!]!
  getFaceTrackPage(cameraIds: [String!], deviceIds: [String!], endTime: Date!, isShowUnknownResult: Boolean, page: Float!, perPage: Float!, startTime: Date!, textSearch: String!, type: Float!): [FaceTrackGql!]!
  getFingerPrintByUserId(id: String): [FingerPrintGql!]!
  getFirstOrderOrganization: [OrganizationGQL!]!
  getGstConfig(deviceId: String!): DeviceDetectorConfigStatusGql!
  getGuestInLocation(locationId: String!): [UserGuestGql!]!
  getHistoryByUserId(deviceIds: [String!]!, endTime: Date!, page: Float!, perPage: Float!, startTime: Date!, userId: String): [FaceResultGql!]!
  getHistoryDetailById(endTime: Date!, guestId: String!, startTime: Date!): [FaceResultGuestGql!]!
  getHumanTrackingPage(attributeFilter: [Float!]!, cameraIds: [String!], colorFilter: [String!], deviceIds: [String!], endDate: Date!, page: Float!, perPage: Float!, startDate: Date!): [HumanTrackInfoGql!]!
  getHumanTrackingResultPage(colorFilter: [String!], deviceId: String!, page: Float!, perPage: Float!, trackingId: String!): [HumanTrackResultGql!]!
  getIdCardByUserId(user_id: String!): [IdCardGQL!]!
  getIntegratedCustomer(page: Int!, perPage: Int!, search: String!): [IparkingCustomerGql!]!
  getIntegratedIdentity(page: Int!, perPage: Int!, search: String!): [IparkingCustomerIdentityGql!]!
  getIntegratedIdentityGroups(page: Int!, perPage: Int!, search: String!): [IparkingGroupGql!]!
  getIntegratedLane(page: Int!, perPage: Int!, search: String!): [IparkingLaneGql!]!
  getIntegratedVehicle(page: Int!, perPage: Int!, search: String!): [IparkingVehicleGql!]!
  getIparkingIntegration(isActive: Boolean, page: Int!, perPage: Int!, search: String!): [IparkingConfigGql!]!
  getIparkingVehicle(customerIds: [String!]!, page: Int!, perPage: Int!, search: String!, type: Int): [IparkingVehicleGql!]!
  getLaneHistoryPage(laneIds: [String!], page: Int!, perPage: Int!, search: String!): [VehiclePackingResultGql!]!
  getLastFaceId: FaceResultGql!
  getLastHistoryByLaneId(laneId: String!): VehiclePackingResultGql!
  getLastResultsByLaneId(laneId: String!): VehiclePackingResultGql!
  getLicenseAllowPage(page: Int!, perPage: Int!, search: String!): [VehiclePackingLicenseAllowGql!]!
  getMbAcUserWaittingDetail(acId: String!, userId: String!): AccessControlUserGql!
  getMbUserRankConfig: [MbUserRankConfigGql!]!
  getMqttAddIn(page: Float!, perPage: Float!, search: String!, type: Float): [MqttConfigGql!]!
  getMqttConfigDevices(configId: String!, search: String!): [MqttConfigDevicesGql!]!
  getNewAlertMess(page: Float!, perPage: Float!, search: String!): [AlertMessageGql!]!
  getNonUniformHistoryByUserId(endDate: Date!, filterUserId: String!, page: Float!, perPage: Float!, startDate: Date!): [HumanTrackInfoGql!]!
  getNonUniformPage(attFilterMode: Int, cameraIds: [String!], comIds: [String!], depIds: [String!], deviceIds: [String!], endDate: Date!, isFaceRecognizedOnly: Boolean, locationIds: [String!], page: Float!, perPage: Float!, startDate: Date!, userIds: [String!], violationMode: Int): [HumanTrackInfoGql!]!
  getOrgRemoteLimit(page: Float!, perPage: Float!, search: String!): [OrgRemoteLimitGQL!]!
  getOrgUserInfos(organizationId: String!): OrganizationGQL!
  getOrganizationPage(orgType: Float, page: Float!, perPage: Float!, search: String!): [OrganizationGQL!]!
  getOrphanResultInTrack(deviceId: String!, page: Float!, perPage: Float!, trackId: String!): [FaceResultGql!]!
  getParkingLanePage(barrieModes: [Int!]!, laneTypes: [Int!]!, page: Int!, perPage: Int!, search: String!, vehicleTypes: [Int!]!): [VehicleParkingLaneGQL!]!
  getRemoteApproverCom(approverId: String!, page: Float!, perPage: Float!, search: String!): [CompanyGql!]!
  getRemoteApproverDep(approverId: String!, page: Float!, perPage: Float!, search: String!): [DepartmentGql!]!
  getRemoteApproverOrg(approverId: String!, page: Float!, perPage: Float!, search: String!): [OrganizationGQL!]!
  getRemoteApproverUser(approverId: String!, page: Float!, perPage: Float!, search: String!): [UserGql!]!
  getRemoteCheckResults(endDate: Date!, isSuperAdminView: Boolean, page: Float!, perPage: Float!, search: String!, startDate: Date!, status: Float): [FaceResultGql!]!
  getRemoteComAdmin(adminId: String!, page: Float!, perPage: Float!, search: String!): [CompanyGql!]!
  getRemoteDepAdmin(adminId: String!, page: Float!, perPage: Float!, search: String!): [DepartmentGql!]!
  getRemoteOrgAdmin(adminId: String!, page: Float!, perPage: Float!, search: String!): [OrganizationGQL!]!
  getRemoteRecognizeEnableUser(companyIds: [String!], departmentIds: [String!], organizationIds: [String!], page: Float!, perPage: Float!, search: String!): [UserGql!]!
  getRemoteUserAdmin(adminId: String!, page: Float!, perPage: Float!, search: String!): [UserGql!]!
  getReportUserFilter(id: String!): ReportUserFilterGql!
  getReportUserFilterCompany(filterId: String!, page: Float!, perPage: Float!, search: String!): [ReportUserFilterCompanyGql!]!
  getReportUserFilterDepartment(filterId: String!, page: Float!, perPage: Float!, search: String!): [ReportUserFilterDepartmentGql!]!
  getReportUserFilterExRuleUser(filterId: String!, page: Float!, perPage: Float!, search: String!): [ReportUserFilterExRuleUserGql!]!
  getReportUserFilterExUser(filterId: String!, page: Float!, perPage: Float!, search: String!): [ReportUserFilterExUserGql!]!
  getReportUserFilterUser(filterId: String!, page: Float!, perPage: Float!, search: String!): [ReportUserFilterUserGql!]!
  getServerUsage: ServerUsageGql!
  getShiftReport(departments: String, shiftIds: String, time: Date, users: String): String!
  getShifts: [ShiftTypeGQL!]!
  getShiftsByPage(page: Float!, perPage: Float!, search: String!): [ShiftTypeGQL!]!
  getTelegramGroupById(groupId: String!): TelegramGroupGQL!
  getTimeKeepingArroungId(recognizedUserId: String!, time: Date!): [FaceResultGql!]!
  getTotalAttDevice(attInfoId: String!): Int!
  getTotalAttUser(attInfoId: String!): Int!
  getTotalCurrentUser(today: Date): InOutDataGql!
  getUnattGuest(attInfoId: String!, page: Int!, perPage: Int!): [AttGuestGql!]!
  getUnattUnknown(attInfoId: String!, page: Int!, perPage: Int!): [UnknownReportGql!]!
  getUnattUser(attInfoId: String!, page: Int!, perPage: Int!): [AttUserGql!]!
  getUnknown(cameraIds: [String!]!, deviceIds: [String!]!, endTime: Date!, page: Float!, perPage: Float!, startTime: Date!): [UnknownReportGql!]!
  getUnknownById(id: Int!): FaceResultGql!
  getUnknownGroup(cameraIds: [String!]!, deviceIds: [String!]!, endTime: Date!, page: Float!, perPage: Float!, search: String, startTime: Date!): [UnknownReportGql!]!
  getUserByDay(endTime: Date!, startTime: Date!, timeZoneOffset: String!): UserReportGql!
  getUserByHour(day: Date!, timeZoneOffset: String!): UserReportGql!
  getUserByPositionByDay(day: String!, timeZoneOffset: String!, userId: String!): UserReportGql!
  getUserEkycIntegrationById(userId: String): EkycIntegrationUserGql!
  getUserFaceGeneralReportByDay(companyId: String, date: String!, departmentId: String, timeZoneOffset: String, userId: String): [FaceResultGeneralReportGql!]!
  getUserFaceGeneralReportByDays(companyId: String, departmentId: String, endDate: String!, startDate: String!, timeZoneOffset: String, userId: String): [FaceResultGeneralReportGql!]!
  getUserImageProfile(userId: String): [FaceImageGraphQL!]!
  getUserLogData(endTime: Date!, startTime: Date!, timeZoneOffset: String!, userId: String!): UserReportGql!
  getUserRankById(userId: String!): Float!
  getUserRankByTitle(companyType: Int!, title: String!): Float!
  getUserRemoteCheckResults(endDate: Date!, page: Float!, perPage: Float!, startDate: Date!, status: Float): [FaceResultGql!]!
  getUserRemoteLimit(time: Date, userId: String): [FaceResultGql!]!
  getUserReportByDay(month: Date!, timeZoneOffset: String!): [Report!]!
  getUserReportByHour(date: Date!, timeZoneOffset: String!): [Report!]!
  getUserTimeReports(companyIds: [String!]!, departmentIds: [String!]!, endDate: String!, page: Float!, perPage: Float!, startDate: String!, userIds: [String!]!): [UserTimeReportGql!]!
  getUsersByTimeInterval(endTime: Date!, startTime: Date!, timeZoneOffset: String): [UserReportGql!]!
  getVehicleCapturedByEventId(eventId: String!, minConfident: Float!): [VehicleSearchCapturedGql!]!
  getVehicleImageResultByTrackId(captureId: String!, minConfident: Float!, resultNumber: Float!, trackId: String!): [VehicleTrackResultGql!]!
  getVehicleInOut(endTime: Date!, startTime: Date!): [VehicleInOutGql!]!
  getVehicleInOutByGroup(endTime: Date!, startTime: Date!): IparkingReportGql!
  getVehicleInOutByTime(endTime: Date!, startTime: Date!): IparkingReportGql!
  getVehicleSearchResultById(eventId: String!, minConfident: Float!, resultNumber: Float!): [VehicleSearchCapturedGql!]!
  getVehicleTrackPageByCaptured(captureId: String!, minConfident: Float!, page: Float!, perPage: Float!): [VehicleTrackInfoGql!]!
  getVehicleTrackResultById(imageLimit: Float!, trackId: String!): [VehicleTrackResultGql!]!
  getVideoAnalyticList(deviceIds: [String!]!, endTime: Date!, page: Float!, perPage: Float!, search: String!, startTime: Date!): [VideoAnalyticGql!]!
  getVideoAnalyticSummary(cameraId: String!, deviceId: String!, endTime: Date!, search: String!, startTime: Date!): [VideoAnalyticGql!]!
  getVideoGroupAnalyticSummary(groupIds: [String!]!, search: String!): [VideoAnalyticSummaryGql!]!
  haftDayAbsentReport(endDate: Date!, startDate: Date!): [CheckInOutResultSql!]!
  lastTimekeeping: FaceResultGql!
  licenseParkingLeftIn(endDate: String!, startDate: String!): [PackingReportGQL!]!
  listPermissions: [PermissionGql!]!
  lockFaceImageStatus(userId: String!): String!
  login(email: String!, password: String!, type: Float): UserGql
  loginIam(email: String!): UserGql
  mannualRotate: [String!]!
  monthQueryShift(endTime: Date!, startTime: Date!, userId: String!): [ShiftUserMonthlyGql!]!
  queryFunctionId: [FunctionGql!]!
  queryLayout: [LayoutGql!]!
  queryNotifications(page: Float!, perPage: Float!): [NotificationGql!]!
  queryPage: [PageGql!]!
  queryShift(endTime: Date!, startTime: Date!, userId: String!): [ShiftUserGql!]!
  querySystemLog(endDate: Date!, functionIds: [String!]!, page: Int!, perPage: Int!, search: String, startDate: Date!, type: [String!]!, userIds: [String!]!): [SystemLogGql!]!
  queryUserAttendantTimeByPageForAdminPage(attendantIds: [String!]!, deviceGroupIds: [String!]!, deviceIds: [String!]!, endDate: Date!, page: Int!, perPage: Int!, startDate: Date!, userIds: [String!]!): [UserAttandantTimeGql!]!
  queryUserAttendantTimeByPageRealTime(attendantIds: [String!]!, deviceGroupIds: [String!]!, deviceIds: [String!]!, endDate: Date!, page: Int!, perPage: Int!, startDate: Date!, userIds: [String!]!): [UserAttandantTimeGql!]!
  queryUserTimeReportByRange(companyIds: [String!], departmentIds: [String!], deviceIds: [String!], endDate: String!, isSelectFullDay: Boolean, startDate: String!, userIds: [String!]): [UserTimeReportGql!]!
  reportAllAge(deviceIds: [String!], endTime: Date!, startTime: Date!): [AgeReportGql!]!
  reportAllEmotion(deviceIds: [String!], endTime: Date!, startTime: Date!): [EmotionReportGql!]!
  reportAllOrgTemplateOrgId(endDate: Date!, startDate: Date!, templateId: String!): [OrganizationGQL!]!
  reportAllOrgTemplateOrgIdWorkTime(endDate: Date!, startDate: Date!, templateId: String!): [OrganizationGQL!]!
  reportAllRemoteResultDayOnly(endDate: String!, startDate: String!): [UserGql!]!
  reportAllShiftUser(endDate: Date!, startDate: Date!): [ShiftUserGql!]!
  reportAvgOrgAvgWorktimeByMultipleId(endDate: Date!, organizationIds: [String!]!, startDate: Date!): [OrganizationGQL!]!
  reportEmptyResultReportOnly(companyIds: [String!], departmentIds: [String!], deviceIds: [String!], endDate: String!, startDate: String!, userIds: [String!]): [UserTimeReportGql!]!
  reportFaceByDayGroupByAc(day: Date): [SecurityReportGQL!]!
  reportFaceByMonthGroupByAc(day: Date): [SecurityReportGQL!]!
  reportFullDayUserTimeReport(companyIds: [String!], departmentIds: [String!], deviceIds: [String!], endDate: String!, startDate: String!, userIds: [String!]): [UserTimeReportGql!]!
  reportOrgWorktimeById(endDate: Date!, organizationId: String!, startDate: Date!): OrganizationGQL!
  reportOrgWorktimeByMultipleId(endDate: Date!, organizationIds: [String!]!, startDate: Date!): [OrganizationGQL!]!
  reportOrganiztionById(endDate: Date!, organizationId: String!, startDate: Date!): OrganizationGQL!
  reportOrganiztionByMultipleId(endDate: Date!, organizationIds: [String!]!, startDate: Date!): [OrganizationGQL!]!
  reportTimekeeping(companyIds: [String!], departmentIds: [String!], deviceIds: [String!], endTime: Date!, page: Float!, perPage: Float!, startTime: Date!, textSearch: String!, timekeepingOnly: Boolean, type: Float!): [FaceResultGql!]!
  reportTotalFaceByDay(day: Date): SecurityReportGQL!
  reportTotalFaceByMonth(day: Date): [SecurityReportGQL!]!
  roles: [RoleGql!]!
  scanAllUser: [CheckAdUserGQL!]!
  search(input: UserSearchInput!): [UserGql!]!
  searchAC(page: Float!, perPage: Float!, search: String!): [AccessControlGql!]!
  searchAcCamera(acLocationId: String!, page: Int!, perPage: Int!, search: String!): [AcCameraGql!]!
  searchAcCom(acId: String!, page: Float!, perPage: Float!, search: String!): [AccessControlCompanyGql!]!
  searchAcDep(acId: String!, page: Float!, perPage: Float!, search: String!): [AccessControlDepartmentGql!]!
  searchAcDevice(acId: String!, page: Float!, perPage: Float!, search: String!): [AccessControlDeviceGql!]!
  searchAcDeviceGroup(acId: String!, page: Float!, perPage: Float!, search: String!): [AccessControlDeviceGroupGql!]!
  searchAcLog(acLocationIds: [String!], deviceIds: [String!], endTime: Date!, isOpen: Boolean, page: Int!, perPage: Int!, startTime: Date!, types: [Int!]): [AccessControlOpenLogGql!]!
  searchAcOrg(acId: String!, page: Float!, perPage: Float!, search: String!): [AccessControlOrgGql!]!
  searchAcTime(acId: String!, page: Int!, perPage: Int!, search: String!): [AccessControlTimeGql!]!
  searchAcUser(acId: String!, page: Float!, perPage: Float!, search: String!): [AccessControlUserGql!]!
  searchAccessControlAlert(acAlertTypes: [Int!], acIds: [String!]!, endTime: Date!, isRead: Boolean, page: Int!, perPage: Int!, search: String!, startTime: Date!): [AlertMessageGql!]!
  searchAccessControlCamera(acId: String!, page: Float!, perPage: Float!, search: String!): [AccessControlCameraGql!]!
  searchAiService(page: Float!, perPage: Float!, search: String!, type: [Float!]!): [AiServiceGql!]!
  searchAllAC(search: String!): [AccessControlGql!]!
  searchAllAlert(page: Float!, perPage: Float!, search: String!): [AlertMessageGql!]!
  searchAllDevice(deviceStatus: Float!, deviceTypes: [Float!]!, deviceUpdateStatus: Float!, search: String!): [DeviceGql!]!
  searchApproveHistory(remoteId: Float!): RemoteFaceResultGQL!
  searchAttDevice(attInfoId: String!, page: Int!, perPage: Int!, search: String!): [AttDeviceGql!]!
  searchAttGuest(attInfoId: String!, isCheck: Boolean, page: Int!, perPage: Int!, search: String!): [AttGuestGql!]!
  searchAttInfo(cameraIds: [String!], deviceIds: [String!], endTime: Date, page: Int!, perPage: Int!, search: String!, startTime: Date): [AttInfoGql!]!
  searchAttUser(attInfoId: String!, isCheck: Boolean, page: Int!, perPage: Int!, search: String!): [AttUserGql!]!
  searchAttUserInDay(companyIds: [String!], departmentIds: [String!], deviceIds: [String!], endTime: Date, isAtt: Boolean, page: Float, perPage: Float, search: String, startTime: Date): [UserGql!]!
  searchAttandantTime(page: Float!, perPage: Float!, search: String!): [AttandantTimeGql!]!
  searchBlackListUser(companyIds: [String!]!, departmentIds: [String!]!, page: Float!, perPage: Float!, search: String!, status: Float): [UserGql!]!
  searchCachedAcMap(search: String!): [String!]!
  searchCachedGroup(cameraIds: [String!], deviceIds: [String!], endTime: Date!, isCoreAiResponse: Boolean, isSentCoreAi: Boolean, page: Int!, perPage: Int!, search: String!, startTime: Date!): [CachedImageResultGql!]!
  searchCachedPersonResults(cameraIds: [String!], deviceIds: [String!], endTime: Date!, isCoreAiResponse: Boolean, isSentCoreAi: Boolean, page: Int!, perPage: Int!, search: String!, startTime: Date!): [CachedImageResultGql!]!
  searchCachedUnknownFace(cameraIds: [String!], deviceIds: [String!], endTime: Date!, isCoreAiResponse: Boolean, isSentCoreAi: Boolean, page: Int!, perPage: Int!, search: String!, startTime: Date!): [CachedImageResultGql!]!
  searchCivamsCustomer(page: Int!, perPage: Int!, search: String!): [IparkingCustomerGql!]!
  searchCivamsIdentity(page: Int!, perPage: Int!, search: String!, status: String, type: Int): [IparkingCustomerIdentityGql!]!
  searchCivamsIdentityGroup(page: Int!, perPage: Int!, plateNumberComparison: Float, plateNumberValidation: Float, search: String!, type: String, vehicleType: Float): [IparkingGroupGql!]!
  searchCivamsLane(page: Int!, perPage: Int!, search: String!, type: Int): [IparkingLaneGql!]!
  searchCompanyPage(page: Float!, perPage: Float!, search: String!): [CompanyGql!]!
  searchDefaultShiftType(page: Float!, perPage: Float!, search: String!, shiftId: [String!], viewMode: Float): [ShiftTypeDefaultGql!]!
  searchDepartmentPage(companyIds: [String!]!, page: Float!, perPage: Float!, search: String!): [DepartmentGql!]!
  searchDeviceGroupInfo(page: Int!, perPage: Int!, search: String!): [DeviceGroupInfoGql!]!
  searchDeviceInGroup(deviceGroupId: String!, page: Int!, perPage: Int!, search: String!): [DeviceGql!]!
  searchDeviceNotInGroup(deviceGroupId: String!, deviceTypes: [Float!]!, page: Int!, perPage: Int!, search: String!): [DeviceGql!]!
  searchDeviceSyncLog(page: Float!, perPage: Float!, search: String!): [DeviceSyncLogGql!]!
  searchEarlyOnly(comIds: [String!], depIds: [String!], endDate: String, endHour: String, startDate: String, userIds: [String!]): [OffTimeUserReportGql!]!
  searchEarlyOrNotOut(comIds: [String!], depIds: [String!], endDate: String, endHour: String, startDate: String, userIds: [String!]): [OffTimeUserReportGql!]!
  searchEventVideoAiBox(cameraIds: [String!], cameraIps: [String!], deviceIds: [String!], endTime: Date!, page: Float!, perPage: Float!, startTime: Date!): [EventVideoAiBoxGql!]!
  searchFaceIntegrationConfig(page: Int!, perPage: Int!, search: String!, types: [Int!]!): [FaceIntegrationConfigGql!]!
  searchFakeResult(endDate: Date!, page: Float!, perPage: Float!, startDate: Date!, userId: String): [FakeFaceResultGql!]!
  searchFeatureImagePage(confident: [Float!], endTime: Date!, page: Float!, perPage: Float!, startTime: Date!, status: Float): [SearchByFeatureGql!]!
  searchGuestACLocations(page: Float!, perPage: Float!, search: String!): [ACLocationGql!]!
  searchGuestById(guestId: String!): UserGuestGql!
  searchHumanAction(actionTypes: [Float!]!, endTime: Date!, page: Float!, perPage: Float!, startTime: Date!): [HumanActionGql!]!
  searchIgnoreReId(page: Float!, perPage: Float!, search: String!): [IgnoreReIdGql!]!
  searchImportJob(endTime: Date!, jobType: Int, page: Int!, perPage: Int!, search: String!, startTime: Date!): [DataImportJobGql!]!
  searchImportJobRow(isErr: Boolean, jobId: String!, page: Int!, perPage: Int!, processed: Boolean, search: String!): [DataImportRowGql!]!
  searchInOutViolations(comIds: [String!], depIds: [String!], endDate: String, startDate: String, userIds: [String!], violationType: Float): [OffTimeUserReportGql!]!
  searchLateOnly(comIds: [String!], depIds: [String!], endDate: String, startDate: String, startHour: String, userIds: [String!]): [OffTimeUserReportGql!]!
  searchLateOrNotIn(comIds: [String!], depIds: [String!], endDate: String, startDate: String, startHour: String, userIds: [String!]): [OffTimeUserReportGql!]!
  searchLocation(page: Float!, perPage: Float!, search: String!): [LocationGQL!]!
  searchMbAcUser(acId: String!, page: Float!, perPage: Float!, search: String!): AccessControlInfoModalGql!
  searchMbAcUserWaitting(acIds: [String!], page: Int!, perPage: Int!, search: String!): [AccessControlUserGql!]!
  searchMbAccessControl(page: Int!, perPage: Int!, search: String!): [AccessControlGql!]!
  searchNoShiftUser(comIds: [String!], depIds: [String!], endDate: Date!, search: String, startDate: Date!, userIds: [String!]): [UserGql!]!
  searchNotificationTime(name: String!, page: Float!, perPage: Float!): [NotificationTimeGql!]!
  searchOrgByType(orgLevel: Float!, search: String): [OrganizationGQL!]!
  searchOrgMaster(orgIds: [String!]!, page: Float!, perPage: Float!, search: String!): [OrganizationUserGQL!]!
  searchOrganizationUser(endDate: String, organizationId: String!, page: Float!, perPage: Float!, search: String!, sort: Float, startDate: String): [OrganizationUserReportGQL!]!
  searchPatientACLocations(page: Float!, perPage: Float!, search: String!): [ACLocationGql!]!
  searchPatientById(patientId: String!): PatientGql!
  searchPatientByKey(key: String!): PatientGql!
  searchPatientPage(companyIds: [String!], departmentIds: [String!], page: Float!, perPage: Float!, search: String!, status: Float): [PatientGql!]!
  searchRankUser(endDate: String, organizationIds: [String!], page: Float!, perPage: Float!, search: String!, sort: Float, startDate: String, userRank: Float!): [OrganizationUserReportGQL!]!
  searchReIdLabel(page: Float!, perPage: Float!, search: String!): [ReIdLabelGql!]!
  searchRemoteAdmin(comIds: [String!], depIds: [String!], orgIds: [String!], page: Float!, perPage: Float!, search: String!, userIds: [String!]): [RemoteAdminGQL!]!
  searchRemoteApprover(comIds: [String!], depIds: [String!], orgIds: [String!], page: Float!, perPage: Float!, search: String!, userIds: [String!]): [RemoteApproverGQL!]!
  searchRemoteResultDayOnly(endDate: String!, page: Float!, perPage: Float!, search: String!, startDate: String!): [UserGql!]!
  searchReportUserFilter(page: Float!, perPage: Float!, search: String!): [ReportUserFilterGql!]!
  searchS2tResult(endSec: Int!, startDec: Int!, videoId: String!): [AudioAnalyticResultGql!]!
  searchServerConfig(search: String!): [ServerConfigGql!]!
  searchShiftUserExplain(departmentIds: [String!]!, endSearch: Date!, explainSearch: String!, explainStatus: Float!, explainType: Float!, page: Float!, perPage: Float!, startSearch: Date!, userIds: [String!]!): [ShiftUserExplainGql!]!
  searchShiftUserExplainAdmin(companyIds: [String!]!, departmentIds: [String!]!, page: Float!, perPage: Float!, search: String!): [ShiftUserExplainAdminGroupedGql!]!
  searchStudentPage(classCode: String, className: String, grade: String, majorCode: String, majorName: String, page: Float!, perPage: Float!, search: String!): [StudentGql!]!
  searchTelegramGroup(page: Float!, perPage: Float!, search: String!): [TelegramGroupGQL!]!
  searchUnbindingCompany(page: Int!, perPage: Int!, search: String!): [CompanyGql!]!
  searchUploadVideo(endTime: Date!, groupIds: [String!]!, page: Float!, perPage: Float!, search: String!, startTime: Date!): [UploadVideoAnalyticGql!]!
  searchUser(input: UserSearchInput!): [UserGql!]!
  searchUserEkycIntegration(page: Int!, perPage: Int!, search: String!): [EkycIntegrationUserGql!]!
  searchUserGuestPage(companyIds: [String!]!, departmentIds: [String!]!, guestStatus: Float!, page: Float!, perPage: Float!, search: String!): [UserGuestGql!]!
  searchUserSyncError(deviceId: String, page: Int!, perPage: Int!, search: String!, userIds: [String!]): [DeviceSyncLogGql!]!
  searchVehicleTrack(colorFilter: String!, endDate: Date!, page: Float!, perPage: Float!, search: String!, showMode: Int, startDate: Date!, vehicleType: Int): [VehicleTrackInfoGql!]!
  searchVideoAnalyticGroup(page: Float!, perPage: Float!, search: String!, types: [String!]!): [VideoAnalyticGroupGql!]!
  searchVipUser(companyIds: [String!]!, departmentIds: [String!]!, page: Float!, perPage: Float!, search: String!, status: Float): [UserGql!]!
  sendTestEmail: Boolean!
  setDataFromJSON: [Float!]!
  streamStatusByDevices: [StreamStatusDashboardGql!]!
  summaryAlarmEvent(endDate: Date, startDate: Date): [AlarmEventSumaryGQL!]!
  testDailyReportScheduler(date: String!): Float!
  testGuestAc(cameraId: String!, deviceId: String!, guestId: String!): String!
  testHourlyReportScheduler(time: String): Float!
  testLiveStream(cameraId: String, time: Date!): [EventVideoGql!]!
  timekeepingDevices: [DeviceGql!]!
  timekeepingHistory(endTime: Date!, startTime: Date!): [FaceResultGql!]!
  timekeepingPage(cameraIds: [String!], companyIds: [String!], departmentIds: [String!], deviceIds: [String!], endTime: Date!, page: Float!, perPage: Float!, recognitionType: String, startTime: Date!, textSearch: String!, type: Float!): [FaceResultGql!]!
  totalLicenseParkingSummary(endDate: String!, startDate: String!): [PackingReportGQL!]!
  unknownPage(cameraIds: [String!]!, endTime: Date!, page: Float!, perPage: Float!, startTime: Date!, trackingId: String!): [UnknownReportGql!]!
  upsertEkycIntegration(id: String, name: String!): EkycIntegrationGql!
  user: UserGql!
  userByCompany: [UserGql!]!
  userByEmail(email: String!): UserGql!
  userById(userId: String!): UserGql!
  userNames: [UserGql!]!
  userNamesFiltered(companyId: String!, departmentId: String!, userStatus: Float!): [UserGql!]!
  userWeb: UserGql!
  userWorkTimeReport(endDate: String, organizationIds: [String!], page: Float!, perPage: Float!, search: String!, startDate: String, userRank: Float!): [OrganizationUserReportGQL!]!
  usersExport(companyId: String!, departmentId: String!, userId: String!, userStatus: Float!): [UserGql!]!
  usersInDevice(deviceId: String!, page: Float!, perPage: Float!, searchText: String!): [DeviceUserGql!]!
  usersNoImage(page: Int!, perPage: Int!): [UserGql!]!
  usersRolePage(nameSearch: String!, page: Float!, perPage: Float!, roleId: String!): [UserRoleGql!]!
  usersToFile: [UserGql!]!
  validatePass(password: String!, tokenType: Float!): Boolean!
  vehicleSearchEventPage(minConfident: Float, page: Float!, perPage: Float!): [VehicleSearchEventGql!]!
  workTime(timeZoneOffset: String!): EmployeeDataGql!
}

type RawReportDataGql {
  cameraIp: String!
  day: Date!
  deviceId: String!
  faceCheckType: Float!
  hour: Float
  image: String
  time: Date!
  trackingId: Float
  userFullName: String!
  userId: String!
  userIntegrationKey: String
}

type ReIdLabelGql {
  UserGql: UserGql
  count: Int
  label: String
  reid: String!
  userId: String
}

type RecordGql {
  SrcDevice: SrcDeviceGql
  cameraIp: String
  dateCreated: Date
  dateModified: Date
  deviceId: String
  id: Float
  image: String
  srcDeviceId: String
  time: Date
  type: Int
  userId: String
}

type RemoteAdminGQL {
  Company: CompanyGql
  Department: DepartmentGql
  RemoteAdminCom: CompanyGql
  RemoteAdminDep: DepartmentGql
  RemoteAdminOrg: OrganizationGQL
  RemoteAdminUser: UserGql
  adminCount: Float!
  avatar: String!
  comCount: Float!
  count: Float!
  dateCreated: Date
  dateModified: Date
  depCount: Float!
  email: String!
  id: String!
  integrationKey: String
  name: String!
  orgCount: Float!
  userCount: Float!
}

type RemoteApproverGQL {
  Company: CompanyGql
  Department: DepartmentGql
  RemoteApproverCom: CompanyGql
  RemoteApproverDep: DepartmentGql
  RemoteApproverOrg: OrganizationGQL
  RemoteApproverUser: UserGql
  approverCount: Float!
  avatar: String!
  comCount: Float!
  count: Float!
  dateCreated: Date
  dateModified: Date
  depCount: Float!
  email: String!
  id: String!
  integrationKey: String
  name: String!
  orgCount: Float!
  userCount: Float!
}

type RemoteFaceResultGQL {
  ApproverHistory: [RemoteFaceResultHistoryGQL!]!
  detail: String!
  id: Float!
  status: Float!
  time: Date!
}

type RemoteFaceResultHistoryGQL {
  Approver: UserGql!
  dateCreated: Date!
  id: Float!
  newStatus: Float!
  oldStatus: Float!
}

type Report {
  day: Date!
  hour: Float!
  id: Float
  timeZoneOffset: String!
  totalCheckIn: Float!
  totalCheckOut: Float!
  totalUndefine: Float!
}

type ReportByDayGql {
  day: Date!
  hoursData: [UserHourDataGql!]
  positionData: [UserPositionDataGql!]
  rawData: [RawReportDataGql!]!
  totalCheckInTimes: Float
  totalCheckOutTimes: Float
  totalUndefineTimes: Float
  userFullName: String!
  userId: String
  userIds: [String!]
  userIntegrationKey: String
}

type ReportUserFilterCompanyGql {
  companyId: String!
  companyName: String!
  count: Float
  filterId: String!
}

type ReportUserFilterDepartmentGql {
  count: Float
  departmentId: String!
  departmentName: String!
  filterId: String!
}

type ReportUserFilterExRuleUserGql {
  avatarBase64: AvatarBase64GQL
  count: Float
  filterId: String!
  userId: String!
  userName: String!
}

type ReportUserFilterExUserGql {
  avatarBase64: AvatarBase64GQL
  count: Float
  filterId: String!
  userId: String!
  userName: String!
}

type ReportUserFilterGql {
  ExcludeFilterUsers: [UserGql!]
  ExcludeRuleUsers: [UserGql!]
  FilterUsers: [UserGql!]
  companyCount: Float!
  companyIds: [String!]
  count: Float
  departmentCount: Float!
  departmentIds: [String!]
  excludeCount: Float!
  excludeFilterUserIds: [String!]
  excludeRuleCount: Float!
  excludeRuleUserIds: [String!]
  filterResult: [String!]!
  id: String!
  name: String!
  totalUserCount: Float!
  userCount: Float!
  userIds: [String!]
}

type ReportUserFilterUserGql {
  avatarBase64: AvatarBase64GQL
  count: Float
  filterId: String!
  userId: String!
  userName: String!
}

type ReportVehicleByDay {
  countByVehicleType: CountByVehicleTypeGql!
  day: Date!
}

type RoleGql {
  description: String
  id: String!
  name: String
  userId: String
}

type SearchByFeatureGql {
  SearchByFeatureImageResults: [SmartSearchMctResultGql!]!
  count: Float
  endTime: Date
  gender: Float
  id: String!
  isBag: Boolean
  isRunning: Boolean!
  isSearchFace: Boolean!
  isSearchFeature: Boolean!
  lowerColor: String
  srcImgBase64: String
  srcImgPath: String
  startTime: Date
  totalFaceResult: Float
  totalMctResult: Float
  totalUserResult: Float
  upperColor: String
}

type SecurityReportGQL {
  AccessControl: AccessControlGql
  count: Float!
  countFaceTrack: Int
  countGuest: Int
  countReId: Int
  countUser: Int
  day: Date
  deviceId: String
  deviceName: String
  endTime: Date
  startTime: Date
}

type ServerConfigGql {
  dataType: String!
  dateModified: Date
  key: String!
  value: String!
}

type ServerUsageGql {
  cpuCores: Float
  cpuUsage: Float
  cpus: String
  freeDisk: Float
  freeMem: Float
  isDiskHealth: Boolean
  isRamHealth: Boolean
  time: Date
  totalDisk: Float
  totalMem: Float
  uptime: Float
}

type ShiftTypeDefaultGql {
  Company: CompanyGql
  Department: DepartmentGql
  Shift: ShiftTypeGQL!
  User: UserGql
  companyId: String
  count: Float
  departmentId: String
  id: String!
  shiftId: String!
  userId: String
}

type ShiftTypeGQL {
  actualWorkHour: Int!
  autoDays: String
  breakBonus: Int!
  breakCode: String!
  breakwithPaid: Float!
  breakwithoutPaid: Float!
  code: String!
  count: Float
  description: String
  endBreak: String!
  endHour: String!
  overnight: Float!
  startBreak: String!
  startHour: String!
  totalWorkHour: Float!
  work: Int!
}

type ShiftUserExplainAdminGql {
  Admin: UserGql!
  Company: CompanyGql
  Department: DepartmentGql
  count: Float
  id: String!
  userId: String!
}

type ShiftUserExplainAdminGroupedGql {
  Admin: UserGql!
  ShiftUserExplainAdmins: [ShiftUserExplainAdminGql!]
  count: Float
}

type ShiftUserExplainGql {
  count: Float
  email: String!
  endExplainTime: Date!
  explainImageGql: String
  explainImageId: String
  explainText: String!
  explainType: Float!
  firstInString: String
  id: String!
  lastOutString: String
  startExplainTime: Date!
  status: Float!
  userId: String!
  userName: String!
}

type ShiftUserGql {
  ShiftExplainations: [ShiftUserExplainGql!]
  User: UserGql
  checkinKey: Int
  checkinTime: Date
  checkoutKey: Int
  checkoutTime: Date
  color: [Int!]
  count: Int
  dateCreated: Date
  dateModified: Date
  departmentName: String
  device: DeviceGql
  duration: Int
  earlySecs: Float
  email: String!
  endDate: Date!
  faceResultIn: RecordGql
  faceResultOut: RecordGql
  integrationKey: String
  isEarly: Boolean
  isLate: Boolean
  lateSecs: Float
  name: String!
  sf4cId: String
  shift: ShiftTypeGQL!
  shiftId: String!
  shiftWorkTime: Float
  startDate: Date!
  totalInvalid: Float
  userId: String!
  userName: String!
}

input ShiftUserInput {
  endDate: String!
  isCompany: Boolean
  organizationId: String
  shiftId: String!
  startDate: String!
  userId: String
}

type ShiftUserMonthlyGql {
  ShiftExplainations: [ShiftUserExplainGql!]
  User: UserGql!
  checkinKey: Int
  checkinTime: Date
  checkoutKey: Int
  checkoutTime: Date
  color: [Int!]
  count: Int
  dateCreated: Date
  dateModified: Date
  departmentName: String
  device: DeviceGql
  duration: Int
  email: String!
  endDate: Date!
  faceResultIn: RecordGql
  faceResultOut: RecordGql
  integrationKey: String
  name: String!
  shift: [ShiftTypeGQL!]!
  shiftId: [String!]!
  startDate: Date!
  userId: String!
  userName: String!
}

type SmartSearchFaceResponseGql {
  FaceResultGroupByTrack: [SmartSearchFaceResultGroupByTrackGql!]!
  FaceResults: [SmartSearchFaceResultGql!]!
  count: Float!
  faceCaptureId: String!
  faceCaptureImageBase64: String
  faceCaptureImagePath: String
}

type SmartSearchFaceResultGql {
  Camera: CameraGql
  FaceResult: FaceResultGql
  OrphanFaceResult: FaceResultGql
  cameraId: String!
  cameraIp: String
  count: Float
  eventId: String!
  imageBase64: String
  imageId: String!
  isMask: Boolean
  personImagePath: String!
  serverPath: String!
  similarity: Float!
  time: Date!
}

type SmartSearchFaceResultGroupByTrackGql {
  Camera: CameraGql
  FaceResults: [SmartSearchFaceResultGql!]!
  cameraId: String!
  cameraIp: String!
  count: Float!
  maxConfidence: Float!
  maxTime: Date!
  minConfidence: Float!
  minTime: Date!
  trackId: String!
}

type SmartSearchMctResponseGql {
  MctResultGroupByTrack: [SmartSearchMctResultGroupByTrackGql!]!
  MctResults: [SmartSearchMctResultGql!]!
  count: Float!
  personCaptureId: String!
  personImageBase64: String!
  personImagePath: String!
}

type SmartSearchMctResultGql {
  cameraId: String!
  cameraIp: String!
  confidence: Float!
  count: Float!
  deviceId: String!
  eventId: String!
  imageBase64: String
  imageId: String
  imagePath: String
  personCaptureId: String!
  personImageBase64: String
  time: Date!
  trackId: String!
}

type SmartSearchMctResultGroupByTrackGql {
  HumanTrackInfo: HumanTrackInfoGql!
  MctResults: [SmartSearchMctResultGql!]!
  count: Float!
  maxConf: Float
  maxTime: Date
  minConf: Float
  minTime: Date
}

type SmartSearchUserResultGql {
  count: Float
  eventId: String!
  resultUserId: String!
  userAvatarBase64: String
  userAvatarPath: String
  userId: String
  userMaskConfidence: Float!
  userName: String!
  userNoMaskConfidence: Float!
}

type SrcDeviceGql {
  activeStatus: String
  count: Int
  dateCreated: Date
  dateModified: Date
  id: String!
  ip: String!
  isTimekeepingDevice: Boolean
  lastUpdateTime: Date!
  name: String!
  statusUpdate: String!
  type: String!
}

type StreamStatusDashboardGql {
  cameraIp: String
  data: [DeviceStreamLogGql!]!
  deviceId: String
}

type StudentGql {
  UserGql: UserGql!
  avatarBase64: String
  classCode: String!
  className: String!
  count: Int
  grade: String!
  majorCode: String!
  majorName: String!
  studentId: String!
  validUntil: Date!
}

type SystemLogGql {
  User: UserGql
  changeNote: String!
  count: Int
  dateCreated: Date!
  dateModified: Date!
  functionId: String!
  id: String!
  newValue: String
  oldValue: String
  type: String!
}

type TelegramBotGql {
  count: Int
  dateCreated: Date!
  dateModified: Date!
  firstName: String
  id: String!
  isClusterSync: Boolean
  isEnable: Boolean
  token: String!
  userName: String!
}

type TelegramGroupGQL {
  Companies: [CompanyGql!]
  Departments: [DepartmentGql!]
  Devices: [DeviceGql!]
  NotiTimes: [NotificationTimeGql!]
  Receivers: [TelegramInfoGql!]
  Users: [UserGql!]
  companyCount: Float!
  count: Float
  departmentCount: Float!
  deviceCount: Float!
  id: String!
  isEnable: Boolean!
  name: String!
  notiTimesCount: Float!
  receiverCount: Float!
  type: Float!
  userCount: Float!
}

type TelegramInfoGql {
  count: Float
  first_name: String
  id: String!
  is_bot: Boolean
  language_code: String
  last_name: String
  username: String
}

"""TimeOfDay custom scalar type"""
scalar TimeOfDay

type UnknownFaceResultGql {
  cameraIp: String
  deviceId: String
  id: Float!
  image: String!
  time: Date!
}

type UnknownReportGql {
  Camera: CameraGql
  SrcDevice: SrcDeviceGql
  cameraIp: String
  channelName: String
  count: Int
  data: [UnknownFaceResultGql!]
  deviceId: String
  image: String
  images: [String!]
  reId: String
  reIdLabel: String
  time: Date
  trackingId: String
}

type UploadVideoAnalyticGql {
  VideoAnalyticSummary: VideoAnalyticSummaryGql
  count: Int
  fileName: String!
  filePath: String!
  fileSize: Int!
  fileType: String!
  groupId: String
  id: String!
}

type UserAbilityGql {
  action: String!
  subject: String!
}

input UserAddInput {
  adName: String
  birthDay: Date!
  companyId: String!
  departmentId: String!
  email: String!
  employeeCode: String
  fullName: String!
  gender: Gender!
  idCardNumber: String
  idCardNumberJson: String
  isFaceReScanEnable: Boolean
  isLoginEnable: Boolean
  isRemoteCheckEnable: Boolean
  phoneNumber: String!
  title: String
  userId: String
}

type UserAlertGQL {
  User: UserGql
  alertRuleId: String!
  avatar: String
  avatarBase64: AvatarBase64GQL
  company: CompanyGql!
  count: Float
  department: DepartmentGql!
  email: String!
  fullName: String!
  integrationKey: String
  userId: String!
}

type UserAttandantTimeGql {
  AttandantTime: AttandantTimeGql
  attandantId: String!
  checkTime: Date!
  company: String
  count: Int
  department: String
  endDate: Date!
  integrationKey: String
  isActive: Boolean!
  isChecked: Boolean!
  startDate: Date!
  userId: String!
  userName: String
}

type UserFeedbackGql {
  User: UserGql
  UserFeedbackImage: [UserFeedbackImageGql!]
  UserFeedbackResponse: [UserFeedbackResponseGql!]
  count: Float
  dateCreated: Date!
  dateModified: Date!
  detail: String!
  id: String!
  isClusterSync: Boolean!
  responseTime: Date
  status: Float!
  title: String!
  type: Float!
  userId: String!
}

type UserFeedbackImageGql {
  dateCreated: Date
  dateModified: Date
  feedbackId: String
  id: String
  isClusterSync: Boolean
  path: String
}

type UserFeedbackResponseGql {
  dateCreated: Date
  dateModified: Date
  detail: String
  id: String
  isClusterSync: Boolean
  userFeedbackId: String
}

type UserGql {
  Company: CompanyGql
  Department: DepartmentGql
  MaskConfThreshold: Float
  NoMaskConfThreshold: Float
  Organizations: [OrganizationGQL!]
  RemoteLimit: UserRemoteLimitGQL
  ability: [UserAbilityGql!]
  accessToken: String
  adName: String
  adminNote: String
  avatar: String!
  avatarBase64: AvatarBase64GQL
  birthDay: Date
  company: CompanyGql
  companyId: String
  companyName: String
  count: Int
  dateCreated: Date
  dateModified: Date
  department: DepartmentGql
  departmentId: String
  departmentName: String
  email: String!
  exportCompanyId: String
  exportedDepartmentId: String
  faceImageCount: Int
  faceImages: [FaceImageGraphQL!]
  faceImagesArrayBase64: [FaceImageBase64GQL!]
  firstIn: Date
  fullName: String!
  gender: Gender!
  id: String!
  idCardFaceSimilarity: Float
  idCardJson: String
  idCardNumber: String
  integrationKey: String
  isFaceReScanEnable: Boolean
  isLoginEnable: Boolean
  isRemoteCheckEnable: Boolean
  lastOut: Date
  name: String!
  password: String
  phoneNumber: String
  refreshToken: String
  role: String
  roles: [RoleGql!]
  status: String
  telegramId: String
  tenantId: String
  title: String
  userRank: Float
  userRoles: [UserRoleGql!]
}

type UserGuestGql {
  AcLocations: [ACLocationGql!]
  Company: CompanyGql
  Department: DepartmentGql
  ReferenceUser: UserGql
  address: String
  avatar: String!
  avatarBase64: String
  birthday: Date
  cardId: String!
  cardImage: String
  cardImageBase64: String
  companyId: String
  count: Int
  dateCreated: Date!
  dateModified: Date!
  departmentId: String
  email: String
  expiredTime: Date!
  faceImage: String
  gender: Gender!
  id: String!
  licensePlate: String
  name: String!
  nationality: String
  phone: String
  placeOfBirth: String
  status: String!
}

type UserHourDataGql {
  day: Date
  hour: Float!
  totalCheckInTimes: Float
  totalCheckOutTimes: Float
  totalUndefineTimes: Float
  userFullName: String!
  userId: String
  userIds: [String!]
  userIntegrationKey: String
}

type UserPositionDataGql {
  cameraIp: String
  checkInTimes: Float!
  checkOutTimes: Float!
  deviceId: String!
  deviceName: String
  undefinedTimes: Float!
  userId: String
}

type UserRemoteLimitGQL {
  currentTimes: Float!
  dateLimit: Date
  limitTimes: Float
  userId: String!
}

type UserReportGql {
  minTime: Date
  reportByDay: [ReportByDayGql!]!
  userId: String
  userName: String
}

type UserRoleGql {
  avatar: String!
  avatarBase64: AvatarBase64GQL
  companyName: String!
  count: Int
  departmentName: String!
  email: String!
  roleId: String!
  userId: String!
  userName: String!
}

input UserSearchInput {
  companyIds: [String!]
  departmentIds: [String!]!
  faceImageFilter: Boolean
  pageIndex: Int!
  pageSize: Int!
  query: String!
  status: Int!
}

type UserTimeReportGql {
  Company: CompanyGql
  Department: DepartmentGql
  avatar: String
  checkInTimes: Float!
  checkOutTimes: Float!
  count: Float!
  date: String
  dateCreated: Date!
  dateModified: Date!
  email: String
  firstInDevice: DeviceGql
  firstInId: Float
  firstInString: String
  firstInTime: Date
  isClusterSync: Boolean!
  lastOutDevice: DeviceGql
  lastOutId: Float
  lastOutString: String
  lastOutTime: Date
  timeZoneOffset: String!
  unknownTimes: Float!
  userId: String!
  userKey: String
  userName: String
  workTime: Float!
}

type VehicleInOutGql {
  count: Float!
  createdBy: String!
  createdUtc: String!
  id: String!
  identity: IparkingCustomerIdentityGql!
  identityGroup: IparkingGroupGql!
  lane: IparkingLaneGql!
  openBarrier: Boolean!
  plateNumber: String!
  status: Float!
}

type VehiclePackingDeviceGql {
  Camera: CameraGql
  Device: DeviceGql
  cameraId: String
  channel: Float!
  count: Float
  dateCreated: Date!
  dateModified: Date!
  deviceId: String!
  deviceType: Float!
  id: String!
  parkingId: String!
}

type VehiclePackingLicenseAllowGql {
  Guest: UserGuestGql
  User: UserGql
  count: Float
  dateCreated: Date!
  dateModified: Date!
  guestId: String
  licensePlate: String!
  userId: String
}

type VehiclePackingResultGql {
  Guest: UserGuestGql
  GuestAllowed: UserGuestGql
  HistoryIn: VehiclePackingResultGql
  User: UserGql
  UserAllowed: UserGql
  VehicleParkingLane: VehicleParkingLaneGQL!
  barrieMode: Float
  count: Float
  dateCreated: Date!
  dateModified: Date!
  faceResultId: Float
  guestId: String
  guestImage: String
  guestResultId: Float
  guestTime: Date
  isCheckedIn: Boolean
  isMatched: Boolean
  laneId: String!
  laneType: Float
  licensePlate: String
  licensePlateImagePath: String
  licensePlateTime: Date
  licenseResultId: Float
  openBarrie: Boolean
  userId: String
  userImage: String
  userTime: Date
}

type VehicleParkingLaneGQL {
  barrieMode: Float!
  count: Float
  dateCreated: Date!
  dateModified: Date!
  id: String!
  laneType: Float!
  name: String!
  vehicleType: Float!
}

type VehicleSearchCapturedGql {
  Cameras: [CameraGql!]
  TrackInfos: [VehicleTrackInfoGql!]
  count: Int
  eventId: String!
  id: String!
  image: String!
  imageId: String!
  maxConfidence: Float
  maxTime: Date
  minConfidence: Float
  minTime: Date
}

type VehicleSearchEventGql {
  camFilterIds: String
  count: Int
  deviceId: String!
  endSearchTime: Date!
  id: String!
  imagePath: String!
  resultCount: Int
  startSearchTime: Date!
  topN: Float!
}

type VehicleTrackInfoGql {
  Camera: CameraGql
  TrackResults: [VehicleTrackResultGql!]
  cameraId: String!
  cameraIp: String!
  count: Float
  dateCreated: Date!
  dateModified: Date!
  isVehicleAllowed: Boolean
  lastLicensPlate: String
  lastVehicleType: Int
  maxConf: Float
  maxTime: Date
  minConf: Float
  minTime: Date
  trackId: String!
}

type VehicleTrackResultGql {
  bodyB: String
  bodyG: String
  bodyR: String
  count: Float
  dateCreated: Date!
  dateModified: Date!
  id: String!
  imageId: String!
  imagePath: String!
  licensePlate: String
  time: Date
  trackId: String!
}

type VideoAnalyticGql {
  cameraId: String!
  cameraName: String
  count: Int
  countSeen: Int!
  deviceId: String!
  fileName: String
  firstSeen: Int!
  lastSeen: Int!
  name: String!
  reId: String!
  reIdLabel: String
  videoId: String
  videoSrcType: String!
}

type VideoAnalyticGroupGql {
  Videos: [UploadVideoAnalyticGql!]
  ageGenderEnable: Boolean!
  count: Int
  description: String
  faceIdEnable: Boolean!
  faceReIdEnable: Boolean!
  faceSmartSearchEnable: Boolean!
  humanAttEnable: Boolean!
  humanSmartSearchEnable: Boolean!
  id: String!
  name: String!
  s2tEnable: Boolean!
  type: String
  vehicleAttEnable: Boolean!
  vehicleSmartSearchEnable: Boolean!
  videoAbnormalEnable: Boolean!
}

type VideoAnalyticReIdResultGql {
  count: Int
  image: String!
  orphanId: Int
  reId: String!
  timeSeen: Int!
}

type VideoAnalyticSummaryGql {
  bitRate: Float
  count: Int
  countSeen: Int
  duration: Float
  fileName: String!
  fileSize: Int
  firstSeen: Int
  frameHeight: Int
  frameRate: Float
  frameWidth: Int
  groupId: String!
  lastSeen: Int
  processDeviceId: String
  processStatus: String
  processingFrame: Int
  reId: String
  reIdLabel: String
  totalFrame: Int
  videoId: String!
}
