import { UseGuards } from '@nestjs/common';
import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';
import { AlertRuleDefaultEnum } from 'src/alert-rule/models/alert-rule-defaults.enum';
import { ResolverAuthGuard } from 'src/auth/auth.guard';
import { SqlAction } from 'src/data/enums/sql-action.enum';
import { UserAlertGateway } from 'src/socket-gateway/user-alert.gateway';
import { Action, CurrentCompanyGql, CurrentDepartmentGql, CurrentUserGql, CurrentUserRoleGql, FunctionIdAuth } from 'src/utils/decorators';
import { UserAlertGQL } from './models/user-alert.gql';
import { UserAlertService } from './user-alert.service';
import { FunctionId } from '@prisma/client';
import { UserGql } from 'src/user/models/user.gql';

@Resolver()
@UseGuards(ResolverAuthGuard)
export class UserAlertResolver {
    constructor(
        private userAlertService: UserAlertService,
        private gateway: UserAlertGateway
    ) { }
    //#endregion

    //#region General
    @FunctionIdAuth('ALEART_USER')
    @Action(SqlAction.Delete)
    @Mutation(returns => Number, { name: 'removeAlertUser' })
    async removeAlertUser(
        @Args('userIds', { type: () => [String] }) userIds: string[],
        @Args('aleartIds', { type: () => [String] }) aleartIds: string[],
    ) {
        return await this.userAlertService.removeAlertUser(userIds, aleartIds);
    }
    //#endregion

    //#region VIP
    @FunctionIdAuth('ALEART_USER')
    @Action(SqlAction.Create)
    @Mutation(returns => [UserAlertGQL], { name: 'upsertVipAlertUser' })
    async upsertVipAlertUser(
        @Args('userIds', { type: () => [String] }) userIds: string[],
    ) {
        let alertId = Object.entries(AlertRuleDefaultEnum).
            find(([key, val]) => val === AlertRuleDefaultEnum.VIP_ALERT)?.[0];
        let upsertVipAlertUser = await this.userAlertService.upsertAlertUser(alertId, userIds);
        this.gateway.sendUserAlertUpdateRequest();

        return upsertVipAlertUser
    }

    @FunctionIdAuth(FunctionId.ALEART_USER)
    @Action(SqlAction.Read)
    @Query(returns => [UserGql], { name: 'searchVipUser' })
    async searchVipUser(
        @CurrentUserGql() currentUserId: string,
        @CurrentUserRoleGql() currentUserRole: string,
        @CurrentCompanyGql() currentCompanyId: string,
        @CurrentDepartmentGql() currentDepId: string,
        @Args('search', { type: () => String }) search: string,
        @Args('page', { type: () => Number }) page: number,
        @Args('perPage', { type: () => Number }) perPage: number,
        @Args('companyIds', { type: () => [String] }) companyIds: string[],
        @Args('departmentIds', { type: () => [String] }) departmentIds: string[],
        @Args('status', { type: () => Number, nullable: true }) status: number,
    ) {
        // let alertId = Object.entries(AlertRuleDefaultEnum).
        //     find(([key, val]) => val === AlertRuleDefaultEnum.VIP_ALERT)?.[0];

        // return await this.userAlertService.searchAlertUser(alertId, search, page, perPage);
        return await this.userAlertService.searchVipUser(currentUserId, currentUserRole, currentCompanyId, currentDepId, search, page, perPage, companyIds, departmentIds, status);
    }

    //#endregion

    //#region Black List
    @FunctionIdAuth('ALEART_USER')
    @Action(SqlAction.Create)
    @Mutation(returns => [UserAlertGQL], { name: 'upsertBlackListAlertUser' })
    async upsertBlackListAlertUser(
        @Args('userIds', { type: () => [String] }) userIds: string[],
    ) {
        let alertId = Object.entries(AlertRuleDefaultEnum).
            find(([key, val]) => val === AlertRuleDefaultEnum.USER_BLACK_LIST_ALERT)?.[0];
        let upsertBlackListAlertUser = await this.userAlertService.upsertAlertUser(alertId, userIds);
        this.gateway.sendUserAlertUpdateRequest();
        return upsertBlackListAlertUser
    }

    @FunctionIdAuth(FunctionId.ALEART_USER)
    @Action(SqlAction.Read)
    @Query(returns => [UserGql], { name: 'searchBlackListUser' })
    async searchBlackListUser(
        @CurrentUserGql() currentUserId: string,
        @CurrentUserRoleGql() currentUserRole: string,
        @CurrentCompanyGql() currentCompanyId: string,
        @CurrentDepartmentGql() currentDepId: string,
        @Args('search', { type: () => String }) search: string,
        @Args('page', { type: () => Number }) page: number,
        @Args('perPage', { type: () => Number }) perPage: number,
        @Args('companyIds', { type: () => [String] }) companyIds: string[],
        @Args('departmentIds', { type: () => [String] }) departmentIds: string[],
        @Args('status', { type: () => Number, nullable: true }) status: number,
    ) {
        // let alertId = Object.entries(AlertRuleDefaultEnum).
        //     find(([key, val]) => val === AlertRuleDefaultEnum.USER_BLACK_LIST_ALERT)?.[0];
        // return await this.userAlertService.searchAlertUser(alertId, search, page, perPage);
        return await this.userAlertService.searchBlacklistUser(currentUserId, currentUserRole, currentCompanyId, currentDepId, search, page, perPage, companyIds, departmentIds, status);
    }
    //#endregion

    //#region new vip/blacklist
    @FunctionIdAuth(FunctionId.ALEART_USER)
    @Action(SqlAction.Create)
    @Mutation(returns => Int, { name: 'toggleVipUser' })
    async toggleVipUser(
        @CurrentUserGql() currentUserId: string,
        @CurrentUserRoleGql() currentUserRole: string,
        @CurrentCompanyGql() currentCompanyId: string,
        @CurrentDepartmentGql() currentDepId: string,
        @Args('userIds', { type: () => [String] }) userIds: string[],
        @Args('isVip', { type: () => Boolean }) isVip: boolean,
    ) {
        // let alertId = Object.entries(AlertRuleDefaultEnum).
        //     find(([key, val]) => val === AlertRuleDefaultEnum.VIP_ALERT)?.[0];
        let countVipToggle = await this.userAlertService.toggleVipUser(currentUserId, currentUserRole, currentCompanyId, currentDepId, userIds, isVip);
        this.gateway.sendUserAlertUpdateRequest();

        return countVipToggle
    }

    @FunctionIdAuth(FunctionId.ALEART_USER)
    @Action(SqlAction.Create)
    @Mutation(returns => Int, { name: 'toggleBlacklistUser' })
    async toggleBlackListUser(
        @CurrentUserGql() currentUserId: string,
        @CurrentUserRoleGql() currentUserRole: string,
        @CurrentCompanyGql() currentCompanyId: string,
        @CurrentDepartmentGql() currentDepId: string,
        @Args('userIds', { type: () => [String] }) userIds: string[],
        @Args('isBlacklist', { type: () => Boolean }) isBlacklist: boolean,
    ) {
        // let alertId = Object.entries(AlertRuleDefaultEnum).
        //     find(([key, val]) => val === AlertRuleDefaultEnum.VIP_ALERT)?.[0];
        let countBlacklistToggle = await this.userAlertService.toggleBlackListUser(currentUserId, currentUserRole, currentCompanyId, currentDepId, userIds, isBlacklist);
        this.gateway.sendUserAlertUpdateRequest();

        return countBlacklistToggle
    }
    //#endregion
}