import { BadRequestException, Injectable, OnModuleInit } from '@nestjs/common';
import { ActiveStatus, Prisma } from '@prisma/client';
import { AlertLevelEnum } from 'src/alert-rule/models/alert-level.enum';
import { AlertRuleDefaultEnum } from 'src/alert-rule/models/alert-rule-defaults.enum';
import { ErrorEnum } from 'src/data/enums/error.enum';
import { PrismaService } from 'src/prisma/prisma.service';
import { StringUtils } from 'src/utils/string';
import { UserAlertGQL } from './models/user-alert.gql';
import { join } from 'path';
import { getStaticFilesPath } from 'src/utils/constants';
import { CompanyGql } from 'src/company/models/company.gql';
import { DepartmentGql } from 'src/department/models/department.gql';
import { AiServiceType } from 'src/ai-service/models/ai-service-type.enum';
import { UserFilterService } from 'src/user-filter/user-filter.service';
import { UserGql } from 'src/user/models/user.gql';
import { NumberStatus } from 'src/data/enums/status.enum';

@Injectable()
export class UserAlertService implements OnModuleInit {
    private avatarPath: string = join(getStaticFilesPath(), 'images', 'avatar');

    async onModuleInit() {
        await this.initVIPAlert();
        await this.initBlackListAlert();
        await this.initAccessControlAlert();
        await this.initUserRegisAlert();
    }

    constructor(
        private prisma: PrismaService,
        private userFilterService: UserFilterService,
    ) { }

    //#region General
    async upsertAlertUser(
        alertId: string,
        userIds: string[],
    ): Promise<UserAlertGQL[]> {
        let result: UserAlertGQL[] = [];
        for (let index = 0; index < userIds.length; index++) {
            const userId = userIds[index];
            let checkUser = await this.prisma.user.findFirst({
                where: { id: userId },
                select: { id: true }
            });
            if (!checkUser) throw new BadRequestException(StringUtils.generateExceptionString(ErrorEnum['User not found']));

            let aleartUser = await this.prisma.alertUser.upsert({
                where: {
                    alertRuleId_userId: {
                        alertRuleId: alertId,
                        userId: userId
                    }
                }, create: {
                    alertRuleId: alertId,
                    userId: userId
                }, update: {}
            });

            result.push(new UserAlertGQL({ ...aleartUser }));
        }

        return result;
    }

    async searchAlertUser(
        alertId: string,
        search: string,
        page: number,
        perPage: number
    ): Promise<UserAlertGQL[]> {

        let filter: Prisma.AlertUserWhereInput = {
            alertRuleId: alertId,
            isActive: true,
            User: {
                OR: [
                    {
                        name: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    },
                    {
                        email: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    },
                    {
                        adName: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    },
                    {
                        integrationKey: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    },
                ]
            }
        };
        let count = await this.prisma.alertUser.count({ where: filter });
        let users = await this.prisma.alertUser.findMany({
            where: filter,
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            select: {
                User: {
                    select: {
                        id: true,
                        name: true,
                        avatar: true,
                        email: true,
                        integrationKey: true,
                        Company: { select: { name: true } },
                        Department: { select: { name: true } },
                    }
                }
            }
        });

        return users.map(user => new UserAlertGQL({
            alertRuleId: alertId,
            userId: user.User.id,
            email: user.User.email,
            fullName: user.User.name,
            integrationKey: user.User.integrationKey,
            company: new CompanyGql({ name: user.User.Company.name }),
            department: new DepartmentGql({ name: user.User.Department.name }),
            avatar: user.User.avatar,
            // avatarBase64: new AvatarBase64GQL({
            //     data: base64.encodeImage((join(this.avatarPath, user.User.avatar)))
            // }),
            count: count
        }));
    }
    //#endregion

    //#region VIP
    async initVIPAlert() {
        let alertId = Object.entries(AlertRuleDefaultEnum).
            find(([key, val]) => val === AlertRuleDefaultEnum.VIP_ALERT)?.[0];
        await this.prisma.alertRule.upsert({
            where: { id: alertId },
            create: {
                id: alertId,
                type: AiServiceType['VIP detected'],
                name: alertId,
                description: alertId,
                alertLevel: AlertLevelEnum.High,
            },
            update: {}
        });
    }

    async removeAlertUser(
        userIds: string[],
        aleartIds: string[]
    ) {

        let deleted = await this.prisma.alertUser.updateMany({
            where: {
                alertRuleId: { in: aleartIds },
                userId: { in: userIds }
            }, data: {
                isActive: false
            }
        });

        return deleted.count;
    }
    //#endregion

    //#region Black list
    async initBlackListAlert() {
        let alertId = Object.entries(AlertRuleDefaultEnum).
            find(([key, val]) => val === AlertRuleDefaultEnum.USER_BLACK_LIST_ALERT)?.[0];
        await this.prisma.alertRule.upsert({
            where: { id: alertId },
            create: {
                id: alertId,
                type: AiServiceType['Black list detected'],
                name: alertId,
                description: alertId,
                alertLevel: AlertLevelEnum.High,
            },
            update: {}
        });
    }

    //#endregion

    async initAccessControlAlert() {
        let alertId = Object.entries(AlertRuleDefaultEnum).
            find(([key, val]) => val === AlertRuleDefaultEnum.ACCESS_CONTROL_ALERT)?.[0];
        await this.prisma.alertRule.upsert({
            where: { id: alertId },
            create: {
                id: alertId,
                type: AiServiceType.AccessControl,
                name: alertId,
                description: alertId,
                alertLevel: AlertLevelEnum.Medium,
            },
            update: {}
        });
    }

    async initUserRegisAlert() {
        let alertId = Object.entries(AlertRuleDefaultEnum).
            find(([key, val]) => val === AlertRuleDefaultEnum.USER_REGIS_ALERT)?.[0];
        await this.prisma.alertRule.upsert({
            where: { id: alertId },
            create: {
                id: alertId,
                type: AiServiceType.UserRegis,
                name: alertId,
                description: alertId,
                alertLevel: AlertLevelEnum.Medium,
            },
            update: {}
        });
    }

    //#region new vip/blacklist
    async toggleVipUser(
        currentUserId: string,
        currentUserRole: string,
        currentCompanyId: string,
        currentDepartmentId: string,
        userIds: string[],
        isVip: boolean
    ): Promise<number> {
        let userFilter = await this.userFilterService.getUserFilterByRole(currentUserId, currentUserRole, currentCompanyId, currentDepartmentId, [], [], userIds);

        userFilter.isVip = { not: isVip };

        let users = await this.prisma.user.findMany({
            where: userFilter,
            select: { id: true }
        });

        let count = 0;
        for (let index = 0; index < users.length; index++) {
            const user = users[index];
            let updated = await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    isVip: isVip
                }, select: { id: true }
            });
            if (updated)
                count++;
        }

        return count;
    }

    async toggleBlackListUser(
        currentUserId: string,
        currentUserRole: string,
        currentCompanyId: string,
        currentDepartmentId: string,
        userIds: string[],
        isBlackList: boolean
    ): Promise<number> {
        let userFilter = await this.userFilterService.getUserFilterByRole(currentUserId, currentUserRole, currentCompanyId, currentDepartmentId, [], [], userIds);

        userFilter.isBlackList = { not: isBlackList };

        let users = await this.prisma.user.findMany({
            where: userFilter,
            select: { id: true }
        });

        let count = 0;
        for (let index = 0; index < users.length; index++) {
            const user = users[index];
            let updated = await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    isBlackList: isBlackList
                }, select: { id: true }
            });
            if (updated)
                count++;
        }

        return count;
    }

    async searchVipUser(
        currentUserId: string,
        currentUserRole: string,
        currentCompanyId: string,
        currentDepartmentId: string,
        search: string,
        page: number,
        perPage: number,
        companyIds: string[],
        departmentIds: string[],
        status: number,
    ): Promise<UserGql[]> {
        let userFilter = await this.userFilterService.getUserFilterByRole(currentUserId, currentUserRole, currentCompanyId, currentDepartmentId);
        userFilter.isVip = true;
        userFilter.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
            { adName: { contains: search, mode: 'insensitive' } },
            { integrationKey: { contains: search, mode: 'insensitive' } },
        ]
        if (companyIds.length > 0) userFilter.companyId = { in: companyIds };
        if (departmentIds.length > 0) userFilter.departmentId = { in: departmentIds };
        let statusFilter: ActiveStatus[] = [ActiveStatus.ACTIVE, ActiveStatus.DEACTIVE]
        if (status == NumberStatus.Active) statusFilter = [ActiveStatus.ACTIVE]
        if (status == NumberStatus.InActive) statusFilter = [ActiveStatus.DEACTIVE]
        if (statusFilter.length > 0) userFilter.activeStatus = { in: statusFilter };

        let count = await this.prisma.user.count({ where: userFilter });
        let users = await this.prisma.user.findMany({
            where: userFilter,
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            select: {
                id: true,
                name: true,
                avatar: true,
                email: true,
                integrationKey: true,
                Company: { select: { name: true, id: true } },
                Department: { select: { name: true, id: true } },
                activeStatus: true
            }
        });

        return users.map(user => new UserGql({
            id: user.id,
            email: user.email,
            fullName: user.name,
            integrationKey: user.integrationKey,
            Company: new CompanyGql({ name: user.Company.name, id: user.Company.id }),
            Department: new DepartmentGql({ name: user.Department.name, id: user.Department.id }),
            avatar: user.avatar,
            status: user.activeStatus,
            count: count
        }));
    }

    async searchBlacklistUser(
        currentUserId: string,
        currentUserRole: string,
        currentCompanyId: string,
        currentDepartmentId: string,
        search: string,
        page: number,
        perPage: number,
        companyIds: string[],
        departmentIds: string[],
        status: number,
    ): Promise<UserGql[]> {
        let userFilter = await this.userFilterService.getUserFilterByRole(currentUserId, currentUserRole, currentCompanyId, currentDepartmentId);
        userFilter.isBlackList = true;
        userFilter.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
            { adName: { contains: search, mode: 'insensitive' } },
            { integrationKey: { contains: search, mode: 'insensitive' } },
        ]
        if (companyIds.length > 0) userFilter.companyId = { in: companyIds };
        if (departmentIds.length > 0) userFilter.departmentId = { in: departmentIds };
        let statusFilter: ActiveStatus[] = [ActiveStatus.ACTIVE, ActiveStatus.DEACTIVE]
        if (status == NumberStatus.Active) statusFilter = [ActiveStatus.ACTIVE]
        if (status == NumberStatus.InActive) statusFilter = [ActiveStatus.DEACTIVE]
        if (statusFilter.length > 0) userFilter.activeStatus = { in: statusFilter };

        let count = await this.prisma.user.count({ where: userFilter });
        let users = await this.prisma.user.findMany({
            where: userFilter,
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            select: {
                id: true,
                name: true,
                avatar: true,
                email: true,
                integrationKey: true,
                Company: { select: { name: true, id: true } },
                Department: { select: { name: true, id: true } },
                activeStatus: true
            }
        });

        return users.map(user => new UserGql({
            id: user.id,
            email: user.email,
            fullName: user.name,
            integrationKey: user.integrationKey,
            Company: new CompanyGql({ name: user.Company.name, id: user.Company.id }),
            Department: new DepartmentGql({ name: user.Department.name, id: user.Department.id }),
            avatar: user.avatar,
            status: user.activeStatus,
            count: count
        }));
    }
    //#endregion
}
