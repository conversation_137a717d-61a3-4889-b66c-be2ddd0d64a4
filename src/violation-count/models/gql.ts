import { Field, Float, Int, ObjectType } from "@nestjs/graphql";

@ObjectType()
export class ViolationCountResultGql {
    constructor(partial: Partial<ViolationCountResultGql>) {
        Object.assign(this, partial);
    }
    @Field(type => Int, { nullable: true })
    uniformViolationCount: number;

    @Field(type => Int, { nullable: false })
    cardViolationCount: number;

    @Field(type => Int, { nullable: false })
    totalViolationCount: number;
}