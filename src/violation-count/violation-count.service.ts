import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { PersonMctAttributeEnum } from 'src/smart-search/models/person-mct-att.enum';
import * as moment from 'moment';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';
import { ViolationCountResultGql } from './models/gql';

@Injectable()
export class ViolationCountService {
    constructor(
        private prisma: PrismaService,
        private cachedGlobalConfig: CachedGlobalConfigService,
    ) { }

    /**
     * Đếm số lượt vi phạm đồng phục và thẻ của user trong tháng theo ca làm việc
     * @param userId ID của user
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Object chứa số lượt vi phạm
     */
    async countViolationsByUserAndMonth(
        userId: string,
        year: number,
        month: number
    ): Promise<ViolationCountResultGql> {
        const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).startOf('day').toDate();
        const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).endOf('month').toDate();

        // Query tối ưu - đồng phục: mỗi ngày 1 lần, thẻ: đếm tất cả lần
        const violationCounts = await this.prisma.$queryRaw<Array<{
            uniform_violation_count: string;
            card_violation_count: string;
        }>>`
            SELECT
                -- Đồng phục: đếm số ngày vi phạm (mỗi ngày 1 lần)
                (SELECT COUNT(DISTINCT violation_date)
                 FROM (
                    SELECT DISTINCT
                        attr."dateCreated"::date as violation_date
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        -- Loại trừ thời gian nghỉ giải lao
                        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                                AND shift_type."startBreak" != '00:00'
                                AND shift_type."endBreak" != '00:00')
                        -- Vi phạm đồng phục chỉ tính vào thứ 2 (1 = Monday trong PostgreSQL)
                        AND EXTRACT(DOW FROM attr."dateCreated") = 1
                        AND result."userIdReCheckResult" = ${userId}
                        AND attr."type" = ${PersonMctAttributeEnum.none_ppe_body}

                    UNION ALL

                    SELECT DISTINCT
                        attr."dateCreated"::date as violation_date
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        -- Loại trừ thời gian nghỉ giải lao
                        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                                AND shift_type."startBreak" != '00:00'
                                AND shift_type."endBreak" != '00:00')
                        -- Vi phạm đồng phục chỉ tính vào thứ 2 (1 = Monday trong PostgreSQL)
                        AND EXTRACT(DOW FROM attr."dateCreated") = 1
                        AND face."userId" = ${userId}
                        AND attr."type" = ${PersonMctAttributeEnum.none_ppe_body}
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackResult" r
                            WHERE r."humanTrackId" = attr."trackId"
                            AND r."userIdReCheckResult" IS NOT NULL
                        )
                 ) uniform_days
                )::text as uniform_violation_count,

                -- Thẻ: đếm tất cả lần vi phạm (chỉ khi có no_employee_card và không có wearing_employee_card)
                (SELECT COUNT(*)
                 FROM (
                    SELECT DISTINCT attr."trackId"
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        -- Loại trừ thời gian nghỉ giải lao
                        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                                AND shift_type."startBreak" != '00:00'
                                AND shift_type."endBreak" != '00:00')
                        AND result."userIdReCheckResult" = ${userId}
                        AND attr."type" = ${PersonMctAttributeEnum.no_employee_card}
                        -- Chỉ tính khi không có wearing_employee_card trong cùng track
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                            WHERE attr2."trackId" = attr."trackId"
                            AND attr2."type" = ${PersonMctAttributeEnum.wearing_employee_card}
                        )

                    UNION ALL

                    SELECT DISTINCT attr."trackId"
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        -- Loại trừ thời gian nghỉ giải lao
                        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                                AND shift_type."startBreak" != '00:00'
                                AND shift_type."endBreak" != '00:00')
                        AND face."userId" = ${userId}
                        AND attr."type" = ${PersonMctAttributeEnum.no_employee_card}
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                            WHERE attr2."trackId" = attr."trackId"
                            AND attr2."type" = ${PersonMctAttributeEnum.wearing_employee_card}
                        )
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackResult" r
                            WHERE r."humanTrackId" = attr."trackId"
                            AND r."userIdReCheckResult" IS NOT NULL
                        )
                 ) card_violations
                )::text as card_violation_count
        `;
console.log(violationCounts);
        const uniformViolationCount = violationCounts.length > 0 ? parseInt(violationCounts[0].uniform_violation_count) || 0 : 0;
        const cardViolationCount = violationCounts.length > 0 ? parseInt(violationCounts[0].card_violation_count) || 0 : 0;

        const attendanceViolationCount = await this.getAttendanceViolationCount(userId, year, month);

        const totalViolationCount = uniformViolationCount + cardViolationCount + attendanceViolationCount;

        return {
            uniformViolationCount,
            cardViolationCount,
            attendanceViolationCount,
            totalViolationCount
        };
    }

    /**
     * Đếm vi phạm cho nhiều user trong tháng theo ca làm việc
     * @param userIds Danh sách ID của users
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Map với key là userId và value là ViolationCountResult
     */
    async countViolationsByUsersAndMonth(
        userIds: string[],
        year: number,
        month: number
    ): Promise<Map<string, ViolationCountResultGql>> {
        if (userIds.length === 0) {
            return new Map();
        }

        const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).startOf('day').toDate();
        const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).endOf('month').toDate();

        // Query tối ưu cho nhiều users - đồng phục: mỗi ngày 1 lần, thẻ: đếm tất cả lần
        const violationCounts = await this.prisma.$queryRaw<Array<{
            user_id: string;
            uniform_violation_count: string;
            card_violation_count: string;
        }>>`
            WITH user_uniform_violations AS (
                -- Đồng phục: đếm số ngày vi phạm cho mỗi user
                SELECT
                    user_id,
                    COUNT(DISTINCT violation_date) as uniform_count
                FROM (
                    SELECT DISTINCT
                        result."userIdReCheckResult" as user_id,
                        attr."dateCreated"::date as violation_date
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        -- Loại trừ thời gian nghỉ giải lao
                        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                                AND shift_type."startBreak" != '00:00'
                                AND shift_type."endBreak" != '00:00')
                        -- Vi phạm đồng phục chỉ tính vào thứ 2 (1 = Monday trong PostgreSQL)
                        AND EXTRACT(DOW FROM attr."dateCreated") = 1
                        AND result."userIdReCheckResult" = ANY(${userIds})
                        AND attr."type" = ${PersonMctAttributeEnum.none_ppe_body}

                    UNION ALL

                    SELECT DISTINCT
                        face."userId" as user_id,
                        attr."dateCreated"::date as violation_date
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        -- Loại trừ thời gian nghỉ giải lao
                        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                                AND shift_type."startBreak" != '00:00'
                                AND shift_type."endBreak" != '00:00')
                        -- Vi phạm đồng phục chỉ tính vào thứ 2 (1 = Monday trong PostgreSQL)
                        AND EXTRACT(DOW FROM attr."dateCreated") = 1
                        AND face."userId" = ANY(${userIds})
                        AND attr."type" = ${PersonMctAttributeEnum.none_ppe_body}
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackResult" r
                            WHERE r."humanTrackId" = attr."trackId"
                            AND r."userIdReCheckResult" IS NOT NULL
                        )
                ) uniform_violations
                GROUP BY user_id
            ),
            user_card_violations AS (
                -- Thẻ: đếm tất cả lần vi phạm cho mỗi user
                SELECT
                    user_id,
                    COUNT(*) as card_count
                FROM (
                    SELECT DISTINCT
                        result."userIdReCheckResult" as user_id,
                        attr."trackId"
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        -- Loại trừ thời gian nghỉ giải lao
                        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                                AND shift_type."startBreak" != '00:00'
                                AND shift_type."endBreak" != '00:00')
                        AND result."userIdReCheckResult" = ANY(${userIds})
                        AND attr."type" = ${PersonMctAttributeEnum.no_employee_card}
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                            WHERE attr2."trackId" = attr."trackId"
                            AND attr2."type" = ${PersonMctAttributeEnum.wearing_employee_card}
                        )

                    UNION ALL

                    SELECT DISTINCT
                        face."userId" as user_id,
                        attr."trackId"
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        -- Loại trừ thời gian nghỉ giải lao
                        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                                AND shift_type."startBreak" != '00:00'
                                AND shift_type."endBreak" != '00:00')
                        AND face."userId" = ANY(${userIds})
                        AND attr."type" = ${PersonMctAttributeEnum.no_employee_card}
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                            WHERE attr2."trackId" = attr."trackId"
                            AND attr2."type" = ${PersonMctAttributeEnum.wearing_employee_card}
                        )
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackResult" r
                            WHERE r."humanTrackId" = attr."trackId"
                            AND r."userIdReCheckResult" IS NOT NULL
                        )
                ) card_violations
                GROUP BY user_id
            )
            SELECT
                u.user_id,
                COALESCE(uu.uniform_count, 0)::text as uniform_violation_count,
                COALESCE(uc.card_count, 0)::text as card_violation_count
            FROM (
                SELECT DISTINCT user_id FROM (
                    SELECT result."userIdReCheckResult" as user_id FROM "CoreAiHumanTrackResult" result WHERE result."userIdReCheckResult" = ANY(${userIds})
                    UNION
                    SELECT face."userId" as user_id FROM "FaceResult" face WHERE face."userId" = ANY(${userIds})
                ) all_users
            ) u
            LEFT JOIN user_uniform_violations uu ON u.user_id = uu.user_id
            LEFT JOIN user_card_violations uc ON u.user_id = uc.user_id
        `;

        const attendanceViolationCountsMap = await this.getAttendanceViolationCounts(userIds, year, month);

        const resultMap = new Map<string, ViolationCountResultGql>();

        userIds.forEach(userId => {
            const attendanceViolationCount = attendanceViolationCountsMap.get(userId) || 0;
            resultMap.set(userId, {
                uniformViolationCount: 0,
                cardViolationCount: 0,
                attendanceViolationCount: attendanceViolationCount,
                totalViolationCount: attendanceViolationCount
            });
        });

        violationCounts.forEach(row => {
            const uniformViolationCount = parseInt(row.uniform_violation_count) || 0;
            const cardViolationCount = parseInt(row.card_violation_count) || 0;
            const attendanceViolationCount = attendanceViolationCountsMap.get(row.user_id) || 0;
            const totalViolationCount = uniformViolationCount + cardViolationCount + attendanceViolationCount;

            resultMap.set(row.user_id, {
                uniformViolationCount,
                cardViolationCount,
                attendanceViolationCount,
                totalViolationCount
            });
        });

        return resultMap;
    }

    /**
     * Lấy số lần vi phạm chấm công (phạt tiền do đi muộn về sớm)
     * @param userId ID của user
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Số lần vi phạm chấm công
     */
    private async getAttendanceViolationCount(userId: string, year: number, month: number): Promise<number> {
        const calculatedWork = await this.prisma.calculatedWorkMonth.findFirst({
            where: {
                userId: userId,
                year: year,
                month: month
            },
            select: {
                phatTien: true
            }
        });

        return calculatedWork?.phatTien || 0;
    }

    /**
     * Lấy số lần vi phạm chấm công cho nhiều users
     * @param userIds Danh sách ID của users
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Map với key là userId và value là số lần vi phạm chấm công
     */
    private async getAttendanceViolationCounts(userIds: string[], year: number, month: number): Promise<Map<string, number>> {
        const calculatedWorks = await this.prisma.calculatedWorkMonth.findMany({
            where: {
                userId: { in: userIds },
                year: year,
                month: month
            },
            select: {
                userId: true,
                phatTien: true
            }
        });

        const resultMap = new Map<string, number>();
        userIds.forEach(userId => {
            resultMap.set(userId, 0);
        });

        calculatedWorks.forEach(work => {
            resultMap.set(work.userId, work.phatTien || 0);
        });

        return resultMap;
    }
}
