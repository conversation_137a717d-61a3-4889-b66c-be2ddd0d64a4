import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { PersonMctAttributeEnum } from 'src/smart-search/models/person-mct-att.enum';
import * as moment from 'moment';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';
import { ViolationCountResultGql } from './models/gql';

@Injectable()
export class ViolationCountService {
    constructor(
        private prisma: PrismaService,
        private cachedGlobalConfig: CachedGlobalConfigService,
    ) { }

    /**
     * Đếm số lượt vi phạm đồng phục và thẻ của user trong tháng theo ca làm việc
     * @param userId ID của user
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Object chứa số lượt vi phạm
     */
    async countViolationsByUserAndMonth(
        userId: string,
        year: number,
        month: number
    ): Promise<ViolationCountResultGql> {
        const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).startOf('day').toDate();
        const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).endOf('month').toDate();

        // Query tối ưu - mỗi ngày chỉ tính 1 lần vi phạm cho mỗi loại
        const violationCounts = await this.prisma.$queryRaw<Array<{
            uniform_violation_count: string;
            card_violation_count: string;
        }>>`
            SELECT
                COUNT(CASE WHEN has_uniform_violation THEN 1 END)::text as uniform_violation_count,
                COUNT(CASE WHEN has_card_violation THEN 1 END)::text as card_violation_count
            FROM (
                -- Group theo ngày để mỗi ngày chỉ tính 1 lần vi phạm cho mỗi loại
                SELECT
                    violation_date,
                    BOOL_OR(type = ${PersonMctAttributeEnum.none_ppe_body}) as has_uniform_violation,
                    -- Vi phạm thẻ: chỉ tính khi có no_employee_card và KHÔNG có wearing_employee_card
                    (BOOL_OR(type = ${PersonMctAttributeEnum.no_employee_card})
                     AND NOT BOOL_OR(type = ${PersonMctAttributeEnum.wearing_employee_card})) as has_card_violation
                FROM (
                    -- Vi phạm từ CoreAiHumanTrackResult (manual recheck) - ưu tiên cao hơn
                    SELECT DISTINCT
                        attr."dateCreated"::date as violation_date,
                        attr."type"
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        AND result."userIdReCheckResult" = ${userId}
                        AND attr."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card}, ${PersonMctAttributeEnum.wearing_employee_card})

                    UNION ALL

                    -- Vi phạm từ FaceResult (face recognition) - chỉ khi không có manual recheck
                    SELECT DISTINCT
                        attr."dateCreated"::date as violation_date,
                        attr."type"
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        AND face."userId" = ${userId}
                        AND attr."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card}, ${PersonMctAttributeEnum.wearing_employee_card})
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackResult" r
                            WHERE r."humanTrackId" = attr."trackId"
                            AND r."userIdReCheckResult" IS NOT NULL
                        )
                ) daily_violations
                GROUP BY violation_date
            ) violation_days
        `;

        const uniformViolationCount = violationCounts.length > 0 ? parseInt(violationCounts[0].uniform_violation_count) || 0 : 0;
        const cardViolationCount = violationCounts.length > 0 ? parseInt(violationCounts[0].card_violation_count) || 0 : 0;

        const attendanceViolationCount = await this.getAttendanceViolationCount(userId, year, month);

        const totalViolationCount = uniformViolationCount + cardViolationCount + attendanceViolationCount;

        return {
            uniformViolationCount,
            cardViolationCount,
            attendanceViolationCount,
            totalViolationCount
        };
    }

    /**
     * Đếm vi phạm cho nhiều user trong tháng theo ca làm việc
     * @param userIds Danh sách ID của users
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Map với key là userId và value là ViolationCountResult
     */
    async countViolationsByUsersAndMonth(
        userIds: string[],
        year: number,
        month: number
    ): Promise<Map<string, ViolationCountResultGql>> {
        if (userIds.length === 0) {
            return new Map();
        }

        const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).startOf('day').toDate();
        const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).endOf('month').toDate();

        // Query tối ưu cho nhiều users - mỗi ngày chỉ tính 1 lần vi phạm cho mỗi loại
        const violationCounts = await this.prisma.$queryRaw<Array<{
            user_id: string;
            uniform_violation_count: string;
            card_violation_count: string;
        }>>`
            SELECT
                user_id,
                COUNT(CASE WHEN has_uniform_violation THEN 1 END)::text as uniform_violation_count,
                COUNT(CASE WHEN has_card_violation THEN 1 END)::text as card_violation_count
            FROM (
                -- Group theo user và ngày để mỗi ngày chỉ tính 1 lần vi phạm cho mỗi loại
                SELECT
                    user_id,
                    violation_date,
                    BOOL_OR(type = ${PersonMctAttributeEnum.none_ppe_body}) as has_uniform_violation,
                    -- Vi phạm thẻ: chỉ tính khi có no_employee_card và KHÔNG có wearing_employee_card
                    (BOOL_OR(type = ${PersonMctAttributeEnum.no_employee_card})
                     AND NOT BOOL_OR(type = ${PersonMctAttributeEnum.wearing_employee_card})) as has_card_violation
                FROM (
                    -- Vi phạm từ CoreAiHumanTrackResult (manual recheck) - ưu tiên cao hơn
                    SELECT DISTINCT
                        result."userIdReCheckResult" as user_id,
                        attr."dateCreated"::date as violation_date,
                        attr."type"
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        AND result."userIdReCheckResult" = ANY(${userIds})
                        AND attr."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card}, ${PersonMctAttributeEnum.wearing_employee_card})

                    UNION ALL

                    -- Vi phạm từ FaceResult (face recognition) - chỉ khi không có manual recheck
                    SELECT DISTINCT
                        face."userId" as user_id,
                        attr."dateCreated"::date as violation_date,
                        attr."type"
                    FROM "CoreAiHumanTrackAttribute" attr
                    INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
                    INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
                    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                    WHERE attr."dateCreated" >= ${startDate}
                        AND attr."dateCreated" <= ${endDate}
                        AND attr."dateCreated"::date = shift_user."startDate"::date
                        AND attr."dateCreated"::time >= shift_type."startHour"::time
                        AND attr."dateCreated"::time <= shift_type."endHour"::time
                        AND face."userId" = ANY(${userIds})
                        AND attr."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card}, ${PersonMctAttributeEnum.wearing_employee_card})
                        AND NOT EXISTS (
                            SELECT 1 FROM "CoreAiHumanTrackResult" r
                            WHERE r."humanTrackId" = attr."trackId"
                            AND r."userIdReCheckResult" IS NOT NULL
                        )
                ) daily_violations
                GROUP BY user_id, violation_date
            ) violation_days
            GROUP BY user_id
        `;

        const attendanceViolationCountsMap = await this.getAttendanceViolationCounts(userIds, year, month);

        const resultMap = new Map<string, ViolationCountResultGql>();

        userIds.forEach(userId => {
            const attendanceViolationCount = attendanceViolationCountsMap.get(userId) || 0;
            resultMap.set(userId, {
                uniformViolationCount: 0,
                cardViolationCount: 0,
                attendanceViolationCount: attendanceViolationCount,
                totalViolationCount: attendanceViolationCount
            });
        });

        violationCounts.forEach(row => {
            const uniformViolationCount = parseInt(row.uniform_violation_count) || 0;
            const cardViolationCount = parseInt(row.card_violation_count) || 0;
            const attendanceViolationCount = attendanceViolationCountsMap.get(row.user_id) || 0;
            const totalViolationCount = uniformViolationCount + cardViolationCount + attendanceViolationCount;

            resultMap.set(row.user_id, {
                uniformViolationCount,
                cardViolationCount,
                attendanceViolationCount,
                totalViolationCount
            });
        });

        return resultMap;
    }

    /**
     * Lấy số lần vi phạm chấm công (phạt tiền do đi muộn về sớm)
     * @param userId ID của user
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Số lần vi phạm chấm công
     */
    private async getAttendanceViolationCount(userId: string, year: number, month: number): Promise<number> {
        const calculatedWork = await this.prisma.calculatedWorkMonth.findFirst({
            where: {
                userId: userId,
                year: year,
                month: month
            },
            select: {
                phatTien: true
            }
        });

        return calculatedWork?.phatTien || 0;
    }

    /**
     * Lấy số lần vi phạm chấm công cho nhiều users
     * @param userIds Danh sách ID của users
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Map với key là userId và value là số lần vi phạm chấm công
     */
    private async getAttendanceViolationCounts(userIds: string[], year: number, month: number): Promise<Map<string, number>> {
        const calculatedWorks = await this.prisma.calculatedWorkMonth.findMany({
            where: {
                userId: { in: userIds },
                year: year,
                month: month
            },
            select: {
                userId: true,
                phatTien: true
            }
        });

        const resultMap = new Map<string, number>();
        userIds.forEach(userId => {
            resultMap.set(userId, 0);
        });

        calculatedWorks.forEach(work => {
            resultMap.set(work.userId, work.phatTien || 0);
        });

        return resultMap;
    }
}
