import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { PersonMctAttributeEnum } from 'src/smart-search/models/person-mct-att.enum';
import * as moment from 'moment';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';
import { ViolationCountResultGql } from './models/gql';

@Injectable()
export class ViolationCountService {
    constructor(
        private prisma: PrismaService,
        private cachedGlobalConfig: CachedGlobalConfigService,
    ) { }

    /**
     * Đếm số lượt vi phạm đồng phục và thẻ của user trong tháng theo ca làm việc
     * @param userId ID của user
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Object chứa số lượt vi phạm
     */
    async countViolationsByUserAndMonth(
        userId: string,
        year: number,
        month: number
    ): Promise<ViolationCountResultGql> {
        // Tạo ngày bắt đầu và kết thúc của tháng
        const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).startOf('day').toDate();
        const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).endOf('month').toDate();

        // Query tối ưu để đếm vi phạm theo ca làm việc (thay vì fix cứng 08:30-17:30)
        const violationCounts = await this.prisma.$queryRaw<Array<{
            uniform_violation_count: string;
            card_violation_count: string;
        }>>`
            SELECT
                COUNT(CASE WHEN type = ${PersonMctAttributeEnum.none_ppe_body} THEN 1 END)::text as uniform_violation_count,
                COUNT(CASE WHEN type = ${PersonMctAttributeEnum.no_employee_card} THEN 1 END)::text as card_violation_count
            FROM (
                -- Vi phạm từ CoreAiHumanTrackResult (manual recheck) - ưu tiên cao hơn
                SELECT DISTINCT
                    attr."trackId",
                    attr."type"
                FROM "CoreAiHumanTrackAttribute" attr
                INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
                INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
                INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                WHERE attr."dateCreated" >= ${startDate}
                    AND attr."dateCreated" <= ${endDate}
                    -- Lọc theo ca làm việc: chỉ trong khoảng thời gian ca của user
                    AND attr."dateCreated"::date = shift_user."startDate"::date
                    AND attr."dateCreated"::time >= shift_type."startHour"::time
                    AND attr."dateCreated"::time <= shift_type."endHour"::time
                    AND result."userIdReCheckResult" = ${userId}
                    AND attr."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card})

                UNION ALL

                -- Vi phạm từ FaceResult (face recognition) - chỉ khi không có manual recheck
                SELECT DISTINCT
                    attr."trackId",
                    attr."type"
                FROM "CoreAiHumanTrackAttribute" attr
                INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
                INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
                INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                WHERE attr."dateCreated" >= ${startDate}
                    AND attr."dateCreated" <= ${endDate}
                    -- Lọc theo ca làm việc: chỉ trong khoảng thời gian ca của user
                    AND attr."dateCreated"::date = shift_user."startDate"::date
                    AND attr."dateCreated"::time >= shift_type."startHour"::time
                    AND attr."dateCreated"::time <= shift_type."endHour"::time
                    AND face."userId" = ${userId}
                    AND attr."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card})
                    -- Chỉ lấy nếu không có manual recheck cho track này
                    AND NOT EXISTS (
                        SELECT 1 FROM "CoreAiHumanTrackResult" r
                        WHERE r."humanTrackId" = attr."trackId"
                        AND r."userIdReCheckResult" IS NOT NULL
                    )
            ) violations
        `;

        const uniformViolationCount = violationCounts.length > 0 ? parseInt(violationCounts[0].uniform_violation_count) || 0 : 0;
        const cardViolationCount = violationCounts.length > 0 ? parseInt(violationCounts[0].card_violation_count) || 0 : 0;
        const totalViolationCount = uniformViolationCount + cardViolationCount;

        return {
            uniformViolationCount,
            cardViolationCount,
            totalViolationCount
        };
    }

    /**
     * Đếm vi phạm cho nhiều user trong tháng theo ca làm việc
     * @param userIds Danh sách ID của users
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Map với key là userId và value là ViolationCountResult
     */
    async countViolationsByUsersAndMonth(
        userIds: string[],
        year: number,
        month: number
    ): Promise<Map<string, ViolationCountResultGql>> {
        if (userIds.length === 0) {
            return new Map();
        }

        const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).startOf('day').toDate();
        const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).endOf('month').toDate();

        // Query tối ưu để đếm vi phạm cho nhiều users theo ca làm việc
        const violationCounts = await this.prisma.$queryRaw<Array<{
            user_id: string;
            uniform_violation_count: string;
            card_violation_count: string;
        }>>`
            SELECT
                user_id,
                COUNT(CASE WHEN type = ${PersonMctAttributeEnum.none_ppe_body} THEN 1 END)::text as uniform_violation_count,
                COUNT(CASE WHEN type = ${PersonMctAttributeEnum.no_employee_card} THEN 1 END)::text as card_violation_count
            FROM (
                -- Vi phạm từ CoreAiHumanTrackResult (manual recheck)
                SELECT DISTINCT
                    attr."trackId",
                    attr."type",
                    result."userIdReCheckResult" as user_id
                FROM "CoreAiHumanTrackAttribute" attr
                INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
                INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
                INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                WHERE attr."dateCreated" >= ${startDate}
                    AND attr."dateCreated" <= ${endDate}
                    -- Lọc theo ca làm việc: chỉ trong khoảng thời gian ca của user
                    AND attr."dateCreated"::date = shift_user."startDate"::date
                    AND attr."dateCreated"::time >= shift_type."startHour"::time
                    AND attr."dateCreated"::time <= shift_type."endHour"::time
                    AND result."userIdReCheckResult" = ANY(${userIds})
                    AND attr."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card})

                UNION ALL

                -- Vi phạm từ FaceResult (face recognition)
                SELECT DISTINCT
                    attr."trackId",
                    attr."type",
                    face."userId" as user_id
                FROM "CoreAiHumanTrackAttribute" attr
                INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
                INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
                INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
                WHERE attr."dateCreated" >= ${startDate}
                    AND attr."dateCreated" <= ${endDate}
                    -- Lọc theo ca làm việc: chỉ trong khoảng thời gian ca của user
                    AND attr."dateCreated"::date = shift_user."startDate"::date
                    AND attr."dateCreated"::time >= shift_type."startHour"::time
                    AND attr."dateCreated"::time <= shift_type."endHour"::time
                    AND face."userId" = ANY(${userIds})
                    AND attr."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card})
                    -- Chỉ lấy nếu không có manual recheck
                    AND NOT EXISTS (
                        SELECT 1 FROM "CoreAiHumanTrackResult" r
                        WHERE r."humanTrackId" = attr."trackId"
                        AND r."userIdReCheckResult" IS NOT NULL
                    )
            ) violations
            GROUP BY user_id
        `;

        const resultMap = new Map<string, ViolationCountResultGql>();

        // Khởi tạo tất cả users với count = 0
        userIds.forEach(userId => {
            resultMap.set(userId, {
                uniformViolationCount: 0,
                cardViolationCount: 0,
                totalViolationCount: 0
            });
        });

        // Cập nhật với dữ liệu thực tế
        violationCounts.forEach(row => {
            const uniformViolationCount = parseInt(row.uniform_violation_count) || 0;
            const cardViolationCount = parseInt(row.card_violation_count) || 0;
            const totalViolationCount = uniformViolationCount + cardViolationCount;

            resultMap.set(row.user_id, {
                uniformViolationCount,
                cardViolationCount,
                totalViolationCount
            });
        });

        return resultMap;
    }
}
