import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { PersonMctAttributeEnum } from 'src/smart-search/models/person-mct-att.enum';
import * as moment from 'moment';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';

export interface ViolationCountResult {
    uniformViolationCount: number;
    cardViolationCount: number;
    totalViolationCount: number;
}

@Injectable()
export class ViolationCountService {
    constructor(
        private prisma: PrismaService,
        private cachedGlobalConfig: CachedGlobalConfigService,
    ) { }

    /**
     * Đếm số lượt vi phạm đồng phục và thẻ của user trong tháng
     * @param userId ID của user
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Object chứa số lượt vi phạm
     */
    async countViolationsByUserAndMonth(
        userId: string,
        year: number,
        month: number
    ): Promise<ViolationCountResult> {
        // Tạo ngày bắt đầu và kết thúc của tháng
        const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).startOf('day').toDate();
        const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).endOf('month').toDate();

        // Query để đếm vi phạm đồng phục và thẻ
        const violationCounts = await this.prisma.$queryRaw<Array<{
            uniform_violation_count: string;
            card_violation_count: string;
        }>>`
            WITH UserViolations AS (
                SELECT
                    "CoreAiHumanTrackAttribute"."trackId",
                    "CoreAiHumanTrackAttribute"."type",
                    "CoreAiHumanTrackAttribute"."dateCreated"
                FROM "CoreAiHumanTrackAttribute"
                LEFT JOIN "CoreAiHumanTrackInfo" ON "CoreAiHumanTrackAttribute"."trackId" = "CoreAiHumanTrackInfo"."trackId"
                LEFT JOIN "CoreAiHumanTrackResult" ON "CoreAiHumanTrackAttribute"."trackId" = "CoreAiHumanTrackResult"."humanTrackId"
                LEFT JOIN "FaceResult" ON "CoreAiHumanTrackAttribute"."trackId" = "FaceResult"."humanTrackId"
                WHERE "CoreAiHumanTrackAttribute"."dateCreated" >= ${startDate}
                    AND "CoreAiHumanTrackAttribute"."dateCreated" <= ${endDate}
                    AND EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") >= 8
                    AND (
                        EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") < 17
                        OR (
                            EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") = 17
                            AND EXTRACT(MINUTE FROM "CoreAiHumanTrackAttribute"."dateCreated") <= 30
                        )
                    )
                    AND (
                        EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") > 8
                        OR (
                            EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") = 8
                            AND EXTRACT(MINUTE FROM "CoreAiHumanTrackAttribute"."dateCreated") >= 30
                        )
                    )
                    AND (
                        "CoreAiHumanTrackResult"."userIdReCheckResult" = ${userId}
                        OR "FaceResult"."userId" = ${userId}
                    )
                    AND "CoreAiHumanTrackAttribute"."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card})
            ),
            TrackViolations AS (
                SELECT
                    "trackId",
                    ARRAY_AGG(DISTINCT "type") AS violation_types
                FROM UserViolations
                GROUP BY "trackId"
            )
            SELECT
                COUNT(CASE WHEN ${PersonMctAttributeEnum.none_ppe_body} = ANY(violation_types) THEN 1 END)::text as uniform_violation_count,
                COUNT(CASE WHEN ${PersonMctAttributeEnum.no_employee_card} = ANY(violation_types) THEN 1 END)::text as card_violation_count
            FROM TrackViolations
        `;

        const uniformViolationCount = violationCounts.length > 0 ? parseInt(violationCounts[0].uniform_violation_count) || 0 : 0;
        const cardViolationCount = violationCounts.length > 0 ? parseInt(violationCounts[0].card_violation_count) || 0 : 0;
        const totalViolationCount = uniformViolationCount + cardViolationCount;

        return {
            uniformViolationCount,
            cardViolationCount,
            totalViolationCount
        };
    }

    /**
     * Đếm vi phạm cho nhiều user trong tháng
     * @param userIds Danh sách ID của users
     * @param year Năm
     * @param month Tháng (1-12)
     * @returns Map với key là userId và value là ViolationCountResult
     */
    async countViolationsByUsersAndMonth(
        userIds: string[],
        year: number,
        month: number
    ): Promise<Map<string, ViolationCountResult>> {
        if (userIds.length === 0) {
            return new Map();
        }

        const startDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).startOf('day').toDate();
        const endDate = moment.tz(`${year}-${month.toString().padStart(2, '0')}-01`, this.cachedGlobalConfig.timeZoneName).endOf('month').toDate();

        // Query để đếm vi phạm cho tất cả users
        const violationCounts = await this.prisma.$queryRaw<Array<{
            user_id: string;
            uniform_violation_count: string;
            card_violation_count: string;
        }>>`
            WITH UserViolations AS (
                SELECT
                    "CoreAiHumanTrackAttribute"."trackId",
                    "CoreAiHumanTrackAttribute"."type",
                    "CoreAiHumanTrackAttribute"."dateCreated",
                    COALESCE("CoreAiHumanTrackResult"."userIdReCheckResult", "FaceResult"."userId") as user_id
                FROM "CoreAiHumanTrackAttribute"
                LEFT JOIN "CoreAiHumanTrackInfo" ON "CoreAiHumanTrackAttribute"."trackId" = "CoreAiHumanTrackInfo"."trackId"
                LEFT JOIN "CoreAiHumanTrackResult" ON "CoreAiHumanTrackAttribute"."trackId" = "CoreAiHumanTrackResult"."humanTrackId"
                LEFT JOIN "FaceResult" ON "CoreAiHumanTrackAttribute"."trackId" = "FaceResult"."humanTrackId"
                WHERE "CoreAiHumanTrackAttribute"."dateCreated" >= ${startDate}
                    AND "CoreAiHumanTrackAttribute"."dateCreated" <= ${endDate}
                    -- Chỉ lấy vi phạm trong giờ làm việc (08:30 - 17:30)
                    AND EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") >= 8
                    AND (
                        EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") < 17
                        OR (
                            EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") = 17
                            AND EXTRACT(MINUTE FROM "CoreAiHumanTrackAttribute"."dateCreated") <= 30
                        )
                    )
                    AND (
                        EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") > 8
                        OR (
                            EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") = 8
                            AND EXTRACT(MINUTE FROM "CoreAiHumanTrackAttribute"."dateCreated") >= 30
                        )
                    )
                    AND (
                        "CoreAiHumanTrackResult"."userIdReCheckResult" = ANY(${userIds})
                        OR "FaceResult"."userId" = ANY(${userIds})
                    )
                    AND "CoreAiHumanTrackAttribute"."type" IN (${PersonMctAttributeEnum.none_ppe_body}, ${PersonMctAttributeEnum.no_employee_card})
                    AND COALESCE("CoreAiHumanTrackResult"."userIdReCheckResult", "FaceResult"."userId") IS NOT NULL
            ),
            TrackViolations AS (
                SELECT
                    "trackId",
                    "user_id",
                    ARRAY_AGG(DISTINCT "type") AS violation_types
                FROM UserViolations
                GROUP BY "trackId", "user_id"
            )
            SELECT
                user_id,
                COUNT(CASE WHEN ${PersonMctAttributeEnum.none_ppe_body} = ANY(violation_types) THEN 1 END)::text as uniform_violation_count,
                COUNT(CASE WHEN ${PersonMctAttributeEnum.no_employee_card} = ANY(violation_types) THEN 1 END)::text as card_violation_count
            FROM TrackViolations
            GROUP BY user_id
        `;

        const resultMap = new Map<string, ViolationCountResult>();

        // Khởi tạo tất cả users với count = 0
        userIds.forEach(userId => {
            resultMap.set(userId, {
                uniformViolationCount: 0,
                cardViolationCount: 0,
                totalViolationCount: 0
            });
        });

        // Cập nhật với dữ liệu thực tế
        violationCounts.forEach(row => {
            const uniformViolationCount = parseInt(row.uniform_violation_count) || 0;
            const cardViolationCount = parseInt(row.card_violation_count) || 0;
            const totalViolationCount = uniformViolationCount + cardViolationCount;

            resultMap.set(row.user_id, {
                uniformViolationCount,
                cardViolationCount,
                totalViolationCount
            });
        });

        return resultMap;
    }
}
