# Violation Count Service

## M<PERSON> tả
Service này được tạo để đếm số lượt vi phạm đeo thẻ và đồng phục của người dùng dựa trên dữ liệu từ hệ thống AI tracking.

## Chức năng chính

### 1. <PERSON><PERSON><PERSON> vi phạm cho một user trong tháng
```typescript
countViolationsByUserAndMonth(userId: string, year: number, month: number): Promise<ViolationCountResult>
```

### 2. Đếm vi phạm cho nhiều users trong tháng
```typescript
countViolationsByUsersAndMonth(userIds: string[], year: number, month: number): Promise<Map<string, ViolationCountResult>>
```

## Loại vi phạm được đếm

### Vi phạm đồng phục (Uniform Violation)
- Ph<PERSON><PERSON> hiện khi có `PersonMctAttributeEnum.none_ppe_body` (16) trong track
- Kh<PERSON>ng có `PersonMctAttributeEnum.wearing_ppe_body` (15) trong cùng track

### Vi phạm thẻ (Card Violation)  
- <PERSON><PERSON><PERSON> hiện khi có `PersonMctAttributeEnum.no_employee_card` (18) trong track
- Không có `PersonMctAttributeEnum.wearing_employee_card` (17) trong cùng track

## Cách hoạt động

1. Service query dữ liệu từ bảng `CoreAiHumanTrackAttribute`
2. Kết hợp với `CoreAiHumanTrackResult` và `FaceResult` để xác định user
3. **Lọc theo giờ làm việc**: Chỉ đếm vi phạm từ 08:30 đến 17:30 các ngày
4. Nhóm theo `trackId` và đếm số track có vi phạm
5. Trả về kết quả với số lượt vi phạm từng loại và tổng cộng

## Giờ làm việc theo ca (Shift-based Working Hours)

Service đếm vi phạm dựa trên **ca làm việc thực tế** của từng user thay vì fix cứng 08:30-17:30:

### Cách hoạt động:
1. **Lấy thông tin ca làm việc**: Từ bảng `ShiftUser` và `ShiftType`
2. **Áp dụng theo ngày**: Mỗi ngày user có thể có ca làm việc khác nhau
3. **Linh hoạt**: Hỗ trợ nhiều loại ca (sáng, chiều, đêm, ca xoay)

### Ví dụ:
- **Ca sáng**: 08:00 - 17:00 → Chỉ đếm vi phạm từ 08:00 đến 17:00
- **Ca chiều**: 13:00 - 22:00 → Chỉ đếm vi phạm từ 13:00 đến 22:00
- **Ca đêm**: 22:00 - 06:00 → Chỉ đếm vi phạm từ 22:00 đến 06:00 hôm sau
- **Ca hành chính**: 08:30 - 17:30 → Chỉ đếm vi phạm từ 08:30 đến 17:30

### Logic SQL:
```sql
-- Join với ShiftUser và ShiftType để lấy thời gian ca
INNER JOIN "ShiftUser" shift_user ON user_id = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"

-- Lọc theo ngày và thời gian ca làm việc
WHERE DATE(attr."dateCreated") = DATE(shift_user."startDate")
    AND TIME(attr."dateCreated") >= TIME(shift_type."startHour")
    AND TIME(attr."dateCreated") <= TIME(shift_type."endHour")
```

### Ưu điểm:
- ✅ **Chính xác**: Đếm vi phạm đúng theo ca làm việc thực tế
- ✅ **Linh hoạt**: Hỗ trợ nhiều loại ca khác nhau
- ✅ **Tự động**: Không cần cấu hình thủ công
- ✅ **Công bằng**: Không đếm vi phạm ngoài giờ làm việc

## Tích hợp với Export Work Month

Service này đã được tích hợp vào:
- `CalculatedWorkMonthService.exportWorkRange()`
- `ShiftUserService.exportWorkMonthNew()`

Trong file Excel export sẽ có thêm 3 cột:
- **Vi phạm đồng phục**: Số lượt vi phạm không đeo đồng phục
- **Vi phạm thẻ**: Số lượt vi phạm không đeo thẻ  
- **Tổng số lần vi phạm**: Tổng cộng tất cả vi phạm

## Database Schema

Đã thêm 3 trường mới vào bảng `CalculatedWorkMonth`:
```sql
uniformViolationCount INTEGER NOT NULL DEFAULT 0
cardViolationCount    INTEGER NOT NULL DEFAULT 0  
totalViolationCount   INTEGER NOT NULL DEFAULT 0
```

## GraphQL Schema

Đã cập nhật `CalculatedWorkMonthGql` với 3 trường mới:
```graphql
uniformViolationCount: Int!
cardViolationCount: Int!
totalViolationCount: Int!
```
