# Violation Count Service

## M<PERSON> tả
Service này được tạo để đếm số lượt vi phạm đeo thẻ và đồng phục của người dùng dựa trên dữ liệu từ hệ thống AI tracking.

## Chức năng chính

### 1. <PERSON><PERSON><PERSON> vi phạm cho một user trong tháng
```typescript
countViolationsByUserAndMonth(userId: string, year: number, month: number): Promise<ViolationCountResult>
```

### 2. Đếm vi phạm cho nhiều users trong tháng
```typescript
countViolationsByUsersAndMonth(userIds: string[], year: number, month: number): Promise<Map<string, ViolationCountResult>>
```

## Loại vi phạm được đếm

### Vi phạm đồng phục (Uniform Violation)
- Ph<PERSON><PERSON> hiện khi có `PersonMctAttributeEnum.none_ppe_body` (16) trong track
- Kh<PERSON>ng có `PersonMctAttributeEnum.wearing_ppe_body` (15) trong cùng track

### Vi phạm thẻ (Card Violation)  
- <PERSON><PERSON><PERSON> hiện khi có `PersonMctAttributeEnum.no_employee_card` (18) trong track
- Không có `PersonMctAttributeEnum.wearing_employee_card` (17) trong cùng track

## Cách hoạt động

1. Service query dữ liệu từ bảng `CoreAiHumanTrackAttribute`
2. Kết hợp với `CoreAiHumanTrackResult` và `FaceResult` để xác định user
3. Nhóm theo `trackId` và đếm số track có vi phạm
4. Trả về kết quả với số lượt vi phạm từng loại và tổng cộng

## Tích hợp với Export Work Month

Service này đã được tích hợp vào:
- `CalculatedWorkMonthService.exportWorkRange()`
- `ShiftUserService.exportWorkMonthNew()`

Trong file Excel export sẽ có thêm 3 cột:
- **Vi phạm đồng phục**: Số lượt vi phạm không đeo đồng phục
- **Vi phạm thẻ**: Số lượt vi phạm không đeo thẻ  
- **Tổng số lần vi phạm**: Tổng cộng tất cả vi phạm

## Database Schema

Đã thêm 3 trường mới vào bảng `CalculatedWorkMonth`:
```sql
uniformViolationCount INTEGER NOT NULL DEFAULT 0
cardViolationCount    INTEGER NOT NULL DEFAULT 0  
totalViolationCount   INTEGER NOT NULL DEFAULT 0
```

## GraphQL Schema

Đã cập nhật `CalculatedWorkMonthGql` với 3 trường mới:
```graphql
uniformViolationCount: Int!
cardViolationCount: Int!
totalViolationCount: Int!
```
