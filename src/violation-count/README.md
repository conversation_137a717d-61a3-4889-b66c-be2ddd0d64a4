# Violation Count Service

## M<PERSON> tả
Service này được tạo để đếm số lượt vi phạm đeo thẻ và đồng phục của người dùng dựa trên dữ liệu từ hệ thống AI tracking.

## Chức năng chính

### 1. <PERSON><PERSON><PERSON> vi phạm cho một user trong tháng
```typescript
countViolationsByUserAndMonth(userId: string, year: number, month: number): Promise<ViolationCountResult>
```

### 2. Đếm vi phạm cho nhiều users trong tháng
```typescript
countViolationsByUsersAndMonth(userIds: string[], year: number, month: number): Promise<Map<string, ViolationCountResult>>
```

## Loại vi phạm được đếm

### Vi phạm đồng phục (Uniform Violation)
- Ph<PERSON><PERSON> hiện khi có `PersonMctAttributeEnum.none_ppe_body` (16) trong track
- Kh<PERSON>ng có `PersonMctAttributeEnum.wearing_ppe_body` (15) trong cùng track

### Vi phạm thẻ (Card Violation)  
- <PERSON><PERSON><PERSON> hiện khi có `PersonMctAttributeEnum.no_employee_card` (18) trong track
- Không có `PersonMctAttributeEnum.wearing_employee_card` (17) trong cùng track

## Cách hoạt động

1. Service query dữ liệu từ bảng `CoreAiHumanTrackAttribute`
2. Kết hợp với `CoreAiHumanTrackResult` và `FaceResult` để xác định user
3. **Lọc theo giờ làm việc**: Chỉ đếm vi phạm từ 08:30 đến 17:30 các ngày
4. Nhóm theo `trackId` và đếm số track có vi phạm
5. Trả về kết quả với số lượt vi phạm từng loại và tổng cộng

## Giờ làm việc

Service chỉ đếm vi phạm trong khoảng thời gian:
- **Từ**: 08:30 (8 giờ 30 phút)
- **Đến**: 17:30 (5 giờ 30 chiều)
- **Áp dụng**: Tất cả các ngày trong tháng

Logic SQL:
```sql
-- Từ 8h trở lên
EXTRACT(HOUR FROM "dateCreated") >= 8
-- Đến trước 17h hoặc đúng 17h30
AND (
    EXTRACT(HOUR FROM "dateCreated") < 17
    OR (
        EXTRACT(HOUR FROM "dateCreated") = 17
        AND EXTRACT(MINUTE FROM "dateCreated") <= 30
    )
)
-- Từ sau 8h30 trở đi
AND (
    EXTRACT(HOUR FROM "dateCreated") > 8
    OR (
        EXTRACT(HOUR FROM "dateCreated") = 8
        AND EXTRACT(MINUTE FROM "dateCreated") >= 30
    )
)
```

## Tích hợp với Export Work Month

Service này đã được tích hợp vào:
- `CalculatedWorkMonthService.exportWorkRange()`
- `ShiftUserService.exportWorkMonthNew()`

Trong file Excel export sẽ có thêm 3 cột:
- **Vi phạm đồng phục**: Số lượt vi phạm không đeo đồng phục
- **Vi phạm thẻ**: Số lượt vi phạm không đeo thẻ  
- **Tổng số lần vi phạm**: Tổng cộng tất cả vi phạm

## Database Schema

Đã thêm 3 trường mới vào bảng `CalculatedWorkMonth`:
```sql
uniformViolationCount INTEGER NOT NULL DEFAULT 0
cardViolationCount    INTEGER NOT NULL DEFAULT 0  
totalViolationCount   INTEGER NOT NULL DEFAULT 0
```

## GraphQL Schema

Đã cập nhật `CalculatedWorkMonthGql` với 3 trường mới:
```graphql
uniformViolationCount: Int!
cardViolationCount: Int!
totalViolationCount: Int!
```
