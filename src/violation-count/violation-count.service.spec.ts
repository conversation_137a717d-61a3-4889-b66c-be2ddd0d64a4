import { Test, TestingModule } from '@nestjs/testing';
import { ViolationCountService } from './violation-count.service';
import { PrismaService } from 'src/prisma/prisma.service';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';

describe('ViolationCountService', () => {
  let service: ViolationCountService;
  let prismaService: PrismaService;
  let cachedGlobalConfigService: CachedGlobalConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ViolationCountService,
        {
          provide: PrismaService,
          useValue: {
            $queryRaw: jest.fn(),
          },
        },
        {
          provide: CachedGlobalConfigService,
          useValue: {
            timeZoneName: 'Asia/Ho_Chi_Minh',
          },
        },
      ],
    }).compile();

    service = module.get<ViolationCountService>(ViolationCountService);
    prismaService = module.get<PrismaService>(PrismaService);
    cachedGlobalConfigService = module.get<CachedGlobalConfigService>(CachedGlobalConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('countViolationsByUserAndMonth', () => {
    it('should return violation counts for a user', async () => {
      const mockResult = [
        {
          uniform_violation_count: '5',
          card_violation_count: '3',
        },
      ];

      jest.spyOn(prismaService, '$queryRaw').mockResolvedValue(mockResult);

      const result = await service.countViolationsByUserAndMonth('user1', 2024, 12);

      expect(result).toEqual({
        uniformViolationCount: 5,
        cardViolationCount: 3,
        totalViolationCount: 8,
      });
    });

    it('should include working hours filter in query', async () => {
      const mockResult = [];
      const queryRawSpy = jest.spyOn(prismaService, '$queryRaw').mockResolvedValue(mockResult);

      await service.countViolationsByUserAndMonth('user1', 2024, 12);

      // Kiểm tra query có chứa điều kiện giờ làm việc
      const queryCall = queryRawSpy.mock.calls[0][0];
      expect(queryCall.toString()).toContain('EXTRACT(HOUR FROM "CoreAiHumanTrackAttribute"."dateCreated") >= 8');
      expect(queryCall.toString()).toContain('EXTRACT(MINUTE FROM "CoreAiHumanTrackAttribute"."dateCreated") >= 30');
      expect(queryCall.toString()).toContain('EXTRACT(MINUTE FROM "CoreAiHumanTrackAttribute"."dateCreated") <= 30');
    });

    it('should return zero counts when no violations found', async () => {
      jest.spyOn(prismaService, '$queryRaw').mockResolvedValue([]);

      const result = await service.countViolationsByUserAndMonth('user1', 2024, 12);

      expect(result).toEqual({
        uniformViolationCount: 0,
        cardViolationCount: 0,
        totalViolationCount: 0,
      });
    });
  });

  describe('countViolationsByUsersAndMonth', () => {
    it('should return violation counts for multiple users', async () => {
      const mockResult = [
        {
          user_id: 'user1',
          uniform_violation_count: '5',
          card_violation_count: '3',
        },
        {
          user_id: 'user2',
          uniform_violation_count: '2',
          card_violation_count: '1',
        },
      ];

      jest.spyOn(prismaService, '$queryRaw').mockResolvedValue(mockResult);

      const result = await service.countViolationsByUsersAndMonth(['user1', 'user2'], 2024, 12);

      expect(result.get('user1')).toEqual({
        uniformViolationCount: 5,
        cardViolationCount: 3,
        totalViolationCount: 8,
      });

      expect(result.get('user2')).toEqual({
        uniformViolationCount: 2,
        cardViolationCount: 1,
        totalViolationCount: 3,
      });
    });

    it('should return empty map when no users provided', async () => {
      const result = await service.countViolationsByUsersAndMonth([], 2024, 12);

      expect(result.size).toBe(0);
    });
  });
});
