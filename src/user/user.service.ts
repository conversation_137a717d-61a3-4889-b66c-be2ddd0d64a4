import { BadGatewayException, BadRequestException, ForbiddenException, Injectable, InternalServerErrorException, Logger, NotFoundException, OnModuleInit, UnauthorizedException } from '@nestjs/common';
const bcrypt = require('bcrypt');
import { PrismaService } from '../prisma/prisma.service';
import {
    User,
    ActiveStatus,
    Prisma,
    FaceImageType,
    RoleType,
    Company,
    Department,
    UserFaceImage,
    ServerConfigKey
} from '@prisma/client';
import { StringUtils } from 'src/utils/string';
import { RedisService } from 'src/redis/redis.service';
import { UserGql } from './models/user.gql';
import { base64Util } from 'src/utils/base64';
import { ProfileRequest } from './models/profile.request';
import { checkFileExt, checkFileSize, deleteFileAsync, readFileBuffer, writeBase64FileAsync, writeFileAsync } from 'src/utils/file';
import { join } from 'path';
import { getStaticFilesPath, getUserImagePath } from 'src/utils/constants';
import { RoleGql } from 'src/role/models/role.gql';
import { NumberStatus } from 'src/data/enums/status.enum';
import { UserAddInput } from './models/user.add.input';
import { CompanyGql } from 'src/company/models/company.gql';
import { DepartmentGql } from 'src/department/models/department.gql';
import { FaceImageGraphQL } from './models/face-image.dto';
import * as XLSX from 'xlsx';
import * as fs from 'fs';
import { NumberUtils } from 'src/utils/number';
import { LdapService } from 'src/ldap/ldap.service';
import { UserGeneralIntegrationService } from 'src/user-general-integration/user-general-integration.service';
import { UserClusterUpsertRequest } from './models/cluster-upsert.request';
import { ShiftUserService } from 'src/shift-user/shift-user.service';
import { UserSearchModel } from './models/user-search.model';
import { PatientService } from 'src/patient/patient.service';
import { SystemLogService } from 'src/system-log/system-log.service';
import { SqlAction } from 'src/data/enums/sql-action.enum';
import { FaceImageHandlerService } from 'src/face-image-handler/face-image-handler.service';
import { ErrorEnum } from 'src/data/enums/error.enum';
import { FaceImageQualityService } from 'src/face-image-quality/face-image-quality.service';
import { FaceQualityCheckRequest } from 'src/face-image-quality/models/check-quality.request';
import { FaceImageErrEnum } from 'src/face-image-quality/models/face-image-err.enum';
import { UserGateway } from 'src/socket-gateway/user.gateway';
import { UserAbilityGql } from './models/ability.gql';
import { OrganizationImportService } from 'src/organization-import/organization-import.service';
import { dateUtil } from 'src/utils/date.util';
import { OrganizationGQL } from 'src/organization/models/organization.gql';
import { excelUtils } from 'src/utils/excel';
import { JwtService } from '@nestjs/jwt';
import { jwtConstants } from 'configs/jwt.config';
import { ParkingService } from 'src/parking/parking.service';
import { UserFilterService } from 'src/user-filter/user-filter.service';
import { PasswordHandlerService } from 'src/password-handler/password-handler.service';
import { SystemLogLabelEnum } from 'src/system-log/models/enums';
import { AlertMessService } from 'src/alert-mess/alert-mess.service';
import { CachedGlobalConfigService } from 'src/cached-global-config/cached-global-config.service';
import { IParkingVehicleType } from 'src/i-parking-integration/models/i-parking-vehicle-type.enums';
import { IParkingVehicleService } from 'src/i-parking-integration/vehicle.service';

@Injectable()
export class UserService implements OnModuleInit {
    private readonly logger = new Logger(UserService.name);
    private isLoginUsingAd = false;
    private isLoginUsingSSO = false;
    private faceImagePath: string = join(getStaticFilesPath(), 'face-images');
    private avatarPath: string = join(getStaticFilesPath(), 'images', 'avatar');
    private isldapEnable = false;

    async onModuleInit() {
        let adEnableConfig = await this.prisma.serverConfig.findFirst({
            where: { key: ServerConfigKey.IS_USER_LOGIN_AD }
        });
        if (adEnableConfig.value === "true") this.isLoginUsingAd = true;

        let ldapEnableConfig = await this.prisma.serverConfig.findFirst({
            where: { key: ServerConfigKey.LDAP_ENABLE }
        });
        if (ldapEnableConfig) this.isldapEnable = ldapEnableConfig.value == "true" ? true : false;

        let ctelConfig = await this.prisma.ctelIntegrationConfig.findFirst();
        if (ctelConfig) {
            this.isLoginUsingSSO = true;
        }
    }
    constructor(
        private prisma: PrismaService,
        private redisService: RedisService,
        private userGeneralIntegrationService: UserGeneralIntegrationService,
        private ldapService: LdapService,
        private shiftUserService: ShiftUserService,
        private patientService: PatientService,
        private systemLog: SystemLogService,
        private faceImageHandler: FaceImageHandlerService,
        private faceImageQualityService: FaceImageQualityService,
        private userGateway: UserGateway,
        private orgImportService: OrganizationImportService,
        private jwtService: JwtService,
        private parkingService: ParkingService,
        private userFilterService: UserFilterService,
        private passHandlerService: PasswordHandlerService,
        private alertMessService: AlertMessService,
        private cachedGlobalConfig: CachedGlobalConfigService,
        private iparkingVehicleService: IParkingVehicleService,
    ) { }

    //#region User Login
    userTypeFromUser(
        user: User,
        accessToken: string = null
    ): UserGql {
        return new UserGql({
            id: user.id,
            fullName: user.name,
            avatar: user.avatar,
            avatarBase64: {
                path: user.avatar,
                data: base64Util.encodeImage(join(this.avatarPath, user.avatar))
            },
            email: user.email,
            accessToken: accessToken,
            roles: [],
        });
    }

    async userTypeFromUserWeb(user: User, accessToken: string = null): Promise<UserGql> {
        return new UserGql({
            id: user.id,
            fullName: user.name,
            avatar: user.avatar,
            avatarBase64: {
                path: user.avatar,
                // data: base64.encodeImage(join(this.avatarPath, user.avatar))
            },
            email: user.email,
            roles: [
                (new RoleGql({
                    id: user.roleId,
                    name: user.name,
                    description: user.name
                }))
            ]
        });
    }

    async getUserAbility(
        roleId: RoleType
    ): Promise<UserAbilityGql[]> {
        let ability: UserAbilityGql[] = [];
        if (roleId == RoleType.SUPER_ADMIN) {
            ability.push(new UserAbilityGql({
                action: "manage",
                subject: "all"
            }));
        }
        else {
            let permissions = await this.prisma.permisson.findMany({
                where: { roleId: roleId }
            });
            for (let index = 0; index < permissions.length; index++) {
                const permission = permissions[index];
                // if ((permission.roleId == "I_PARKING_VIEWER" || permission.roleId == "I_PARKING_ADMIN") && permission.functionId == "USERS") {
                //     continue;
                // }
                if (permission.canCread && permission.canRead && permission.canDelete && permission.canUpdate) {
                    ability.push(new UserAbilityGql({
                        action: "manage",
                        subject: permission.functionId
                    }));
                } else {
                    if (permission.canRead) {
                        ability.push(new UserAbilityGql({
                            action: "read",
                            subject: permission.functionId
                        }));
                    }
                }
            }
        }

        return ability;
    }

    async userLogin(
        email: string,
        password: string
    ): Promise<UserGql> {
        if (this.isLoginUsingAd) {
            let adConfig = await this.prisma.activeDomainConfig.findFirst({
                where: { isActive: true }
            });

            //#region No AD config found -> login by email
            if (!adConfig) {
                return await this.userLoginByEmail(email, password);
            }
            //#endregion

            const isAuthen = await this.ldapService.userLoginByAd(adConfig, email, password);
            if (isAuthen) {
                let user = await this.prisma.user.findFirst({
                    where: {
                        adName: email,
                        activeStatus: ActiveStatus.ACTIVE
                    },
                    include: {
                        Department: true,
                        Company: true,
                        FaceImages: true,
                    }
                });
                if (!user) {
                    let users = await this.ldapService.createUserFromSap(adConfig, [email]);
                    if (users.length == 1) {
                        user = await this.prisma.user.findFirst({
                            where: {
                                id: users[0].id
                            },
                            include: {
                                Department: true,
                                Company: true,
                                FaceImages: true,
                            }
                        });
                        this.shiftUserService.importShiftFromSap(adConfig, user.id, user.integrationKey);
                    }
                }

                if (!user) {
                    throw new BadRequestException("Wrong username or password");
                } else {
                    const payload = { userId: user.id, username: user.name };
                    let encodedToken = await this.jwtService.signAsync(payload);
                    let refreshToken = await this.jwtService.signAsync(payload, {
                        expiresIn: jwtConstants.refresh_expiresIn,
                        secret: jwtConstants.refresh_secret
                    })

                    let faceImagesBaseArrayBase64 = user.FaceImages.map((el) => {
                        // let data = base64.encodeImage(join(this.faceImagePath, el.path));
                        return {
                            ...el,
                            data: ""
                        }
                    })

                    // return this.userTypeFromUser(user, encodedToken);
                    let userGql = new UserGql({
                        id: user.id,
                        email: user.email,
                        fullName: user.name,
                        avatar: user.avatar,
                        avatarBase64: {
                            path: user.avatar,
                            data: ""
                            // data: base64.encodeImage(join(this.avatarPath, user.avatar))
                        },
                        gender: user.gender,
                        birthDay: user.birthday,
                        phoneNumber: user.phone,
                        status: user.activeStatus,
                        Company: user.Company,
                        Department: user.Department,
                        dateCreated: user.dateCreated,
                        dateModified: user.dateModified,
                        accessToken: encodedToken?.replace(/\"/g, ''),
                        refreshToken: refreshToken?.replace(/\"/g, ''),
                        roles: [(new RoleGql({
                            id: user.roleId,
                            name: user.name,
                            description: user.name
                        }))],
                        faceImagesArrayBase64: faceImagesBaseArrayBase64,
                        role: user.roleId == RoleType.SUPER_ADMIN ? "admin" : "client",
                        ability: await this.getUserAbility(user.roleId)
                    });
                    return userGql;
                }

            } else {
                return await this.userLoginByEmail(email, password);
            }
        }
        else {  //Login by email, not using LDAP
            if (this.isLoginUsingSSO) {
                let checkSso = await this.orgImportService.checkUserLogin(email, password);
                if (checkSso) {
                    let ssoUser = await this.prisma.user.findFirst({
                        where: {
                            activeStatus: ActiveStatus.ACTIVE,
                            OR: [
                                { email: email },
                                { email: `${email}@cmctelecom.vn` },
                            ]
                        },
                        include: {
                            Department: true,
                            Company: true,
                            FaceImages: true,
                        }
                    });
                    if (!ssoUser) {
                        console.error(email);
                        throw new BadRequestException("Email chưa được đăng ký");
                    }

                    const payload = { userId: ssoUser.id, username: ssoUser.name };
                    let encodedToken = await this.jwtService.signAsync(payload);
                    let refreshToken = await this.jwtService.signAsync(payload, {
                        expiresIn: jwtConstants.refresh_expiresIn,
                        secret: jwtConstants.refresh_secret
                    })

                    let faceImagesBaseArrayBase64 = ssoUser.FaceImages.map((el) => {
                        // let data = base64.encodeImage(join(this.faceImagePath, el.path));
                        return {
                            ...el,
                            // data: data
                            data: ""
                        }
                    })

                    // return this.userTypeFromUser(user, encodedToken);
                    let userGql = new UserGql({
                        id: ssoUser.id,
                        email: ssoUser.email,
                        fullName: ssoUser.name,
                        avatar: ssoUser.avatar,
                        avatarBase64: {
                            path: ssoUser.avatar,
                            data: ""
                            // data: base64.encodeImage(join(this.avatarPath, ssoUser.avatar))
                        },
                        gender: ssoUser.gender,
                        birthDay: ssoUser.birthday,
                        phoneNumber: ssoUser.phone,
                        status: ssoUser.activeStatus,
                        Company: ssoUser.Company,
                        Department: ssoUser.Department,
                        dateCreated: ssoUser.dateCreated,
                        dateModified: ssoUser.dateModified,
                        accessToken: encodedToken?.replace(/\"/g, ''),
                        refreshToken: refreshToken?.replace(/\"/g, ''),
                        roles: [(new RoleGql({
                            id: ssoUser.roleId,
                            name: ssoUser.name,
                            description: ssoUser.name
                        }))],
                        faceImagesArrayBase64: faceImagesBaseArrayBase64,
                        role: ssoUser.roleId == RoleType.SUPER_ADMIN ? "admin" : "client",
                        ability: await this.getUserAbility(ssoUser.roleId)
                    });
                    return userGql;
                } else {
                    return await this.userLoginByEmail(email, password);
                }
            } else {
                return await this.userLoginByEmail(email, password);
            }
        }
    }

    async refreshToken(
        refreshToken: string
    ) {
        try {
            const refresh_payload = await this.jwtService.verifyAsync(refreshToken, {
                secret: jwtConstants.refresh_secret
            })

            let user = await this.prisma.user.findUnique({
                where: {
                    id: refresh_payload.userId
                }, include: {
                    Company: true,
                    Department: true,
                }
            })
            if (!user) throw new UnauthorizedException('Invalid token')

            const payload = { userId: user.id, username: user.name };

            let encodedToken = await this.jwtService.signAsync(payload);

            return new UserGql({
                id: user.id,
                email: user.email,
                fullName: user.name,
                avatar: user.avatar,
                avatarBase64: {
                    path: user.avatar,
                    data: ""
                    // data: base64.encodeImage(join(this.avatarPath, user.avatar))
                },
                gender: user.gender,
                birthDay: user.birthday,
                phoneNumber: user.phone,
                status: user.activeStatus,
                Company: user.Company,
                Department: user.Department,
                dateCreated: user.dateCreated,
                dateModified: user.dateModified,
                accessToken: encodedToken?.replace(/\"/g, ''),
                refreshToken: refreshToken,
                roles: [(new RoleGql({
                    id: user.roleId,
                    name: user.name,
                    description: user.name
                }))],
                // faceImagesArrayBase64: faceImagesBaseArrayBase64,
                role: user.roleId == RoleType.SUPER_ADMIN ? "admin" : "client",
                ability: await this.getUserAbility(user.roleId)
                // ...user,
                // accessToken: encodedToken?.replace(/\"/g, ''),
                // refreshToken: refreshToken,
            })
        } catch (e) {
            throw new UnauthorizedException('Invalid token')

        }
    }

    async userLoginByEmail(
        email: string,
        password: string
    ) {
        let user = await this.prisma.user.findFirst({
            where: {
                activeStatus: ActiveStatus.ACTIVE,
                OR: [
                    { email: email },
                    { integrationKey: email },
                    { adName: email }
                ]
            },
            include: {
                Department: true,
                Company: true,
                FaceImages: true,
                UserLoginFailed: true,
            }
        });
        if (!user) throw new BadRequestException(`Wrong username or password`);
        if (!user.isLoginAvaiable) throw new BadRequestException("Wrong ussername or password");

        await this.checkLoginTimeout(user);
        let passValid = await bcrypt.compare(password, user.password);
        await this.onUserLoginFailed(user, passValid);

        var token = StringUtils.generateRandomString();
        await this.redisService.setUserToken(user.id, token);
        this.prisma.userLoginFailed.deleteMany({
            where: {
                userId: user.id
            }
        });
        // let encodedToken = base64.encode(`${user.id}:${token}`);
        const payload = { userId: user.id, username: user.name };
        let encodedToken = await this.jwtService.signAsync(payload);
        let refreshToken = await this.jwtService.signAsync(payload, {
            expiresIn: jwtConstants.refresh_expiresIn,
            secret: jwtConstants.refresh_secret
        })

        //faceImage
        let faceImagesBaseArrayBase64 = await Promise.all(user.FaceImages.map(async (el) => {
            let data = base64Util.encodeImage(join(this.faceImagePath, el.path));
            return {
                ...el,
                // data: data
                data: ""
            }
        }))
        // return this.userTypeFromUser(user, encodedToken);
        let userGql = new UserGql({
            id: user.id,
            email: user.email,
            fullName: user.name,
            avatar: user.avatar,
            avatarBase64: {
                path: user.avatar,
                data: ""
                // data: base64.encodeImage(join(this.avatarPath, user.avatar))
            },
            gender: user.gender,
            birthDay: user.birthday,
            phoneNumber: user.phone,
            status: user.activeStatus,
            Company: user.Company,
            Department: user.Department,
            dateCreated: user.dateCreated,
            dateModified: user.dateModified,
            accessToken: encodedToken?.replace(/\"/g, ''),
            refreshToken: refreshToken?.replace(/\"/g, ''),
            roles: [(new RoleGql({
                id: user.roleId,
                name: user.name,
                description: user.name
            }))],
            faceImagesArrayBase64: faceImagesBaseArrayBase64,
            role: user.roleId == RoleType.SUPER_ADMIN ? "admin" : "client",
            ability: await this.getUserAbility(user.roleId)
        });

        return userGql;
    }

    async userLoginByIam(
        email: string
    ) {
        let user = await this.prisma.user.findFirst({
            where: {
                activeStatus: ActiveStatus.ACTIVE,
                OR: [
                    { email: email },
                    { integrationKey: email },
                    { adName: email }
                ]
            },
            include: {
                Department: true,
                Company: true,
                FaceImages: true,
                UserLoginFailed: true,
            }
        });
        if (!user) throw new BadRequestException(`${email} not found in system`);
        if (!user.isLoginAvaiable) throw new BadRequestException("Wrong ussername");

        var token = StringUtils.generateRandomString();
        await this.redisService.setUserToken(user.id, token);
        this.prisma.userLoginFailed.deleteMany({
            where: {
                userId: user.id
            }
        });
        // let encodedToken = base64.encode(`${user.id}:${token}`);
        const payload = { userId: user.id, username: user.name };
        let encodedToken = await this.jwtService.signAsync(payload);
        let refreshToken = await this.jwtService.signAsync(payload, {
            expiresIn: jwtConstants.refresh_expiresIn,
            secret: jwtConstants.refresh_secret
        })

        //faceImage
        let faceImagesBaseArrayBase64 = await Promise.all(user.FaceImages.map(async (el) => {
            let data = base64Util.encodeImage(join(this.faceImagePath, el.path));
            return {
                ...el,
                // data: data
                data: ""
            }
        }))
        // return this.userTypeFromUser(user, encodedToken);
        let userGql = new UserGql({
            id: user.id,
            email: user.email,
            fullName: user.name,
            avatar: user.avatar,
            avatarBase64: {
                path: user.avatar,
                data: ""
                // data: base64.encodeImage(join(this.avatarPath, user.avatar))
            },
            gender: user.gender,
            birthDay: user.birthday,
            phoneNumber: user.phone,
            status: user.activeStatus,
            Company: user.Company,
            Department: user.Department,
            dateCreated: user.dateCreated,
            dateModified: user.dateModified,
            accessToken: encodedToken?.replace(/\"/g, ''),
            refreshToken: refreshToken?.replace(/\"/g, ''),
            roles: [(new RoleGql({
                id: user.roleId,
                name: user.name,
                description: user.name
            }))],
            faceImagesArrayBase64: faceImagesBaseArrayBase64,
            role: user.roleId == RoleType.SUPER_ADMIN ? "admin" : "client",
            ability: await this.getUserAbility(user.roleId)
        });

        return userGql;
    }

    async checkLoginTimeout(user: User): Promise<void> {
        let checkFailed = await this.prisma.userLoginFailed.findFirst({
            where: { userId: user.id }
        });
        if (!checkFailed) {

        } else {
            if (checkFailed.failedCounter > 5) {

                // tính thời gian chờ: Sau 5 lần sai, cứ sau 1 lần sai sẽ tăng thêm thời gian chờ là 1 p
                var wattingTime = Math.floor(checkFailed.failedCounter / 5) * 60//(s)

                let secLeft = Math.ceil(wattingTime - dateUtil.getSecDiff(new Date(), checkFailed.dateModified));

                if (secLeft > 0) { //wait time out
                    throw new UnauthorizedException(`Login locked for ${secLeft} sec(s)`);
                } else {    //continue try login
                }
            }

        }
    }

    async onUserLoginFailed(user: User, passValid: boolean) {
        if (passValid) {
            await this.prisma.userLoginFailed.deleteMany({
                where: { userId: user.id }
            });
        } else {
            let checkFailed = await this.prisma.userLoginFailed.findFirst({
                where: { userId: user.id }
            });
            if (!checkFailed) {
                checkFailed = await this.prisma.userLoginFailed.create({
                    data: {
                        userId: user.id,
                        failedCounter: 1,
                    }
                });
            } else {
                await this.prisma.userLoginFailed.update({
                    where: { userId: user.id },
                    data: { failedCounter: checkFailed.failedCounter + 1, }
                });
            }
            throw new BadRequestException(`Wrong username or password`);
        }
    }

    async resetPassword(
        ownerId: string,
        currentUserRole: string,
        currentCompanyId: string,
        userId: string
    ): Promise<string> {
        switch (currentUserRole) {
            case RoleType.SUPER_ADMIN:
                break;
            case RoleType.ADMIN:
                let checkUser = await this.prisma.user.findFirst({
                    where: {
                        id: userId,
                        companyId: currentCompanyId
                    }, select: { id: true }
                });

                if (!checkUser)
                    throw new ForbiddenException();
                break;
            default:
                throw new ForbiddenException();
                break;
        }

        this.systemLog.writeLog(userId, "USERS", SqlAction.Update, "User",
            `Reset user password`, "", "");
        var user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: { id: true }
        });
        const password = StringUtils.generatePassword();
        user = await this.prisma.user.update({
            where: {
                id: userId
            }, data: {
                password: password,
                isLoginAvaiable: true
            }
        });

        await this.prisma.userLoginFailed.deleteMany({
            where: { userId: userId }
        });
        if (!user) throw new InternalServerErrorException();
        if (user) return password;
    }

    async changePassword(
        userId: string,
        oldPassword: string,
        newPassword: string,
    ): Promise<boolean> {
        this.systemLog.writeLog(userId, "USERS", SqlAction.Update, "User",
            `Change user password`, "", "");
        const passRex = new RegExp(`(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^A-Za-z0-9])(?=.{8,})`)
        let passValid = newPassword.match(passRex);
        if (passValid == null) throw new BadRequestException("Password too weak");
        var user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: { password: true }
        });
        var isPasswordValid = await bcrypt.compare(oldPassword, user.password);
        if (!isPasswordValid)
            throw new BadRequestException('Old password invalid');

        return this.updatePassword(userId, newPassword);
    }

    private async updatePassword(
        userId: string,
        newPassword: string
    ): Promise<boolean> {
        let user = await this.prisma.user.update({
            where: {
                id: userId
            }, data: {
                password: newPassword
            }
        });
        if (user) return true;
        else return false;
    }

    async lockUser(
        currentUserId: string,
        userId: string,
        lock: boolean,
    ): Promise<boolean> {
        var user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: {
                id: true,
                activeStatus: true,
                name: true,
                email: true,
            }
        });

        if (!user) return false;

        if (!lock) {
            const getAllFaceImage = await this.prisma.userFaceImage.findMany({
                where: { userId: userId }
            });

            if (getAllFaceImage?.length > 0) {
                let files = {};
                for (let i = 0; i < getAllFaceImage.length; i++) {
                    const faceImage = getAllFaceImage[i];
                    let buffer = fs.readFileSync(join(this.faceImagePath, faceImage.path));
                    if (buffer.length > 0) {
                        let key = faceImage.faceImageType.charAt(0).toLowerCase() + faceImage.faceImageType.substring(1);
                        files[key] = [{ buffer: buffer }];
                    }
                };

                let checkFaceQuality = await this.faceImageQualityService.checkFaceFileQuality(
                    userId,
                    new FaceQualityCheckRequest({ userId: userId }),
                    files
                );

                let errors = checkFaceQuality.filter(item => item.error?.length > 0 || item.errorCode?.length > 0);
                if (errors?.length > 0) {
                    throw new BadRequestException(errors);
                };
            }
        }

        this.systemLog.writeLog(currentUserId, "USERS", SqlAction.Delete, "User",
            lock ? `Lock user` : `Unlock user`, "", JSON.stringify({
                email: user.email,
                userName: user.name,
            }));

        user = await this.prisma.user.update({
            where: {
                id: userId
            }, data: {
                activeStatus: lock ? ActiveStatus.DEACTIVE : ActiveStatus.ACTIVE
            }, select: {
                id: true, activeStatus: true, name: true, email: true
            }
        });
        await this.redisService.setUserToken(user.id, "");
        this.userGateway.sendUserUpdateRequest();
        await this.prisma.deviceUser.updateMany({
            where: { userId: userId },
            data: { status: ActiveStatus.DEACTIVE }
        });
        await this.prisma.acUserTime.updateMany({
            where: { userId: userId },
            data: { status: ActiveStatus.DEACTIVE }
        });
        return user.activeStatus == ActiveStatus.ACTIVE ? false : true
    }
    //#endregion

    //#region User CREATE
    userGqlFromUser(user: User): UserGql {
        return new UserGql({
            id: user.id,
            email: user.email,
            fullName: user.name,
            avatar: user.avatar,
            gender: user.gender,
            birthDay: user.birthday,
            phoneNumber: user.phone,
            companyId: user.companyId,
            departmentId: user.departmentId,
            integrationKey: user.integrationKey,
            status: user.activeStatus,
            dateCreated: user.dateCreated,
            dateModified: user.dateModified,
            adName: user.adName,
            title: user.title,
            isFaceReScanEnable: user.isFaceReScanEnable,
            isRemoteCheckEnable: user.isRemoteCheckEnable,
            MaskConfThreshold: user.MaskConfThreshold,
            NoMaskConfThreshold: user.NoMaskConfThreshold,
        });
    }

    userSearchModelFromUser(user: User): UserSearchModel {
        return new UserSearchModel({
            id: user.id,
            email: user.email,
            fullName: user.name,
            avatar: user.avatar,
            gender: user.gender,
            birthDay: user.birthday,
            phoneNumber: user.phone,
            companyId: user.companyId,
            departmentId: user.departmentId,
            integrationKey: user.integrationKey,
            status: user.activeStatus,
            dateCreated: user.dateCreated,
            dateModified: user.dateModified,
            patientId: user.patientId ? user.patientId : "",
        });
    }

    userGqlFromPrisma(user: (User & { Company: Company, Department: Department, FaceImages: UserFaceImage[] })): UserGql {
        return new UserGql({
            id: user.id,
            email: user.email,
            fullName: user.name,
            adName: user.adName,
            title: user.title,
            avatar: user.avatar,
            avatarBase64: {
                path: user.avatar,
                // data: base64.encodeImage(join(this.avatarPath, user.avatar))
                data: ""
            },
            gender: user.gender,
            birthDay: user.birthday,
            phoneNumber: user.phone,
            companyId: user.companyId,
            departmentId: user.departmentId,
            integrationKey: user.integrationKey,
            status: user.activeStatus,
            dateCreated: user.dateCreated,
            dateModified: user.dateModified,
            company: new CompanyGql({
                id: user.Company.id,
                name: user.Company.name
            }),
            department: new DepartmentGql({
                id: user.Department.id,
                name: user.Department.name,
                parentId: user.Department.parentId
            }),
            faceImages: user.FaceImages.map(faceImage => new FaceImageGraphQL({
                faceImageType: faceImage.faceImageType,
                imagePath: faceImage.path
            })),
            isFaceReScanEnable: user.isFaceReScanEnable,
            isRemoteCheckEnable: user.isRemoteCheckEnable,
            idCardJson: user.idCardJson,
            idCardNumber: user.idCardNumber,
        });
    }

    async createNewUser(
        profile: Prisma.UserCreateInput
    ): Promise<User> {
        if (!profile.avatar) {
            const fileName = await this.faceImageHandler.writeAvatarImageFile();
            profile.avatar = fileName;
        }
        if (!profile.birthday) {
            profile.birthday = new Date();
        }

        let newUser = await this.prisma.user.create({
            data: profile
        });
        this.userGateway.sendUserUpdateRequest();
        return newUser;
    }

    async upsertProfile(
        ownerId: string,
        ownerRole: string,
        profile: ProfileRequest,
        avatarFile: any,
    ): Promise<string> {
        let userByEmail = await this.prisma.user.findFirst({
            where: { email: profile.email }
        });

        if (userByEmail && userByEmail.id != profile.userId) {
            throw new BadRequestException('Email existed');
        }

        if (this.isldapEnable) {
            let checkUserLdap = await this.ldapService.searchLdapEmail(profile.email);
            if (!checkUserLdap) throw new BadRequestException("LDAP User not existed");
        }

        //#region Avartar upsert
        const isCreate = !profile.userId;
        if (avatarFile) {
            if (!isCreate) {
                var user = await this.prisma.user.findFirst({
                    where: { id: profile.userId },
                    select: {
                        id: true,
                        avatar: true
                    }
                })
                if (user) {
                    await deleteFileAsync(join(this.avatarPath, user.avatar));
                }
            }
            var avatarFileName = await this.faceImageHandler.writeAvatarImageFile(avatarFile[0].buffer);
            profile.avatar = avatarFileName;
        }
        else if (isCreate) {
            if (ownerRole != RoleType.SUPER_ADMIN &&
                ownerRole != RoleType.ADMIN &&
                ownerRole != RoleType.ORG_ADMIN &&
                ownerRole != RoleType.I_PARKING_ADMIN
            ) throw new ForbiddenException();

            var avatarFileName = await this.faceImageHandler.writeAvatarImageFile();
            profile.avatar = avatarFileName;
        }
        //#endregion

        if (isCreate) {//Create new user
            //Validate role
            if (ownerRole != RoleType.SUPER_ADMIN &&
                ownerRole != RoleType.ADMIN &&
                ownerRole != RoleType.ORG_ADMIN &&
                ownerRole != RoleType.I_PARKING_ADMIN
            ) throw new ForbiddenException();

            //Validate integrationKey
            if (profile.employeeKey) {
                let checkKeyUser = await this.prisma.user.findFirst({
                    where: { integrationKey: profile.employeeKey },
                    select: { id: true }
                });
                if (checkKeyUser) {
                    this.logger.error(checkKeyUser);
                    throw new BadRequestException(`${ErrorEnum['User key exited']}_${StringUtils.getErrKeyName(ErrorEnum['User key exited'])}`);
                }
            }

            //Validate adName
            if (profile.adName) {
                let checkAdUser = await this.prisma.user.findFirst({
                    where: { adName: profile.adName },
                    select: { id: true }
                });
                if (checkAdUser) {
                    this.logger.error(checkAdUser);
                    throw new BadRequestException(`${ErrorEnum['AD user exited']}_${StringUtils.getErrKeyName(ErrorEnum['AD user exited'])}`);
                }
            }


            let newUser = await this.createNewUser({
                email: profile.email,
                name: profile.fullName,
                adName: profile.adName,
                title: profile.title,
                password: profile.password ? profile.password : StringUtils.generatePassword(),
                birthday: profile.birthDay,
                integrationKey: profile.employeeKey,
                avatar: profile.avatar,
                phone: profile.phoneNumber,
                gender: profile.gender,
                Department: {
                    connect: {
                        id: profile.departmentId
                    }
                },
                Company: {
                    connect: {
                        id: profile.companyId
                    }
                },
                activeStatus: ActiveStatus.ACTIVE,
                isFaceReScanEnable: profile.isFaceReScanEnable ? profile.isFaceReScanEnable : false,
                isRemoteCheckEnable: profile.isRemoteCheckEnable ? profile.isRemoteCheckEnable : false,
                idCardNumber: profile.idCardNumber,
                idCardJson: profile.idCardJson,
                idCardFaceSimilarity: profile.idCardFaceSimilarity,
            });

            this.userGeneralIntegrationService.onUserUpsertEvent(newUser.id);
            return newUser.id;
        } else {//Update user
            //Validate integrationKey
            if (profile.employeeKey) {
                let checkKeyUser = await this.prisma.user.findFirst({
                    where: {
                        id: { not: profile.userId },
                        integrationKey: profile.employeeKey
                    },
                    select: { id: true }
                });
                if (checkKeyUser) {
                    this.logger.error(checkKeyUser);
                    throw new BadRequestException(`${ErrorEnum['User key exited']}_${StringUtils.getErrKeyName(ErrorEnum['User key exited'])}`);
                }
            }

            //Validate adName
            if (profile.adName) {
                let checkAdUser = await this.prisma.user.findFirst({
                    where: {
                        id: { not: profile.userId },
                        adName: profile.adName
                    },
                    select: { id: true }
                });
                if (checkAdUser) throw new BadRequestException(`${ErrorEnum['AD user exited']}_${StringUtils.getErrKeyName(ErrorEnum['AD user exited'])}`);
            }

            if (profile.idCardNumber) {
                let checkIdCardUser = await this.prisma.user.findFirst({
                    where: {
                        id: { not: profile.userId },
                        idCardNumber: profile.idCardNumber
                    },
                    select: { id: true }
                });
                if (checkIdCardUser) throw new BadRequestException(`${ErrorEnum['AD user exited']}_${StringUtils.getErrKeyName(ErrorEnum['User key exited'])}`);
            }

            let updateData: Prisma.UserUpdateInput = {};

            if (profile.isFaceReScanEnable != null || profile.isFaceReScanEnable != undefined) {
                updateData.isFaceReScanEnable = profile.isFaceReScanEnable
            }

            if (profile.isRemoteCheckEnable != null || profile.isRemoteCheckEnable != undefined) {
                updateData.isRemoteCheckEnable = profile.isRemoteCheckEnable
            }

            if (profile.isLoginEnable !== undefined) {
                updateData.isLoginAvaiable = profile.isLoginEnable ? profile.isLoginEnable : false;
                if (profile.isLoginEnable) {
                    await this.prisma.userLoginFailed.deleteMany({
                        where: { userId: profile.userId }
                    });
                }
            }

            if (profile.email) {
                let curentUser = await this.prisma.user.findFirst({
                    where: { id: profile.userId },
                    select: { email: true }
                });
                if (curentUser?.email != profile.email) {
                    let checkUser = await this.prisma.user.findFirst({
                        where: { email: profile.email }
                    });
                    if (checkUser) {
                        this.logger.error(checkUser);
                        throw new BadRequestException(`Email existed`);
                    }
                    updateData.email = profile.email;
                }
            }
            if (profile.fullName) updateData.name = profile.fullName;
            updateData.adName = profile.adName;
            if (profile.birthDay) updateData.birthday = profile.birthDay;
            if (profile.employeeKey != profile.userId)
                updateData.integrationKey = profile.employeeKey;
            if (profile.avatar) updateData.avatar = profile.avatar;
            if (profile.phoneNumber) updateData.phone = profile.phoneNumber;
            if (profile.gender != null) updateData.gender = profile.gender;

            if (profile.departmentId) updateData.Department = { connect: { id: profile.departmentId } };
            if (profile.companyId) updateData.Company = { connect: { id: profile.companyId } };
            if (profile.title) updateData.title = profile.title;
            if (this.cachedGlobalConfig.defaultGuestDepId == profile.departmentId) updateData.activeStatus = ActiveStatus.ACTIVE;
            if (profile.idCardNumber) { updateData.idCardNumber = profile.idCardNumber }
            if (profile.idCardJson) { updateData.idCardJson = profile.idCardJson }
            if (profile.idCardFaceSimilarity != null) { updateData.idCardFaceSimilarity = profile.idCardFaceSimilarity }
            if (profile.password) { updateData.password = profile.password }
            let updatedUser = await this.prisma.user.update({
                where: {
                    id: profile.userId
                }, data: updateData
            });
            this.userGeneralIntegrationService.onUserUpsertEvent(updatedUser.id);
            return profile.userId;
        }
    }

    async upsertFaceImages(
        ownerId: string,
        ownerRole: string,
        userId: string,
        files,
    ) {
        if (await this.isUserLockFaceUpdate(userId)) throw new BadRequestException("Lock Update Face Images")
        if (files == null) return;
        if (ownerId != userId) {
            if (ownerRole != RoleType.SUPER_ADMIN &&
                ownerRole != RoleType.ADMIN &&
                ownerRole != RoleType.ORG_ADMIN &&
                ownerRole != RoleType.I_PARKING_ADMIN
            ) throw new ForbiddenException();
        }

        //#region Write new images
        let faceImages = await this.faceImageHandler.writeFaceImageFiles(userId, files);
        const currentFaceImages = await this.prisma.userFaceImage.findMany({
            where: {
                userId: userId
            }
        });
        await this.prisma.userFaceImage.createMany({
            data: faceImages
        });
        //#endregion

        // remove old face images
        for (const oldFaceImage of currentFaceImages) {
            if (faceImages.some(e => e.faceImageType == oldFaceImage.faceImageType)) {
                await this.prisma.userFaceImage.delete({
                    where: { id: oldFaceImage.id }
                });
                await deleteFileAsync(join(this.faceImagePath, oldFaceImage.path));
            }
        }

        let user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: {
                FaceImages: {
                    select: {
                        id: true
                    }
                }
            }
        })

        if (user.FaceImages.length == 12) {
            let user = await this.prisma.user.update({
                where: { id: userId },
                data: {
                    isFaceReScanEnable: false
                }
            })
        }
    }

    async upsertUser(
        ownerId: string,
        ownerRole: string,
        req: ProfileRequest,
        files,
        isSaveLog: boolean = true,
    ): Promise<UserGql> {
        if (isSaveLog) {
            this.systemLog.writeLog(ownerId, "USERS", SqlAction.Create, "User",
                `Upsert user`, "", JSON.stringify(req), null, req.userId);
        }
        //Validate email
        const isUserExists = await this.findByEmail(req.email);
        if (isUserExists && req.userId != isUserExists.id) {
            if (req.expiredTime) { } else
                throw new BadRequestException(`${ErrorEnum['Email exited']}_${StringUtils.getErrKeyName(ErrorEnum['Email exited'])}`);
        }

        //Upsert user's profile
        const userId = await this.upsertProfile(ownerId, ownerRole, req, files['avatar'] ? files['avatar'] : (files['center'] ? files['center'] : files[0]));

        //Upload face images
        delete files['avatar']; //Delete tmp file
        await this.upsertFaceImages(ownerId, ownerRole, userId, files);
        let user = await this.prisma.user.findFirst({
            where: { id: userId },
            include: {
                Company: true,
                Department: true,
                FaceImages: true,
            }
        })
        if (user) {
            this.cachedGlobalConfig.cachedAllUser.set(user.id, user);
        }

        if (user && req.licensePlate) {
            let upsertedVehicle = await this.iparkingVehicleService.upsertIparkingVehicle(
                `${user.name}_${req.licensePlate}`,
                req.licensePlate,
                req.vehicleType ?? IParkingVehicleType.Motorbike,
                user.id, new Date('2099-01-01'),
                user.dateCreated,
                req.vehicleType == IParkingVehicleType.Car ? true : false,
                req.vehicleType == IParkingVehicleType.Car ? true : false,
            )
        }

        //Return user
        let userGql = this.userGqlFromUser(user);
        let faceImagesBaseArrayBase64 = user.FaceImages.map((el) => {
            // let data = base64.encodeImage(join(this.faceImagePath, el.path));
            return {
                ...el,
                data: ""
            }
        })
        userGql.company = {
            id: user.Company.id,
            name: user.Company.name,
            sortKey: user.Company.sortKey
        }
        userGql.department = {
            id: user.Department.id,
            name: user.Department.name,
            parentId: user.Department.parentId
        }
        userGql.faceImages = user.FaceImages.map(faceImage => new FaceImageGraphQL({
            faceImageType: faceImage.faceImageType,
            imagePath: faceImage.path
        }));
        userGql.avatarBase64 = {
            path: userGql.avatar,
            data: ""
            // data: base64.encodeImage(join(this.avatarPath, userGql.avatar))
        }
        userGql.faceImagesArrayBase64 = faceImagesBaseArrayBase64;
        return userGql;
    }

    async deviceUploadUser(
        deviceId: string,
        profile: ProfileRequest,
        files,
    ) {
        let checkDevUser = await this.prisma.deviceUser.findFirst({
            where: {
                deviceId: deviceId,
                userId: profile.userId,
                status: ActiveStatus.ACTIVE
            }
        });
        if (!checkDevUser) throw new BadRequestException(StringUtils.generateExceptionString(ErrorEnum['User not found']));
        let userGql = await this.upsertUser(null, RoleType.SUPER_ADMIN, profile, files, false);

        userGql.idCardFaceSimilarity = profile.idCardFaceSimilarity;

        if (profile.idCardFaceSimilarity != null) {
            if (profile.idCardFaceSimilarity < 0.8) {
                await this.prisma.user.update({
                    where: {
                        id: userGql.id
                    }, data: {
                        activeStatus: ActiveStatus.WAITTING_FOR_APPROVE
                    }
                });

                for (const [key, value] of Object.entries(files)) {
                    const fileName = await this.alertMessService.writeAlertCachedImageFile(value[0].buffer);
                    this.alertMessService.createInvalidUserAlert(userGql, fileName);
                    break;
                }

                return userGql;
            } else {
                for (const [key, value] of Object.entries(files)) {
                    const fileName = await this.alertMessService.writeAlertCachedImageFile(value[0].buffer);
                    this.alertMessService.upsertUserAlert(userGql, fileName);
                    break;
                }

            }
        } else {
            for (const [key, value] of Object.entries(files)) {
                const fileName = await this.alertMessService.writeAlertCachedImageFile(value[0].buffer);
                this.alertMessService.upsertUserAlert(userGql, fileName);
                break;
            }
        }

        this.systemLog.writeLog(null, "USERS", SqlAction.Update, "User",
            `Update user`, "", JSON.stringify(userGql), SystemLogLabelEnum.FACE_TERMINAL_USER_UPLOAD, userGql.id);

        return userGql;
    }

    async addLdapUser(
        ownerId: string,
        adNames: string[]
    ): Promise<User[]> {
        let userRole = await this.prisma.user.findFirst({
            where: {
                id: ownerId,
                roleId: RoleType.SUPER_ADMIN
            }
        });
        if (!userRole) throw new ForbiddenException();
        if (!adNames) throw new BadRequestException();
        if (adNames.length == 0) throw new BadRequestException();

        let adConfig = await this.prisma.activeDomainConfig.findFirst({
            where: { isActive: true }
        });
        if (!adConfig) throw new InternalServerErrorException(`${ErrorEnum['LDAP not configurated']}_${StringUtils.getErrKeyName(ErrorEnum['LDAP not configurated'])}`);
        return await this.ldapService.createUserFromSap(adConfig, adNames);
    }

    async ClusterAddLdapUser(
        adName: string
    ) {
        if (!adName) throw new BadRequestException();
        let adConfig = await this.prisma.activeDomainConfig.findFirst({
            where: { isActive: true }
        });
        if (!adConfig) throw new InternalServerErrorException(`${ErrorEnum['LDAP not configurated']}_${StringUtils.getErrKeyName(ErrorEnum['LDAP not configurated'])}`);
        let users = await this.ldapService.createUserFromSap(adConfig, [adName]);
        if (users.length == 1) {
            let user = await this.prisma.user.findFirst({
                where: {
                    id: users[0].id
                },
                include: {
                    Department: true,
                    Company: true,
                    FaceImages: true,
                }
            });
            this.shiftUserService.importShiftFromSap(adConfig, user.id, user.integrationKey);

            if (!user) {
                throw new BadRequestException(`${ErrorEnum['Wrong username or password']}_${StringUtils.getErrKeyName(ErrorEnum['Wrong username or password'])}`);
            } else {
                return null;
            }
        }
    }

    async addUserGraphql(
        ownerId: string,
        ownerRole: string,
        input: UserAddInput
    ): Promise<UserGql> {
        let profile = new ProfileRequest({
            email: input.email.trim(),
            fullName: input.fullName,
            birthDay: input.birthDay,
            phoneNumber: input.phoneNumber,
            gender: input.gender,
            departmentId: input.departmentId,
            companyId: input.companyId,
            employeeKey: input.employeeCode,
            idCardNumber: input.idCardNumber,
        });
        const userId = await this.upsertProfile(ownerId, ownerRole, profile, null);

        return this.userGqlFromUser(await this.prisma.user.findFirst({
            where: { id: userId }
        }));
    }

    async findExactCompanyByName(ownerId: string, name: string): Promise<string> {
        let company = await this.prisma.company.findFirst({
            where: {
                name: name,
                Owner: {
                    some: {
                        userId: ownerId
                    }
                }
            }
        });

        if (!company) return "";
        return company.id;
    }

    async findExactDepByName(name: string, companyId: string): Promise<string> {
        let dep = await this.prisma.department.findFirst({
            where: {
                name: name,
                companyId: companyId
            }
        });
        if (!dep) return "";
        return dep.id;
    }

    async import(
        ownerId: string,
        ownerRole: string,
        req: ProfileRequest,
        files
    ): Promise<UserGql> {
        const isUserExists = await this.prisma.user.findFirst({
            where: {
                email: req.email
            }
        });
        if (isUserExists && req.userId != isUserExists.id)
            throw new BadRequestException(`${ErrorEnum['Email exited']}_${StringUtils.getErrKeyName(ErrorEnum['Email exited'])}`);

        const userId = await this.upsertProfile(ownerId, ownerRole, req, files['avatar']);
        delete files['avatar'];
        await this.upsertFaceImages(ownerId, ownerRole, userId, files);
        return this.userGqlFromUser(await this.prisma.user.findFirst({
            where: { id: userId }
        }));
    }

    //#endregion

    //#region User READ
    async getUserById(
        currentUserId: string,
        currentUserRole: string,
        currentComId: string,
        currentDepId: string,
        userId: string,
    ): Promise<UserGql> {
        if (userId != currentUserId ||
            currentUserRole != RoleType.SUPER_ADMIN
        ) {
            let checkAllowUserIds = await this.userFilterService.getUserIdsByRole(currentUserId, currentUserRole, currentComId, currentDepId);
            if (!checkAllowUserIds.includes(userId)) throw new ForbiddenException();
        }
        let user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                integrationKey: true,
                Company: {
                    select: {
                        id: true,
                        name: true,
                    }
                },
                Department: {
                    select: {
                        id: true,
                        name: true,
                    }
                }, FaceImages: {
                    select: {
                        id: true,
                        path: true,
                        faceImageType: true,
                    }
                }
            }
        });

        if (!user) throw new BadRequestException(StringUtils.generateExceptionString(ErrorEnum['User not found']));

        let gql = new UserGql({
            id: user.id,
            name: user.name,
            email: user.email,
            avatar: user.avatar,
            integrationKey: user.integrationKey,
            Company: new CompanyGql({
                id: user.Company.id,
                name: user.Company.name,
            }),
            Department: new DepartmentGql({
                id: user.Department.id,
                name: user.Department.name,
            }),
            faceImages: []
        });
        for (let index = 0; index < user.FaceImages.length; index++) {
            const faceImage = user.FaceImages[index];
            gql.faceImages.push(new FaceImageGraphQL({
                imagePath: faceImage.path,
                faceImageType: faceImage.faceImageType
            }));
        }
        return gql;
    }

    async checkGetUserProfilePermisstion(
        ownerId: string,
        user: User
    ) {
        if (ownerId != user.id) {
            if (!user) {
                throw new ForbiddenException();
            }

            let ownerCheck = await this.prisma.user.findFirst({
                where: { id: ownerId },
                select: {
                    roleId: true,
                    companyId: true,
                    departmentId: true,
                    OrganizationUser: {
                        select: {
                            organizationId: true
                        }
                    }
                }
            });

            switch (ownerCheck.roleId) {
                case RoleType.SUPER_ADMIN:
                    break;
                case RoleType.ADMIN:
                    if (ownerCheck.companyId != user.companyId) {
                        throw new ForbiddenException();
                    }
                    break;
                case RoleType.HR:
                    break;
                case RoleType.ORG_ADMIN:
                    let checkOrg = await this.prisma.organizationUser.findFirst({
                        where: {
                            organizationId: {
                                in: ownerCheck.OrganizationUser.map(orgUser => orgUser.organizationId)
                            },
                            userId: user.id
                        }
                    });
                    if (!checkOrg) throw new ForbiddenException();
                    break;
                case RoleType.EMPLOYEE:
                    throw new ForbiddenException();
                    break;
                case RoleType.GUEST:
                    throw new ForbiddenException();
                    break;
            }
        }
    }

    async getUserFilterByRole(
        ownerId: string,
    ): Promise<Prisma.UserWhereInput> {
        let owner = await this.prisma.user.findFirst({
            where: { id: ownerId },
            select: {
                roleId: true,
                companyId: true,
                departmentId: true,
            }
        });

        let userFilter: Prisma.UserWhereInput = {}

        switch (owner.roleId) {
            case RoleType.SUPER_ADMIN:
                break;
            case RoleType.ADMIN:
                userFilter.companyId = owner.companyId;
                break;
            case RoleType.DEPARTMENT_ADMIN:
                userFilter.departmentId = owner.departmentId;
                break;
            case RoleType.RECEPTION:
                throw new ForbiddenException();
                break;
            case RoleType.HR:
                break;
            case RoleType.EMPLOYEE:
                throw new ForbiddenException();
                break;
            case RoleType.GUEST:
                throw new ForbiddenException();
                break;
            case RoleType.ATTANDANT_ADMIN:
                throw new ForbiddenException();
                break;
            case RoleType.ORG_ADMIN:
                let orgs = await this.prisma.organizationUser.findMany({
                    where: {
                        userId: ownerId,
                        isMaster: true
                    }
                });
                userFilter.OrganizationUser = { some: { organizationId: { in: orgs.map(org => org.organizationId) } } };
                break;
            default:
                throw new ForbiddenException();
                break;
        }

        return userFilter;
    }

    async findByIdWithImage(
        ownerId: string,
        userId: string
    ): Promise<UserGql> {
        let user = await this.prisma.user.findFirst({
            where: {
                id: userId
            }, include: {
                Company: true,
                Department: true,
                FaceImages: true,
                UserLoginFailed: true
            }
        });
        if (!user) throw new BadRequestException(`${ErrorEnum['User not found']}_${StringUtils.getErrKeyName(ErrorEnum['User not found'])}`);
        await this.checkGetUserProfilePermisstion(ownerId, user);

        //faceImage
        let faceImagesBaseArrayBase64 = await Promise.all(user.FaceImages.map(async (el) => {
            let data = base64Util.encodeImage(join(this.faceImagePath, el.path));
            return {
                ...el,
                data: data
                // data: ""
            }
        }))
        let userGql = this.userGqlFromUser(user);
        userGql.faceImages = user.FaceImages.map(faceImage => new FaceImageGraphQL({
            faceImageType: faceImage.faceImageType,
            imagePath: faceImage.path
        }));
        userGql.company = new CompanyGql({
            id: user.Company.id,
            name: user.Company.name
        })
        userGql.department = new DepartmentGql({
            id: user.Department.id,
            name: user.Department.name,
            parentId: user.Department.parentId
        })
        userGql.avatarBase64 = {
            path: userGql.avatar,
            data: base64Util.encodeImage(join(this.avatarPath, userGql.avatar))
            // data: ""
        }
        userGql.faceImagesArrayBase64 = faceImagesBaseArrayBase64;
        userGql.isLoginEnable = user.UserLoginFailed[0]?.failedCounter < 6 ? true : false;
        userGql.idCardNumber = user.idCardNumber;
        userGql.idCardJson = user.idCardJson;
        return userGql;
    }

    async userByEmail(
        ownerId: string,
        email: string
    ): Promise<UserGql> {
        let user = await this.prisma.user.findFirst({
            where: {
                email: email
            }, include: {
                Company: true,
                Department: true,
                FaceImages: true
            }
        });
        if (!user) throw new BadRequestException(`${ErrorEnum['User not found']}_${StringUtils.getErrKeyName(ErrorEnum['User not found'])}`);
        await this.checkGetUserProfilePermisstion(ownerId, user);

        let userGql = this.userGqlFromUser(user);
        userGql.faceImages = user.FaceImages.map(faceImage => new FaceImageGraphQL({
            faceImageType: faceImage.faceImageType,
            imagePath: faceImage.path
        }));
        userGql.company = new CompanyGql({
            id: user.Company.id,
            name: user.Company.name
        })
        userGql.department = new DepartmentGql({
            id: user.Department.id,
            name: user.Department.name,
            parentId: user.Department.parentId
        })
        return userGql;
    }

    async userById(
        ownerId: string,
        userId: string
    ): Promise<UserGql> {
        let user = await this.prisma.user.findFirst({
            where: {
                id: userId
            }, include: {
                Company: true,
                Department: true,
                FaceImages: true
            }
        });
        if (!user) throw new BadRequestException(`${ErrorEnum['User not found']}_${StringUtils.getErrKeyName(ErrorEnum['User not found'])}`);
        await this.checkGetUserProfilePermisstion(ownerId, user);

        let userGql = this.userGqlFromUser(user);
        userGql.faceImages = user.FaceImages.map(faceImage => new FaceImageGraphQL({
            faceImageType: faceImage.faceImageType,
            imagePath: faceImage.path
        }));
        userGql.company = new CompanyGql({
            id: user.Company.id,
            name: user.Company.name
        })
        userGql.department = new DepartmentGql({
            id: user.Department.id,
            name: user.Department.name,
            parentId: user.Department.parentId
        })
        return userGql;
    }

    async userByIdWithHisInfo(userId: string): Promise<UserSearchModel> {
        let user = await this.prisma.user.findFirst({
            where: {
                id: userId
            }, include: {
                Company: true,
                Department: true,
                FaceImages: true
            }
        });
        if (!user) throw new BadRequestException(`${ErrorEnum['User not found']}_${StringUtils.getErrKeyName(ErrorEnum['User not found'])}`);
        let userSearchModel = this.userSearchModelFromUser(user);
        userSearchModel.avatarBase64 = base64Util.encodeImage(join(this.avatarPath, user.avatar));
        userSearchModel.faceImages = user.FaceImages.map(faceImage => new FaceImageGraphQL({
            faceImageType: faceImage.faceImageType,
            imagePath: faceImage.path
        }));
        userSearchModel.company = new CompanyGql({
            id: user.Company.id,
            name: user.Company.name
        })
        userSearchModel.department = new DepartmentGql({
            id: user.Department.id,
            name: user.Department.name,
            parentId: user.Department.parentId
        });
        let patientHisInfo = await this.patientService.getPatientInfoFromHis(user.patientId);
        userSearchModel.hisProfile = patientHisInfo;
        return userSearchModel;
    }

    async findByEmail(email: string): Promise<User> {
        let user = await this.prisma.user.findFirst({
            where: {
                email: email
            }
        });
        // if (!user) throw new BadRequestException("Email not found")
        return user;
    }

    async search(
        ownerId: string,
        ownerRole: string,
        query: string,
        companyIds: string[],
        departmentIds: string[],
        pageIndex: number,
        pageSize: number,
        status: number,
        startDate: Date = null,
        endDate: Date = null,
        faceImageFilter: boolean = null,
    ): Promise<UserGql[]> {
        //#region Get active status from number
        let statusFilter: ActiveStatus[] = [ActiveStatus.ACTIVE, ActiveStatus.DEACTIVE, ActiveStatus.WAITTING_FOR_APPROVE]
        if (status == NumberStatus.Active) {
            statusFilter = [ActiveStatus.ACTIVE]
        } else {
            if (status == NumberStatus.InActive) {
                statusFilter = [ActiveStatus.DEACTIVE]
            }
        }
        //#endregion

        //#region Get filter deps
        let depsFilter: string[] = departmentIds;
        if (departmentIds.length > 0) {
            let deps = await this.prisma.department.findMany({
                where: {
                    id: { in: departmentIds }
                }
            });
            depsFilter = deps.map(dep => dep.id);
        } else {
            if (companyIds.length > 0) {
                let deps = await this.prisma.department.findMany({
                    where: {
                        companyId: {
                            in: companyIds
                        }
                    }
                });
                depsFilter = deps.map(dep => dep.id);
            }
        }
        //#endregion

        //#region Query Users
        switch (ownerRole) {
            case RoleType.SUPER_ADMIN:
                return await this.webSearchSuperAdmin(ownerId, query, depsFilter, pageIndex, pageSize, statusFilter, startDate, endDate, faceImageFilter);
                break;
            case RoleType.I_PARKING_ADMIN:
                return await this.webSearchSuperAdmin(ownerId, query, depsFilter, pageIndex, pageSize, statusFilter, startDate, endDate, faceImageFilter);
                break;
            case RoleType.ADMIN:
                return await this.webSearchAdmin(ownerId, query, depsFilter, pageIndex, pageSize, statusFilter, startDate, endDate, faceImageFilter)
                break;
            case RoleType.HR:
                return await this.webSearchHr(ownerId, query, depsFilter, pageIndex, pageSize, statusFilter, startDate, endDate, faceImageFilter)
                break;
            case RoleType.ORG_ADMIN:
                return await this.webSearchOrgAdmin(ownerId, query, depsFilter, pageIndex, pageSize, statusFilter, startDate, endDate, faceImageFilter)
                break;
            case RoleType.EMPLOYEE:
                return [];
                break;
            case RoleType.GUEST:
                return [];
                break;
        }
        //#endregion
        return [];
    }

    async webSearchSuperAdmin(
        ownerId: string,
        query: string,
        departmentIds: string[],
        page: number,
        perPage: number,
        status: ActiveStatus[],
        startDate: Date = null,
        endDate: Date = null,
        faceImageFilter: boolean = null,
    ): Promise<UserGql[]> {
        let filter: Prisma.UserWhereInput = {
            // id: { not: ownerId },
            activeStatus: {
                in: status
            }
            , AND: {
                OR: [
                    { name: { contains: query, mode: 'insensitive' } },
                    { email: { contains: query, mode: 'insensitive' } },
                    { id: { contains: query, mode: 'insensitive' } },
                    { integrationKey: { contains: query, mode: 'insensitive' } },
                    { idCardNumber: { contains: query, mode: 'insensitive' } },
                ]
            }
        }

        if (faceImageFilter != null) {
            if (faceImageFilter) {
                filter.FaceImages = { some: {} }
            } else {
                filter.FaceImages = { none: {} }
            }
        }

        if (departmentIds.length > 0) {
            filter.departmentId = { in: departmentIds }
        }

        if (startDate != null && endDate != null) {
            filter.dateModified = {
                gte: startDate,
                lte: endDate
            }
        } else {
            if (startDate != null) {
                filter.dateModified = {
                    gte: startDate,
                }
            }

            if (endDate != null) {
                filter.dateModified = {
                    lte: endDate,
                }
            }
        }

        let users = await this.prisma.user.findMany({
            where: filter,
            include: {
                Company: true,
                Department: true,
                FaceImages: true,
                OrganizationUser: {
                    select: {
                        Organiztion: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                }
            },
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            orderBy: [
                { name: 'asc' },
                { adName: 'asc' },
                { integrationKey: 'asc' }],
        });

        let count = await this.prisma.user.count({
            where: filter,
        });
        return await Promise.all(users.map(async (user) => {
            let userGql = this.userGqlFromPrisma(user);
            userGql.count = count;
            let faceImagesBaseArrayBase64 = user.FaceImages.map((el) => {
                // let data = base64.encodeImage(join(this.faceImagePath, el.path));
                return {
                    ...el,
                    data: ""
                }
            })
            userGql.faceImagesArrayBase64 = faceImagesBaseArrayBase64;
            userGql.Organizations = [];
            for (let index = 0; index < user.OrganizationUser.length; index++) {
                const orgUser = user.OrganizationUser[index];
                let orgGql = new OrganizationGQL({ ...orgUser.Organiztion });
                userGql.Organizations.push(orgGql);
            }
            return userGql;
        }))
    }

    async webSearchAdmin(
        ownerId: string,
        query: string,
        departmentIds: string[],
        page: number,
        perPage: number,
        status: ActiveStatus[],
        startDate: Date = null,
        endDate: Date = null,
        faceImageFilter: boolean = null,
    ): Promise<UserGql[]> {
        let filter: Prisma.UserWhereInput = {
            // id: { not: ownerId },
            Company: {
                Owner: {
                    some: {
                        userId: ownerId
                    }
                }
            },
            activeStatus: {
                in: status
            },
            AND: {
                OR: [
                    { name: { contains: query, mode: 'insensitive' } },
                    { email: { contains: query, mode: 'insensitive' } },
                    { id: { contains: query, mode: 'insensitive' } },
                    { integrationKey: { contains: query, mode: 'insensitive' } },
                    { idCardNumber: { contains: query, mode: 'insensitive' } },
                ]
            }
        }

        if (departmentIds.length > 0) {
            filter.departmentId = { in: departmentIds }
        }

        if (startDate != null && endDate != null) {
            filter.dateModified = {
                gte: startDate,
                lte: endDate
            }
        } else {
            if (startDate != null) {
                filter.dateModified = {
                    gte: startDate,
                }
            }

            if (endDate != null) {
                filter.dateModified = {
                    lte: endDate,
                }
            }
        }

        if (faceImageFilter != null) {
            if (faceImageFilter) {
                filter.FaceImages = { some: {} }
            } else {
                filter.FaceImages = { none: {} }
            }
        }

        let users = await this.prisma.user.findMany({
            where: filter,
            include: {
                Company: true,
                Department: true,
                FaceImages: true,
                OrganizationUser: {
                    select: {
                        Organiztion: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                }
            },
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            orderBy: [
                { name: 'asc' },
                { adName: 'asc' },
                { integrationKey: 'asc' }],
        });

        let count = await this.prisma.user.count({
            where: filter,
        });

        return await Promise.all(users.map(async (user) => {
            let userGql = this.userGqlFromPrisma(user);
            userGql.count = count;
            let faceImagesBaseArrayBase64 = user.FaceImages.map((el) => {
                // let data = base64.encodeImage(join(this.faceImagePath, el.path));
                return {
                    ...el,
                    data: ""
                }
            })
            userGql.faceImagesArrayBase64 = faceImagesBaseArrayBase64;
            userGql.Organizations = [];
            for (let index = 0; index < user.OrganizationUser.length; index++) {
                const orgUser = user.OrganizationUser[index];
                let orgGql = new OrganizationGQL({ ...orgUser.Organiztion });
                userGql.Organizations.push(orgGql);
            }
            return userGql;
        }))
    }

    async webSearchHr(
        ownerId: string,
        query: string,
        departmentIds: string[],
        page: number,
        perPage: number,
        status: ActiveStatus[],
        startDate: Date = null,
        endDate: Date = null,
        faceImageFilter: boolean = null,
    ): Promise<UserGql[]> {
        let filter: Prisma.UserWhereInput = {
            activeStatus: {
                in: status
            },
            AND: {
                OR: [
                    { name: { contains: query, mode: 'insensitive' } },
                    { email: { contains: query, mode: 'insensitive' } },
                    { id: { contains: query, mode: 'insensitive' } },
                    { integrationKey: { contains: query, mode: 'insensitive' } },
                    { idCardNumber: { contains: query, mode: 'insensitive' } },
                ]
            }
        }

        if (departmentIds.length > 0) {
            filter.departmentId = { in: departmentIds }
        }

        if (startDate != null && endDate != null) {
            filter.dateModified = {
                gte: startDate,
                lte: endDate
            }
        } else {
            if (startDate != null) {
                filter.dateModified = {
                    gte: startDate,
                }
            }

            if (endDate != null) {
                filter.dateModified = {
                    lte: endDate,
                }
            }
        }

        if (faceImageFilter != null) {
            if (faceImageFilter) {
                filter.FaceImages = { some: {} };
            } else {
                filter.FaceImages = { none: {} };
            }
        }

        let users = await this.prisma.user.findMany({
            where: filter,
            include: {
                Company: true,
                Department: true,
                FaceImages: true,
                OrganizationUser: {
                    select: {
                        Organiztion: {
                            select: {
                                id: true,
                                name: true
                            }
                        }
                    }
                }
            },
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            orderBy: [
                { name: 'asc' },
                { adName: 'asc' },
                { integrationKey: 'asc' }],
        });

        let count = await this.prisma.user.count({
            where: filter,
        });

        return await Promise.all(users.map(async (user) => {
            let userGql = this.userGqlFromPrisma(user);
            userGql.count = count;
            let faceImagesBaseArrayBase64 = user.FaceImages.map((el) => {
                // let data = base64.encodeImage(join(this.faceImagePath, el.path));
                return {
                    ...el,
                    data: ""
                }
            })
            userGql.faceImagesArrayBase64 = faceImagesBaseArrayBase64;
            userGql.Organizations = [];
            for (let index = 0; index < user.OrganizationUser.length; index++) {
                const orgUser = user.OrganizationUser[index];
                let orgGql = new OrganizationGQL({ ...orgUser.Organiztion });
                userGql.Organizations.push(orgGql);
            }
            return userGql;
        }))
    }

    async webSearchOrgAdmin(
        ownerId: string,
        query: string,
        departmentIds: string[],
        page: number,
        perPage: number,
        status: ActiveStatus[],
        startDate: Date = null,
        endDate: Date = null,
        faceImageFilter: boolean = null,
    ): Promise<UserGql[]> {
        let filter: Prisma.UserWhereInput = {
            activeStatus: {
                in: status
            },
            AND: {
                OR: [
                    { name: { contains: query, mode: 'insensitive' } },
                    { email: { contains: query, mode: 'insensitive' } },
                    { id: { contains: query, mode: 'insensitive' } },
                    { integrationKey: { contains: query, mode: 'insensitive' } },
                    { idCardNumber: { contains: query, mode: 'insensitive' } },
                ]
            }
        }

        if (startDate != null && endDate != null) {
            filter.dateModified = {
                gte: startDate,
                lte: endDate
            }
        } else {
            if (startDate != null) {
                filter.dateModified = {
                    gte: startDate,
                }
            }

            if (endDate != null) {
                filter.dateModified = {
                    lte: endDate,
                }
            }
        }

        if (faceImageFilter != null) {
            if (faceImageFilter) {
                filter.FaceImages = { some: {} };
            } else {
                filter.FaceImages = { none: {} };
            }
        }

        let checkOrg = await this.prisma.organizationUser.findMany({
            where: {
                userId: ownerId,
                isMaster: true
            }, select: { organizationId: true }
        });
        if (checkOrg.length > 0) {
            let orgUsers = await this.prisma.organizationUser.findMany({
                where: { organizationId: { in: checkOrg.map(org => org.organizationId) } },
                select: { userId: true }
            });
            filter.id = { in: orgUsers.map(user => user.userId) };

            let users = await this.prisma.user.findMany({
                where: filter,
                include: {
                    Company: true,
                    Department: true,
                    FaceImages: true,
                    OrganizationUser: {
                        select: {
                            Organiztion: {
                                select: {
                                    id: true,
                                    name: true
                                }
                            }
                        }
                    }
                },
                skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
                take: perPage > 0 ? perPage : 1,
                orderBy: [
                    { name: 'asc' },
                    { adName: 'asc' },
                    { integrationKey: 'asc' }],
            });

            let count = await this.prisma.user.count({
                where: filter,
            });

            return await Promise.all(users.map(async (user) => {
                let userGql = this.userGqlFromPrisma(user);
                userGql.count = count;
                let faceImagesBaseArrayBase64 = user.FaceImages.map((el) => {
                    // let data = base64.encodeImage(join(this.faceImagePath, el.path));
                    return {
                        ...el,
                        data: ""
                    }
                })
                userGql.faceImagesArrayBase64 = faceImagesBaseArrayBase64;
                userGql.Organizations = [];
                for (let index = 0; index < user.OrganizationUser.length; index++) {
                    const orgUser = user.OrganizationUser[index];
                    let orgGql = new OrganizationGQL({ ...orgUser.Organiztion });
                    userGql.Organizations.push(orgGql);
                }
                return userGql;
            }))
        } else {
            return [];
        }
    }

    async webSearch(
        ownerId,
        query: string,
        departmentIds: string[],
        pageIndex: number,
        pageSize: number,
        status: number
    ): Promise<UserGql[]> {
        //#region Get highest user role
        let role: RoleType = RoleType.GUEST;
        let userRoles = await this.prisma.user.findMany({
            where: {
                id: ownerId
            }, select: { roleId: true }
        });
        for (let index = 0; index < userRoles.length; index++) {
            const userRole = userRoles[index];
            if (userRole.roleId == RoleType.SUPER_ADMIN) {
                role = RoleType.SUPER_ADMIN;
                break;
            }

            if (userRole.roleId == RoleType.ADMIN) {
                role = RoleType.ADMIN;
                break;
            }
            role = userRole.roleId;
        }
        //#endregion

        //#region Get active status from number
        let statusFilter: ActiveStatus[] = [ActiveStatus.ACTIVE, ActiveStatus.DEACTIVE]
        if (status == NumberStatus.Active) {
            statusFilter = [ActiveStatus.ACTIVE]
        } else {
            if (status == NumberStatus.InActive) {
                statusFilter = [ActiveStatus.DEACTIVE]
            }
        }
        //#endregion

        switch (role) {
            case RoleType.SUPER_ADMIN:
                return await this.webSearchSuperAdmin(ownerId, query, departmentIds, pageIndex, pageSize, statusFilter);
                break;
            case RoleType.ADMIN:
                return await this.webSearchAdmin(ownerId, query, departmentIds, pageIndex, pageSize, statusFilter)
                break;
            case RoleType.DEPARTMENT_ADMIN:
                return [];
                break;
            case RoleType.RECEPTION:
                return [];
                break;
            case RoleType.HR:
                return await this.webSearchHr(ownerId, query, departmentIds, pageIndex, pageSize, statusFilter)
                break;
            case RoleType.EMPLOYEE:
                return [];
                break;
            case RoleType.GUEST:
                return [];
                break;
            case RoleType.ATTANDANT_ADMIN:
                return [];
                break;
            case RoleType.ORG_ADMIN:
                return [];
                break;
            default:
                return [];
                break;
        }
        return [];
    }

    async getAllName(
        ownerId: string,
    ): Promise<UserGql[]> {
        let userFilter = await this.getUserFilterByRole(ownerId);
        userFilter.activeStatus = ActiveStatus.ACTIVE;
        let users = await this.prisma.user.findMany({
            where: userFilter,
            orderBy: [
                { name: 'asc' }
            ], select: {
                id: true,
                name: true
            }
        });

        return users.map(user => new UserGql({
            id: user.id,
            fullName: user.name
        }));
    }

    async usersToFile(
        ownerId: string,
    ): Promise<UserGql[]> {
        let userFilter = await this.getUserFilterByRole(ownerId);
        userFilter.FaceImages = { some: {} };
        let users = await this.prisma.user.findMany({
            where: userFilter,
            include: {
                Company: true,
                Department: true,
                FaceImages: true
            },
            orderBy: [
                { Company: { name: 'asc' } },
                { Department: { name: 'asc' } },
            ]
        });

        return users.map(user => this.userGqlFromPrisma(user));
    }

    async getAllUserNoImage(
        currentUserId: string,
        currentUserRole: string,
        currentComId: string,
        currentDepId: string,
        page: number,
        perPage: number,
    ): Promise<UserGql[]> {
        // let userFilter = await this.getUserFilterByRole(ownerId);
        let userFilter = await this.userFilterService.getUserFilterByRole(currentUserId, currentUserRole, currentComId, currentDepId);
        userFilter.FaceImages = { none: {} };
        let results: UserGql[] = [];
        let count = await this.prisma.user.count({ where: userFilter });

        let users = await this.prisma.user.findMany({
            where: userFilter,
            select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                Company: { select: { id: true, name: true } },
                Department: { select: { id: true, name: true } }
            },
            take: perPage,
            skip: Math.max(0, page - 1) * perPage,
        });

        for (let index = 0; index < users.length; index++) {
            const user = users[index];
            let gql = new UserGql({
                id: user.id,
                name: user.name,
                email: user.email,
                avatar: user.avatar,
                Company: new CompanyGql({ id: user.Company.id, name: user.Company.name }),
                Department: new DepartmentGql({ id: user.Department.id, name: user.Department.name }),
                count: count
            });

            results.push(gql);
        }

        return results;
    }

    async countAllUser(
        ownerId: string,
    ): Promise<number> {
        let userFilter = await this.getUserFilterByRole(ownerId);
        return await this.prisma.user.count({
            where: userFilter
            // where: {
            //     activeStatus: ActiveStatus.ACTIVE
            // }
        });
    }

    async countActiveUser(
        ownerId: string
    ): Promise<number> {
        let userFilter = await this.getUserFilterByRole(ownerId);
        userFilter.activeStatus = ActiveStatus.ACTIVE;
        return await this.prisma.user.count({
            where: userFilter
        });
    }

    async countDeactiveUser(
        ownerId: string,
    ): Promise<number> {
        let userFilter = await this.getUserFilterByRole(ownerId);
        userFilter.activeStatus = ActiveStatus.ACTIVE;
        return await this.prisma.user.count({
            where: userFilter
        });
    }

    async groupUserByCompany(
        ownerId: string
    ) {
        let userFilter = await this.getUserFilterByRole(ownerId);
        userFilter.activeStatus = ActiveStatus.ACTIVE;

        let users = await this.prisma.user.findMany({
            where: userFilter,
            orderBy: {
                companyId: 'asc'
            }, include: {
                Company: true
            }
        });
        if (!users) return [];
        return users.map(user => new UserGql({
            id: user.id,
            companyId: user.Company.id,
            companyName: user.Company.name
        }));
    }

    async getNameFiltered(
        companyId: string,
        departmentId: string,
        userStatus: number
    ): Promise<UserGql[]> {
        let depIds: string[] = [];
        //#region Get dep filter
        if (companyId != "-1") {    //filter by company
            if (departmentId == "-1") {    //Get all deps in company
                let deps = await this.prisma.department.findMany({
                    where: {
                        companyId: companyId
                    }
                });
                depIds = deps.map(dep => dep.id);
            } else {
                let dep = await this.prisma.department.findFirst({
                    where: {
                        id: departmentId
                    }
                });
                depIds = [dep.id];
            }
        } else {  //Get all deps in all companys
            let deps = await this.prisma.department.findMany();
            depIds = deps.map(dep => dep.id);
        }
        //#endregion

        //#region Get active status from number
        let statusFilter: ActiveStatus[] = [ActiveStatus.ACTIVE, ActiveStatus.DEACTIVE]
        if (userStatus == NumberStatus.Active) {
            statusFilter = [ActiveStatus.ACTIVE]
        } else {
            if (userStatus == NumberStatus.InActive) {
                statusFilter = [ActiveStatus.DEACTIVE]
            }
        }
        //#endregion
        let users = await this.prisma.user.findMany({
            where: {
                activeStatus: {
                    in: statusFilter
                },
                departmentId: {
                    in: depIds
                }
            }, orderBy: {
                name: 'asc'
            }
        });

        return users.map(user => this.userGqlFromUser(user));
    }

    async searchPage(
        query: string,
        page: number,
        perPage: number,
    ): Promise<any> {
        //#region Count all Users
        let userWhereInput: Prisma.UserWhereInput = {
            activeStatus: ActiveStatus.ACTIVE,
            AND: {
                OR: [
                    {
                        email: {
                            contains: query,
                            mode: 'insensitive'
                        }
                    },
                    {
                        name: {
                            contains: query,
                            mode: 'insensitive'
                        }
                    },
                    {
                        phone: {
                            contains: query,
                            mode: 'insensitive'
                        }
                    },
                    // { email: { search: query } },
                    // { name: { search: query } },
                    // { phone: { search: query } },
                ]
            }
        }
        let totalPage = await this.prisma.user.count({
            where: userWhereInput
        });
        //#endregion

        //#region Get user page
        let users = await this.prisma.user.findMany({
            where: userWhereInput,
            include: {
                Department: true,
                Company: true,
                FaceImages: true
            },
            skip: (page - 1) * perPage >= 0 ? (page - 1) * perPage : 0,
            take: perPage > 0 ? perPage : 1,
            orderBy: { dateCreated: 'desc' }
        });
        //#endregion

        return {
            personData: users.map(user => {
                let userGql = this.userGqlFromUser(user);
                userGql.company = new CompanyGql({
                    id: user.Company.id,
                    name: user.Company.name,
                });
                userGql.department = new DepartmentGql({
                    id: user.Department.id,
                    name: user.Department.name
                });
                userGql.faceImages = user.FaceImages.map(faceImage => new FaceImageGraphQL({
                    faceImageType: faceImage.faceImageType,
                    imagePath: faceImage.path
                }));
                return userGql;
            }),
            totalPages: totalPage,
        };
    }

    async getAllUserInDep(
        depIds: string[]
    ): Promise<UserGql[]> {
        let results: UserGql[] = [];
        let users = await this.prisma.user.findMany({
            where: {
                departmentId: { in: depIds },
                activeStatus: ActiveStatus.ACTIVE
            }, select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                integrationKey: true,
            }
        });
        for (let userIndex = 0; userIndex < users.length; userIndex++) {
            const user = users[userIndex];
            let gql = new UserGql({
                id: user.id,
                name: user.name,
                email: user.email,
                avatar: user.avatar,
                integrationKey: user.integrationKey,
            });
            results.push(gql);
        }
        return results;
    }

    async getFaceImageUser(
        userId: string
    ): Promise<FaceImageGraphQL[]> {
        let faceImage = [];
        let user = await this.prisma.user.findFirst({
            where: {
                id: userId
            }, include: {
                FaceImages: true
            }
        });

        if (!user) return faceImage;
        faceImage = user.FaceImages.map(faceImage => new FaceImageGraphQL({
            faceImageType: faceImage.faceImageType,
            imagePath: faceImage.path
        }));
        return faceImage;
    }
    //#endregion

    //#region User UPDATE
    async updateUserGraphql(
        ownerId: string,
        ownerRole: string,
        input: UserAddInput,
    ): Promise<UserGql> {
        this.systemLog.writeLog(ownerId, "USERS", SqlAction.Update, "User",
            `Update user`, "", JSON.stringify(input));
        if (ownerId != input.userId) {
            let currentUser = await this.prisma.user.findFirst({
                where: {
                    id: ownerId,
                    roleId: {
                        in: [
                            RoleType.SUPER_ADMIN,
                            RoleType.ADMIN,
                            RoleType.DEPARTMENT_ADMIN,
                            RoleType.ORG_ADMIN,
                        ]
                    }
                }, select: {
                    roleId: true,
                    companyId: true,
                    departmentId: true,
                    OrganizationUser: true,
                }
            });
            if (!currentUser) throw new ForbiddenException();

            switch (currentUser.roleId) {
                case RoleType.SUPER_ADMIN:
                    break;

                case RoleType.ADMIN:
                    let checkUser1 = await this.prisma.user.findFirst({
                        where: {
                            id: input.userId,
                            companyId: currentUser.companyId
                        }, select: { id: true }
                    });
                    if (!checkUser1) throw new ForbiddenException();
                    break;

                case RoleType.DEPARTMENT_ADMIN:
                    let checkUser2 = await this.prisma.user.findFirst({
                        where: {
                            id: input.userId,
                            departmentId: currentUser.departmentId
                        }, select: { id: true }
                    });
                    if (!checkUser2) throw new ForbiddenException();
                    break;

                case RoleType.ORG_ADMIN:
                    let checkUser3 = await this.prisma.organizationUser.findFirst({
                        where: {
                            userId: input.userId,
                            organizationId: { in: currentUser.OrganizationUser.map(org => org.organizationId) }
                        }, select: { userId: true }
                    });
                    if (!checkUser3) throw new ForbiddenException();
                    break;

                default:
                    throw new ForbiddenException();
                    break;
            }

        }

        let profile = new ProfileRequest({
            userId: input.userId,
            email: input.email.trim(),
            fullName: input.fullName.trim().toUpperCase().replace(/\s\s+/g, ' '),
            birthDay: input.birthDay,
            phoneNumber: input.phoneNumber,
            gender: input.gender,
            departmentId: input.departmentId,
            companyId: input.companyId,
            employeeKey: input.employeeCode,
            adName: input.adName,
            title: input.title,
            isFaceReScanEnable: input.isFaceReScanEnable,
            isRemoteCheckEnable: input.isRemoteCheckEnable,
            isLoginEnable: input.isLoginEnable,
            idCardNumber: input.idCardNumber,
            idCardJson: input.idCardNumberJson
        });

        const userId = await this.upsertProfile(ownerId, ownerRole, profile, null);

        return this.userTypeFromUser(await this.prisma.user.findFirst({
            where: { id: userId }
        }));
    }

    async clusterUpdateUserGraphql(
        ownerId: string,
        ownerRole: string,
        profile: ProfileRequest
    ): Promise<UserGql> {
        const userId = await this.upsertProfile(ownerId, ownerRole, profile, null);

        return this.userTypeFromUser(await this.prisma.user.findFirst({
            where: { id: userId }
        }));
    }

    async updateAvatar(ownerId: string, userId: string, files): Promise<UserGql> {
        // await writeFileAsync(join(getStaticFilesPath(), "avatar.png"), files.avatar[0].buffer);
        if (files == null) throw new BadRequestException("Invalid images");
        if (ownerId != userId) {
            let userRole = await this.prisma.user.findFirst({
                where: {
                    id: ownerId,
                    roleId: RoleType.SUPER_ADMIN
                }, select: { roleId: true }
            });
            if (!userRole) throw new UnauthorizedException();
        }
        let user = await this.prisma.user.findFirst({
            where: {
                id: userId
            }, include: {
                Company: true,
                Department: true,
                FaceImages: true,
            }
        });
        if (!user) throw new BadRequestException("User not found");
        await deleteFileAsync(join(this.avatarPath, user.avatar));
        var avatarFileName = await this.faceImageHandler.writeAvatarImageFile(files.avatar[0].buffer);
        user.avatar = avatarFileName;
        user = await this.prisma.user.update({
            where: {
                id: userId
            }, data: {
                avatar: avatarFileName
            }, include: {
                Company: true,
                Department: true,
                FaceImages: true,
            }
        });
        this.userGateway.sendUserUpdateRequest();
        if (user) {
            this.cachedGlobalConfig.cachedAllUser.set(userId, user);
        }
        return this.userGqlFromUser(user);
    };

    async updateUserFaceRecogThreshold(
        currentUserId: string,
        currentUserRole: string,
        currentComId: string,
        currentDepId: string,
        maskThreshold: number,
        noMaskThreshold: number,
        comIds: string[] = [],
        depIds: string[] = [],
        userIds: string[] = [],
    ): Promise<number> {
        let userFilter = await this.userFilterService.getUserFilterByRole(currentUserId, currentUserRole, currentComId, currentDepId, comIds, depIds, userIds);
        let userUpdated = await this.prisma.user.updateMany({
            where: userFilter,
            data: {
                MaskConfThreshold: maskThreshold,
                NoMaskConfThreshold: noMaskThreshold
            }
        });
        return userUpdated.count;
    }
    //#region Giang add checkQuality for Face Image upload

    // async upsertAndCheckQualityFaceImages(
    //     ownerId: string,
    //     userId: string,
    //     files,
    // ) {
    //     if (files == null) return;
    //     if (!(await checkFileSize(files, 3000000))) throw new BadRequestException("Max size violation images");
    //     if (!(await checkFileExt(files, ['png', 'jpg', 'jpeg']))) throw new BadRequestException("Invalid images");

    //     if (ownerId != userId) {
    //         let userRole = await this.prisma.userRole.findFirst({
    //             where: {
    //                 userId: ownerId,
    //                 roleId: { in: [RoleType.SUPER_ADMIN, RoleType.ADMIN] }
    //             }
    //         });
    //         if (!userRole) throw new UnauthorizedException();
    //     }

    //     // Should I do this respectively?
    //     // actually only 1 file?
    //     for (const [key, value] of Object.entries(files)) {

    //         let file = files[key];
    //         let scanResult = await this.faceImageQualityService.checkFaceFileQuality(
    //             userId,
    //             new FaceQualityCheckRequest({ userId: userId }),
    //             file
    //         );

    //         // Check File Images Quality
    //         if (scanResult[0].isQualified) {

    //             // Write new image
    //             let faceImages = await this.faceImageHandler.writeFaceImageFiles(userId, files);
    //             const currentFaceImage = await this.prisma.userFaceImage.findMany({
    //                 where: {
    //                     userId: userId
    //                 }
    //             });
    //             await this.prisma.userFaceImage.createMany({
    //                 data: faceImages
    //             });

    //             // remove old face image
    //             if (faceImages.some(e => e.faceImageType == currentFaceImage[0].faceImageType)) {
    //                 await this.prisma.userFaceImage.delete({
    //                     where: { id: currentFaceImage[0].id }
    //                 });
    //                 await deleteFileAsync(join(this.faceImagePath, currentFaceImage[0].path));
    //             }
    //         } else {
    //             throw new BadRequestException("Unqualified Avatar")
    //         }
    //     }
    // }

    async uploadFileAndCheckQuality(
        ownerId: string,
        ownerRole: string,
        userId: string,
        files,
        isByPass: boolean = false
    ) {
        if (files == null) throw new BadRequestException(FaceImageErrEnum.NoImageFile);
        if (!(await checkFileSize(files, 3000000))) throw new BadRequestException(FaceImageErrEnum.LimitedFileSize);
        if (!(await checkFileExt(files, ['png', 'jpg', 'jpeg', 'jfif']))) throw new BadRequestException(FaceImageErrEnum.InvalidFileType);

        // let lockFaceConfig = await this.prisma.serverConfig.findFirst({
        //     where: { key: "IS_LOCK_USER_UPDATE_FACE_IMAGE" },
        //     select: { value: true }
        // })
        // if (lockFaceConfig.value == "true" ? true : false) throw new BadRequestException("Lock Update Face Images")

        if (ownerId != userId) {
            let userRole = await this.prisma.user.findFirst({
                where: {
                    id: ownerId,
                    roleId: RoleType.SUPER_ADMIN
                }, select: { roleId: true }
            });
            if (!userRole) throw new UnauthorizedException();
        }
        let user = await this.prisma.user.findFirst({
            where: {
                id: userId
            },
            include: { FaceImages: true }
        });
        if (!user) throw new BadRequestException("User not found");

        // if (!user.isFaceReScanEnable) throw new BadRequestException("Lock Update Face Images")

        let checkQualityFaceImages = Object.assign({});

        //#region Add old images to check quality object
        for (let index = 0; index < user.FaceImages.length; index++) {
            const faceImage = user.FaceImages[index];
            checkQualityFaceImages[`${faceImage.faceImageType.charAt(0).toLowerCase() + faceImage.faceImageType.substring(1)}`] = [
                { buffer: await readFileBuffer(join(getUserImagePath(), faceImage.path)) }
            ];
        }
        //#endregion

        let keyFaceImage: string
        //#region Upsert requested file to check quality object
        for (const [key, value] of Object.entries(files)) {
            keyFaceImage = key.toLowerCase()
            checkQualityFaceImages[`${key}`] = [
                { buffer: value[0].buffer }
            ];
        }
        //#endregion

        // let scanResults = await this.faceImageQualityService.checkFaceFileQuality(
        //     userId,
        //     new FaceQualityCheckRequest({ userId: userId }),
        //     checkQualityFaceImages
        // );
        try {
            var scanResults = await this.faceImageQualityService.checkFaceFileQuality(
                userId,
                new FaceQualityCheckRequest({ userId: userId }),
                checkQualityFaceImages
            );
        } catch (error) {
            throw new BadRequestException("Can Not Get Scan Result")
        }
        let isQualified = true;
        let disQualifiedArr = [];
        for (let index = 0; index < scanResults.length; index++) {
            const scanResult = scanResults[index];
            if (!scanResult.isQualified) {
                isQualified = false;
                disQualifiedArr.push(scanResult)
            }
        }

        //#region NEW FIND DIFFERENT FACE IMAGE
        if (isQualified) {
            await this.upsertFaceImages(ownerId, ownerRole, userId, files,);
            // let user = await this.prisma.user.update({
            //     where: { id: userId },
            //     data: {
            //         isFaceReScanEnable: true
            //     }
            // })
        } else {
            if (isByPass) {
                await this.upsertFaceImages(ownerId, ownerRole, userId, files,);
                // let user = await this.prisma.user.update({
                //     where: { id: userId },
                //     data: {
                //         isFaceReScanEnable: true
                //     }
                // })
            } else {
                throw new BadRequestException(disQualifiedArr) // Status Code 400 => check shown noti
            }
        }
        //#endregion

        //update cached
        let updatedUser = await this.prisma.user.findFirst({
            where: {
                id: userId
            }, include: {
                Company: true,
                Department: true,
                FaceImages: true
            }
        });
        if (updatedUser) {
            this.cachedGlobalConfig.cachedAllUser.set(updatedUser.id, updatedUser);
        }
    };

    //#endregion

    async updateUserCompanyKey(
        userId: string
    ): Promise<number> {
        let user = await this.prisma.user.findFirst({
            where: {
                id: userId
            }, include: {
                Company: true
            }
        });
        let number = await this.prisma.user.count({
            where: {
                companyId: user.companyId,
                integrationKey: { not: "" }
            }
        });
        if (number) {
            let update = await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    integrationKey: `${user.Company.sortKey.trim().toUpperCase()}${NumberUtils.padLeadingZeros(number + 1, 6)}`
                }
            });
            this.userGateway.sendUserUpdateRequest();
            if (update) return 1;
            else return 0;
        } else {
            let update = await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    integrationKey: `${user.Company.sortKey.trim().toUpperCase()}${NumberUtils.padLeadingZeros(1, 6)}`
                }
            });
            this.userGateway.sendUserUpdateRequest();
            if (update) return 1;
            else return 0;
        }
    }

    async updateUserPatientId(
        ownerId: string,
        userId: string,
        patientId: string
    ) {
        let userRole = await this.prisma.user.findFirst({
            where: {
                id: ownerId,
                roleId: {
                    in: [RoleType.SUPER_ADMIN, RoleType.ADMIN, RoleType.EMPLOYEE, RoleType.RECEPTION]
                }
            }, select: { roleId: true }
        });
        if (!userRole) throw new ForbiddenException();

        let user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: { id: true }
        });
        if (!user) throw new BadRequestException(`${ErrorEnum['User not found']}_${StringUtils.getErrKeyName(ErrorEnum['User not found'])}`);

        let userWithPId = await this.prisma.user.findFirst({
            where: { patientId: patientId },
            select: { id: true }
        });
        if (userWithPId) {
            if (userWithPId.id != user.id) throw new BadRequestException(`${ErrorEnum['PID exited']}_${StringUtils.getErrKeyName(ErrorEnum['PID exited'])}`);
        }

        let patient = await this.prisma.patient.findFirst({
            where: { intergrationKey: patientId },
            select: { id: true }
        });
        if (patient) throw new BadRequestException(`${ErrorEnum['PID exited']}_${StringUtils.getErrKeyName(ErrorEnum['PID exited'])}`);

        let updated = await this.prisma.user.update({
            where: { id: userId },
            data: {
                patientId: patientId
            }, select: {
                id: true,
                patientId: true
            }
        });
        this.userGateway.sendUserUpdateRequest();

        return updated;
    }
    //#endregion

    //#region User DELETE
    async lockUserGraphql(
        currentUserId: string,
        userId: string,
        lock: boolean): Promise<boolean> {
        return await this.lockUser(currentUserId, userId, lock)
    }

    async lockAllUserGraphql(
        ownerId: string,
        ownerRole: string,
        isActive: boolean,
        userIds: string[] = [],
        depIds: string[] = [],
        comIds: string[] = [],
    ): Promise<number> {
        if (ownerRole != RoleType.SUPER_ADMIN) throw new ForbiddenException();

        let userFilter: Prisma.UserWhereInput = {
            OR: [
                { id: { in: userIds } },
                { companyId: { in: comIds } },
                { departmentId: { in: depIds } }
            ],
            activeStatus: { not: isActive ? ActiveStatus.ACTIVE : ActiveStatus.DEACTIVE }
        };

        let users = await this.prisma.user.findMany({
            where: userFilter,
            select: { id: true }
        });

        for (let index = 0; index < users.length; index++) {
            const user = users[index];
            let updateUser = await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    activeStatus: isActive ? ActiveStatus.ACTIVE : ActiveStatus.DEACTIVE
                }, select: { id: true }
            })
        }

        return users.length;
    }

    async unlockLogin(userId: string): Promise<boolean> {
        let user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: { id: true }
        });
        if (!user) throw new BadRequestException(`${ErrorEnum['User not found']}_${StringUtils.getErrKeyName(ErrorEnum['User not found'])}`);
        await this.prisma.userLoginFailed.deleteMany({
            where: { userId: user.id }
        });

        this.userGateway.sendUserUpdateRequest();
        return true;
    }

    async removeUserFaceImages(userId: string, faceImagePaths: string[]): Promise<number> {
        let user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: { id: true }
        });
        if (!user) return;
        // if (!user) throw new BadRequestException(`User not found`);

        if (await this.isUserLockFaceUpdate(user.id)) throw new BadRequestException("Lock Update Face Images")
        let deleted = 0;
        for (let index = 0; index < faceImagePaths.length; index++) {
            const faceImagePath = faceImagePaths[index];
            let faceImage = await this.prisma.userFaceImage.findFirst({
                where: {
                    path: faceImagePath,
                    User: { id: userId }
                }
            });
            if (faceImage) {
                let deletedImage = await this.prisma.deletedUserFaceImage.create({
                    data: {
                        faceImageType: faceImage.faceImageType,
                        path: faceImage.path,
                        User: { connect: { id: faceImage.userId } }
                    }
                });
                if (deletedImage) {
                    await this.prisma.userFaceImage.delete({
                        where: {
                            id: faceImage.id
                        }
                    });
                    deleted++;
                }
            }
        }

        await this.prisma.user.update({
            where: { id: userId },
            data: { dateModified: new Date() }
        });

        return deleted;
    }

    async removeFaceImage(faceImagePaths: string[]): Promise<number> {
        let user = await this.prisma.user.findFirst({
            where: {
                FaceImages: {
                    some: {
                        path: { in: faceImagePaths }
                    }
                }
            }, select: { id: true }
        });
        if (!user) return 0;
        // if (!user) throw new BadRequestException(`User not found`);

        let deleted = 0;
        for (let index = 0; index < faceImagePaths.length; index++) {
            const faceImagePath = faceImagePaths[index];
            let faceImage = await this.prisma.userFaceImage.findFirst({
                where: {
                    path: faceImagePath,
                    User: { id: user.id }
                }
            });
            if (faceImage) {
                let deletedImage = await this.prisma.deletedUserFaceImage.create({
                    data: {
                        faceImageType: faceImage.faceImageType,
                        path: faceImage.path,
                        User: { connect: { id: faceImage.userId } }
                    }
                });
                if (deletedImage) {
                    await this.prisma.userFaceImage.delete({
                        where: {
                            id: faceImage.id
                        }
                    });
                    deleted++;
                }
            }
        }

        return deleted;
    }

    async removeFaceImagesFromImportedFile(
        userId: string,
        files
    ) {
        //#region Check permission
        let checkPermiss = await this.prisma.user.count({
            where: {
                id: userId,
                roleId: RoleType.SUPER_ADMIN
            }
        });
        if (checkPermiss == 0) throw new UnauthorizedException();
        //#endregion

        let deletedImages = 0;
        let bstr = files['deleteImages'][0].buffer;
        const wb = XLSX.read(bstr, { type: 'buffer' });
        /* Get first worksheet */
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        /* Convert array of arrays */
        const data = XLSX.utils.sheet_to_json(ws);
        for (let index = 0; index < data.length; index++) {
            const row = data[index];
            // let userId = row['userId'].trim();
            let imageString = row['imagePath']
            let tmpArr = imageString.trim().split('/');
            let imageName = tmpArr[tmpArr.length - 1];
            let imageNameArr = imageName.split(".");
            if (imageNameArr.length == 1) {
                imageName = imageName + ".jpg";
            }

            let result = await this.removeFaceImage([imageName]);
            deletedImages = deletedImages + result;
        }
        return deletedImages;
    }

    async deleteUser(
        currentUserId: string,
        currentUserRole: string,
        userIds: string[],
        pass: string,
        tokenType: number
    ): Promise<number> {
        let validPass = this.passHandlerService.userValidatePass(currentUserId, pass, tokenType)
        if (!validPass) throw new ForbiddenException();

        if (currentUserRole != RoleType.SUPER_ADMIN &&
            currentUserRole != RoleType.ADMIN
        ) throw new ForbiddenException();

        let user = await this.prisma.user.findFirst({
            where: {
                id: currentUserId,
            }, select: { password: true }
        });
        if (!user) throw new ForbiddenException();

        let passValid = await bcrypt.compare(pass, user.password);
        if (!passValid) throw new ForbiddenException();

        for (let index = 0; index < userIds.length; index++) {
            const userId = userIds[index];
            let checkUser = await this.prisma.user.findFirst({
                where: {
                    id: userId,
                    roleId: { not: RoleType.SUPER_ADMIN }
                },
                select: {
                    id: true,
                    departmentId: true
                }
            });
            if (!checkUser) throw new BadRequestException(`${ErrorEnum['User not found']}_${StringUtils.getErrKeyName(ErrorEnum['User not found'])}`);
            // let checkUserInUsed = await this.prisma.user.count({
            //     where: {
            //         // id: userId,
            //         activeStatus: ActiveStatus.ACTIVE,
            //         OR: [{
            //             AcUserTime: {
            //                 some: {
            //                     userId: userId,
            //                     status: ActiveStatus.ACTIVE
            //                 }
            //             }
            //         },
            //         {
            //             Devices: {
            //                 some: {
            //                     userId: userId,
            //                     status: ActiveStatus.ACTIVE
            //                 }
            //             }
            //         }, {
            //             Department: {
            //                 OR: [
            //                     {
            //                         AcDepartmentTime: {
            //                             some: {
            //                                 departmentId: checkUser.departmentId,
            //                                 status: ActiveStatus.ACTIVE
            //                             }
            //                         }
            //                     },
            //                 ]
            //             }
            //         }]
            //     }
            // });

            // if (checkUserInUsed > 0) throw new BadRequestException(`${ErrorEnum['User in used']}_${StringUtils.getErrKeyName(ErrorEnum['User in used'])}`);
            await this.prisma.deletedUsers.upsert({
                where: { userId: userId },
                create: { userId: userId },
                update: {}
            });
        }

        let deleted = await this.prisma.user.deleteMany({
            where: { id: { in: userIds } }
        });
        for (let index = 0; index < userIds.length; index++) {
            const userId = userIds[index];
            this.cachedGlobalConfig.cachedAllUser.delete(userId);
            this.cachedGlobalConfig.cachedAllUserId.delete(userId);
        }
        return deleted.count;
    }

    async deleteUserByRole(
        currentUserId: string,
        currentRole: string,
        currentComId: string,
        currentDepId: string,
        userIds: string[],
        pass: string,
        tokenType: number
    ): Promise<string[]> {
        let results: string[] = []
        let allowUserIds = await this.userFilterService.getUserIdsByRole(currentUserId, currentRole, currentComId, currentDepId, [], [], userIds);
        if (allowUserIds.length == 0 && currentRole != RoleType.SUPER_ADMIN) {
            throw new ForbiddenException;
        }
        let validPass = this.passHandlerService.userValidatePass(currentUserId, pass, tokenType)
        if (!validPass) throw new ForbiddenException();

        let user = await this.prisma.user.findFirst({
            where: {
                id: currentUserId,
            }, select: { password: true }
        });
        if (!user) throw new ForbiddenException();

        let passValid = await bcrypt.compare(pass, user.password);
        if (!passValid) throw new ForbiddenException();

        for (let index = 0; index < allowUserIds.length; index++) {
            const userId = allowUserIds[index];
            let checkUser = await this.prisma.user.findFirst({
                where: { id: userId },
                select: {
                    id: true,
                    departmentId: true
                }
            });
            if (!checkUser) throw new BadRequestException(`${ErrorEnum['User not found']}_${StringUtils.getErrKeyName(ErrorEnum['User not found'])}`);
            // let checkUserInUsed = await this.prisma.user.count({
            //     where: {
            //         // id: userId,
            //         activeStatus: ActiveStatus.ACTIVE,
            //         OR: [{
            //             AcUserTime: {
            //                 some: {
            //                     userId: userId,
            //                     status: ActiveStatus.ACTIVE
            //                 }
            //             }
            //         },
            //         {
            //             Devices: {
            //                 some: {
            //                     userId: userId,
            //                     status: ActiveStatus.ACTIVE
            //                 }
            //             }
            //         }, {
            //             Department: {
            //                 OR: [
            //                     {
            //                         AcDepartmentTime: {
            //                             some: {
            //                                 departmentId: checkUser.departmentId,
            //                                 status: ActiveStatus.ACTIVE
            //                             }
            //                         }
            //                     },
            //                 ]
            //             }
            //         }]
            //     }
            // });

            // if (checkUserInUsed > 0) throw new BadRequestException(`${ErrorEnum['User in used']}_${StringUtils.getErrKeyName(ErrorEnum['User in used'])}`);
            await this.prisma.deletedUsers.upsert({
                where: { userId: userId },
                create: { userId: userId },
                update: {}
            });
            await this.prisma.user.delete({
                where: { id: userId }
            });

            this.cachedGlobalConfig.cachedAllUser.delete(userId);
            this.cachedGlobalConfig.cachedAllUserId.delete(userId);
            results.push(userId);
        }

        return results;
    }
    //#endregion

    //#region Site sync
    async clusterUpsert(
        req: UserClusterUpsertRequest): Promise<{
            id: string;
        }> {
        let company = await this.prisma.company.findFirst({
            where: { id: req.companyId },
            select: { id: true }
        });
        if (!company) throw new BadRequestException("Compnay not found");

        let department = await this.prisma.department.findFirst({
            where: { id: req.departmentId },
            select: { id: true }
        });
        if (!department) throw new BadRequestException("Department not found");

        let user = await this.prisma.user.findFirst({
            where: { id: req.userId }
        });
        let checkUserEmail = await this.prisma.user.findFirst({
            where: { email: req.userEmail },
            select: { id: true }
        });

        let updateUserData: Prisma.UserUpdateInput = {
            email: req.userEmail,
            name: req.userName,
            birthday: req.birthday,
            password: req.userPass,
            gender: req.gender,
            avatar: req.avatar,
            Company: { connect: { id: req.companyId } },
            Department: { connect: { id: req.departmentId } },
            integrationKey: req.integrationKey,
            phone: req.phone,
            activeStatus: ActiveStatus[req.activeStatus],
            isClusterSync: true,
            adName: req.adName,
            // failedCounter: req.failedCounter,
            isLoginAvaiable: req.isLoginAvaiable,
        };

        let userCreateData: Prisma.UserCreateInput = {
            id: req.userId,
            email: req.userEmail,
            name: req.userName,
            birthday: req.birthday,
            password: req.userPass,
            gender: req.gender,
            avatar: req.avatar,
            Company: { connect: { id: req.companyId } },
            Department: { connect: { id: req.departmentId } },
            integrationKey: req.integrationKey,
            phone: req.phone,
            activeStatus: ActiveStatus[req.activeStatus],
            isClusterSync: true,
            adName: req.adName,
            // failedCounter: req.failedCounter,
            isLoginAvaiable: req.isLoginAvaiable,
        }

        if (!user) {//Create
            if (checkUserEmail) {
                checkUserEmail.id = req.userId;
                await this.siteUpsertUserImages(req);
                return checkUserEmail;
            } else {

                let upsertedUser = await this.prisma.user.create({
                    data: userCreateData,
                    select: { id: true }
                });
                await this.siteUpsertUserImages(req);
                this.userGateway.sendUserUpdateRequest();
                return upsertedUser;
            }
        } else {
            if (checkUserEmail) {
                if (checkUserEmail.id != req.userId) {
                    checkUserEmail.id = req.userId;
                    return checkUserEmail;
                } else {
                    let upsertedUser = await this.prisma.user.update({
                        where: { id: req.userId },
                        data: updateUserData,
                        select: { id: true }
                    });

                    await this.siteUpsertUserImages(req);
                    this.userGateway.sendUserUpdateRequest();
                    return upsertedUser;
                }
            } else {
                let upsertedUser = await this.prisma.user.update({
                    where: { id: req.userId },
                    data: updateUserData,
                    select: { id: true }
                });

                await this.siteUpsertUserImages(req);
                this.userGateway.sendUserUpdateRequest();
                return upsertedUser;
            }
        }
    }

    async siteUpsertUserImages(req: UserClusterUpsertRequest) {
        if (req.avatarBase64) {
            const fileName: string = req.avatar;
            await writeBase64FileAsync(join(this.avatarPath, fileName), req.avatarBase64);
        }

        if (req.topBase64 && req.topPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.Top, req.topPath, req.topBase64);
        if (req.centerBase64 && req.centerPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.Center, req.centerPath, req.centerBase64);
        if (req.leftBase64 && req.leftPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.Left, req.leftPath, req.leftBase64);
        if (req.rightBase64 && req.rightPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.Right, req.rightPath, req.rightBase64);
        if (req.fTopBase64 && req.fTopPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.FlippedTop, req.fTopPath, req.fTopBase64);
        if (req.fCenterBase64 && req.fCenterPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.FlippedCenter, req.fCenterPath, req.fCenterBase64);
        if (req.fLeftBase64 && req.fLeftPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.FlippedLeft, req.fLeftPath, req.fLeftBase64);
        if (req.fRightBase64 && req.fRightPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.FlippedRight, req.fRightPath, req.fRightBase64);
        if (req.topLeftPath && req.topLeftBase64) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.TopLeft, req.topLeftPath, req.topLeftBase64);
        if (req.topRightBase64 && req.topRightPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.TopRight, req.topRightPath, req.topRightBase64);
        if (req.fTopLeftBase64 && req.fTopLeftPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.FlippedTopLeft, req.fTopLeftPath, req.fTopLeftBase64);
        if (req.fTopRightBase64 && req.fTopRightPath) await this.faceImageHandler.upsertFaceImageBase64(req.userId, FaceImageType.FlippedTopRight, req.fTopRightPath, req.fTopRightBase64);
    }
    //#endregion

    //#region LOCK USER UPDATE FACE IMAGE
    async unlockUpdateFaceImage(
        ownerId: string,
        userId: string
    ) {
        let user = await this.prisma.user.findFirst({
            where: {
                id: ownerId,
                roleId: RoleType.SUPER_ADMIN
            }, select: {
                id: true,
                isFaceReScanEnable: true
            }
        });
        if (!user) throw new ForbiddenException();

        if (!user.isFaceReScanEnable) {
            let user = await this.prisma.user.update({
                where: { id: userId },
                data: {
                    isFaceReScanEnable: true
                },
                select: {
                    id: true
                }
            })
        }
        return true
    }

    async lockUpdateFaceImage(
        ownerId: string,
        userId: string
    ) {

        let user = await this.prisma.user.findFirst({
            where: {
                id: ownerId,
                roleId: RoleType.SUPER_ADMIN
            }, select: {
                id: true,
                isFaceReScanEnable: true
            }
        });
        if (!user) throw new ForbiddenException();

        if (user.isFaceReScanEnable) {
            let user = await this.prisma.user.update({
                where: { id: userId },
                data: {
                    isFaceReScanEnable: false
                }
            })
        }

        return true
    }

    async lockFaceImageStatus(userId: string) {
        let user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: {
                id: true,
                isFaceReScanEnable: true
            }
        })

        return user.isFaceReScanEnable
    }

    async isUserLockFaceUpdate(userId: string) {
        let lockFaceConfig = await this.prisma.serverConfig.findFirst({
            where: { key: "IS_LOCK_USER_UPDATE_FACE_IMAGE" },
            select: { value: true }
        })
        if (lockFaceConfig.value == "false") return false;

        let user = await this.prisma.user.findFirst({
            where: { id: userId },
            select: {
                id: true,
                isFaceReScanEnable: true
            }
        })

        if (!user) return false;
        else return false;
    }
    //#endregion

    async confirmByPass(
        ownerId: string,
        pass: string,
        tokenType: number
    ) {

        let validPass = this.passHandlerService.userValidatePass(ownerId, pass, tokenType)
        if (!validPass) throw new ForbiddenException();
        let user = await this.prisma.user.findFirst({
            where: {
                id: ownerId,
                roleId: RoleType.SUPER_ADMIN
            }, select: { password: true }
        });
        if (!user) throw new ForbiddenException();

        return true
    }

    async lockAllUser() {
        let lockUsers = await this.prisma.user.updateMany({
            where: {
                roleId: { not: RoleType.SUPER_ADMIN }
            }, data: { activeStatus: ActiveStatus.DEACTIVE }
        });
        return lockUsers.count;
    }

    async importUserSF4C(
        currentId: string,
        file
    ) {

        let userRole = await this.prisma.user.findFirst({
            where: {
                id: currentId,
                roleId: { in: [RoleType.SUPER_ADMIN] }
            }, select: { roleId: true }
        });

        if (!userRole) throw new ForbiddenException();

        let wb = excelUtils.readExcelFile(file)
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const data = XLSX.utils.sheet_to_json(ws, { header: 3, range: 2 });

        for (let index = 0; index < data.length; index++) {
            let inputEmail = data[index]["Email công ty"].trim().toLowerCase()

            let check = await this.prisma.user.findUnique({
                where: { email: inputEmail }
            })

            if (!check) {
                continue;
            }
            let update = await this.prisma.user.update({
                where: { email: inputEmail },
                data: { integrationKey: data[index]["Mã nhân viên (SF4C)"].trim() }
            })
        }
        return data.length

    }
}



