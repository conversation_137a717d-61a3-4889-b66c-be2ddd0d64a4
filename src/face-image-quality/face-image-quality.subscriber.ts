import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { Prisma, ServerConfigKey } from "@prisma/client";
import { MqttService } from "src/mqtt/mqtt.service";
import { PrismaService } from "src/prisma/prisma.service";
import { connect } from 'mqtt';
import FaceQualityResultMqtt from "./models/face-quality-result.mess";
import FaceQualityGlobalCheckModel from "./models/face-quality-global-check.model";

@Injectable()
export class FaceImageQualitySubscriber implements OnModuleInit {
    private readonly logger = new Logger(FaceImageQualitySubscriber.name);
    private isDebug = false;
    constructor(
        private mqtt: MqttService,
        private prisma: PrismaService,
    ) { }
    async onModuleInit() {
        let brokerAdd = await this.prisma.serverConfig.findFirst({
            where: { key: ServerConfigKey.BROKER_ADDRESS }
        })

        let ackTopicConfig = await this.prisma.serverConfig.findFirst({
            where: { key: ServerConfigKey.BROKER_FACE_QUALITY_ACK }
        })

        const ackClient = connect(brokerAdd.value, this.mqtt.options)
        ackClient.on('connect', function () {
            let logger = new Logger("FaceImageQualitySubscriber");
            logger.log("Connected")
            ackClient.subscribe(ackTopicConfig.value, function (err) {
                if (err) {
                    let logger = new Logger("FaceImageQualitySubscriber");
                    logger.error(err)
                }
            })
        })

        ackClient.on('error', function (err) {
            let logger = new Logger("FaceImageQualitySubscriber");
            logger.error(err)
        })

        ackClient.on('message', (topic, message) => {
            let res = new FaceQualityResultMqtt(JSON.parse(message.toString()));
            this.onFaceQualityResultReceived(res);
        })
    }

    async onFaceQualityResultReceived(res: FaceQualityResultMqtt) {
        let checkEvent = await this.prisma.checkFaceQualityEvent.findFirst({
            where: {
                id: res.event_id,
                matcherId: res.device_id
            }
        });
        if (!checkEvent) return;
        let processedCount = 0;
        if (this.isDebug) {
            this.logger.debug(`Event ${checkEvent.id}. Total images: ${checkEvent.totalImage}`);
        }
        for (let index = 0; index < res.img_info.length; index++) {
            const imgInfo = res.img_info[index];
            let updateRes = await this.prisma.checkFaceQualityResult.update({
                where: {
                    imageName_eventId: {
                        imageName: imgInfo.img_name,
                        eventId: checkEvent.id
                    }
                }, data: {
                    isProcessed: true,
                    isError: imgInfo.img_error == "1" ? true : false,
                    isCropFailed: imgInfo.crop_failed == "1" ? true : false,
                    isPoseFailed: imgInfo.pose_failed == "1" ? true : false,
                    isTooSmall: imgInfo.too_small == "1" ? true : false,
                    isBlur: imgInfo.blur == "1" ? true : false,
                    isOcclustion: imgInfo.occlusion == "1" ? true : false,
                    isLocalDiff: imgInfo.local_different == "1" ? true : false,
                    isGlobalExisted: imgInfo.global_existed?.existed == "1" ? true : false,
                    globalDbId: imgInfo.global_existed.db_id,
                    globalPersonId: imgInfo.global_existed.person_id,
                    globalPersonName: imgInfo.global_existed.person_name,
                    globalImage: imgInfo.global_existed.image,
                    globalConfident: imgInfo.global_existed?.confident ? parseFloat(imgInfo.global_existed?.confident) : null,
                    isMask: imgInfo.mask == "1" ? true : false,
                    isDark: imgInfo.dark == "1" ? true : false,
                    darkValue: imgInfo.dark_val,
                }
            });
            if (updateRes) processedCount++;
            if (this.isDebug) {
                this.logger.debug(`Event ${checkEvent.id}_${imgInfo.img_name} checked: ${processedCount}`)
            }
        }
        if (processedCount >= checkEvent.totalImage) {
            await this.prisma.checkFaceQualityEvent.update({
                where: { id: checkEvent.id },
                data: { isFinished: true }
            });
        }
    }
}