-- Test logic JOIN để tìm vấn đề

-- 1. TEST LOGIC HIỆN TẠI TRONG CODE
SELECT 'Current code logic test' as test_type;

SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::date as violation_date,
    attr."dateCreated"::time as violation_time,
    su."startDate" as shift_date,
    su."shiftId",
    st."startHour",
    st."endHour",
    
    -- <PERSON><PERSON><PERSON> tra từng điều kiện
    CASE 
        WHEN attr."dateCreated"::date = su."startDate"::date 
        THEN '✅ Date match'
        ELSE '❌ Date not match'
    END as date_check,
    
    CASE 
        WHEN attr."dateCreated"::time >= st."startHour"::time
        THEN '✅ After start hour'
        ELSE '❌ Before start hour'
    END as start_hour_check,
    
    CASE 
        WHEN attr."dateCreated"::time <= st."endHour"::time
        THEN '✅ Before end hour'
        ELSE '❌ After end hour'
    END as end_hour_check,
    
    -- <PERSON><PERSON><PERSON> quả cuối cùng
    CASE 
        WHEN attr."dateCreated"::date = su."startDate"::date
             AND attr."dateCreated"::time >= st."startHour"::time
             AND attr."dateCreated"::time <= st."endHour"::time
        THEN '✅ SHOULD BE COUNTED'
        ELSE '❌ SHOULD BE EXCLUDED'
    END as final_result

FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND attr."dateCreated"::date = su."startDate"::date  -- Logic hiện tại
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated";

-- 2. SO SÁNH VỚI LOGIC KHÔNG CÓ SHIFT FILTERING
SELECT 'Comparison without shift filtering' as test_type;

SELECT 
    'Total violations (no shift filter)' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)

UNION ALL

SELECT 
    'Violations with shift filtering' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND attr."dateCreated"::date = su."startDate"::date
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND attr."dateCreated"::time >= st."startHour"::time
    AND attr."dateCreated"::time <= st."endHour"::time
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18);

-- 3. PHÂN TÍCH CHI TIẾT VI PHẠM 05:25 VÀ 10:34
SELECT 'Detailed analysis of specific violations' as test_type;

SELECT 
    attr."dateCreated",
    attr."dateCreated"::time as violation_time,
    '05:25 should be EXCLUDED (before 08:30)' as expected_result,
    
    -- Kiểm tra có shift match không
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM "ShiftUser" su
            INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
            WHERE su."userId" = '62417db10f022fca95fa3e40'
                AND attr."dateCreated"::date = su."startDate"::date
        ) THEN 'Has shift match'
        ELSE 'No shift match'
    END as shift_match,
    
    -- Kiểm tra time filtering
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM "ShiftUser" su
            INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
            WHERE su."userId" = '62417db10f022fca95fa3e40'
                AND attr."dateCreated"::date = su."startDate"::date
                AND attr."dateCreated"::time >= st."startHour"::time
                AND attr."dateCreated"::time <= st."endHour"::time
        ) THEN 'Within working hours'
        ELSE 'Outside working hours'
    END as time_check

FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)
    AND attr."dateCreated"::time IN ('05:25:07.311', '10:34:02.767')
ORDER BY attr."dateCreated";

-- 4. KIỂM TRA VIOLATION COUNT SERVICE LOGIC
SELECT 'Violation count service logic test' as test_type;

-- Uniform violations (Monday only + shift filtering)
SELECT 
    'Uniform violations (Monday + shift filter)' as metric,
    COUNT(DISTINCT attr."dateCreated"::date) as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND attr."dateCreated"::time >= shift_type."startHour"::time
    AND attr."dateCreated"::time <= shift_type."endHour"::time
    AND EXTRACT(DOW FROM attr."dateCreated") = 1  -- Monday only
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" = 16

UNION ALL

-- Card violations (all days + shift filtering)
SELECT 
    'Card violations (all days + shift filter)' as metric,
    COUNT(DISTINCT attr."trackId") as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND attr."dateCreated"::time >= shift_type."startHour"::time
    AND attr."dateCreated"::time <= shift_type."endHour"::time
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" = 18
    AND NOT EXISTS (
        SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
        WHERE attr2."trackId" = attr."trackId"
        AND attr2."type" = 17
    );
