-- <PERSON><PERSON><PERSON> đơn giản để kiểm tra break time exclusion
-- Thay userId và date range trực tiếp trong query

-- 1. CHI TIẾT VI PHẠM VÀ BREAK TIME
SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::time as violation_time,
    shift_type."startBreak" as break_start_text,
    shift_type."endBreak" as break_end_text,
    shift_type."startBreak"::time as break_start_time,
    shift_type."endBreak"::time as break_end_time,
    
    -- <PERSON><PERSON> tích break time
    CASE 
        WHEN shift_type."startBreak" IS NULL OR shift_type."endBreak" IS NULL
        THEN 'NULL break time'
        WHEN shift_type."startBreak"::time <= '00:00:00'::time
        THEN 'No break (startBreak = 00:00)'
        WHEN shift_type."endBreak"::time <= '00:00:00'::time  
        THEN 'No break (endBreak = 00:00)'
        WHEN shift_type."startBreak" = shift_type."endBreak"
        THEN 'No break (start = end)'
        WHEN attr."dateCreated"::time < shift_type."startBreak"::time
        THEN 'TRƯỚC break time ✅ TÍNH'
        WHEN attr."dateCreated"::time > shift_type."endBreak"::time
        THEN 'SAU break time ✅ TÍNH'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
        THEN 'TRONG break time ❌ LOẠI TRỪ'
        ELSE 'UNKNOWN'
    END as break_analysis,
    
    -- Logic cuối cùng trong code
    CASE 
        WHEN NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                 AND attr."dateCreated"::time <= shift_type."endBreak"::time
                 AND shift_type."startBreak" IS NOT NULL
                 AND shift_type."endBreak" IS NOT NULL
                 AND shift_type."startBreak"::time > '00:00:00'::time
                 AND shift_type."endBreak"::time > '00:00:00'::time
                 AND shift_type."startBreak" != shift_type."endBreak")
        THEN '✅ ĐƯỢC TÍNH'
        ELSE '❌ BỊ LOẠI TRỪ'
    END as final_result,
    
    CASE attr."type"
        WHEN 16 THEN 'Đồng phục'
        WHEN 18 THEN 'Thẻ'
        ELSE 'Khác'
    END as violation_type
    
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= '2024-12-01'::date
    AND attr."dateCreated" <= '2024-12-31'::date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated"
LIMIT 20;

-- 2. THỐNG KÊ TỔNG QUAN
SELECT '=== THỐNG KÊ BREAK TIME EXCLUSION ===' as title;

SELECT 
    CASE 
        WHEN shift_type."startBreak" IS NULL OR shift_type."endBreak" IS NULL
             OR shift_type."startBreak"::time <= '00:00:00'::time
             OR shift_type."endBreak"::time <= '00:00:00'::time
             OR shift_type."startBreak" = shift_type."endBreak"
        THEN 'no_break_time'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
        THEN 'in_break_time'
        ELSE 'outside_break_time'
    END as break_status,
    COUNT(*) as violation_count,
    CASE 
        WHEN shift_type."startBreak" IS NULL OR shift_type."endBreak" IS NULL
             OR shift_type."startBreak"::time <= '00:00:00'::time
             OR shift_type."endBreak"::time <= '00:00:00'::time
             OR shift_type."startBreak" = shift_type."endBreak"
        THEN 'Được tính (không có break time)'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
        THEN 'Bị loại trừ (trong break time)'
        ELSE 'Được tính (ngoài break time)'
    END as result_description
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= '2024-12-01'::date
    AND attr."dateCreated" <= '2024-12-31'::date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND attr."type" IN (16, 18)
GROUP BY 
    CASE 
        WHEN shift_type."startBreak" IS NULL OR shift_type."endBreak" IS NULL
             OR shift_type."startBreak"::time <= '00:00:00'::time
             OR shift_type."endBreak"::time <= '00:00:00'::time
             OR shift_type."startBreak" = shift_type."endBreak"
        THEN 'no_break_time'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
        THEN 'in_break_time'
        ELSE 'outside_break_time'
    END,
    CASE 
        WHEN shift_type."startBreak" IS NULL OR shift_type."endBreak" IS NULL
             OR shift_type."startBreak"::time <= '00:00:00'::time
             OR shift_type."endBreak"::time <= '00:00:00'::time
             OR shift_type."startBreak" = shift_type."endBreak"
        THEN 'Được tính (không có break time)'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
        THEN 'Bị loại trừ (trong break time)'
        ELSE 'Được tính (ngoài break time)'
    END
ORDER BY break_status;

-- 3. KIỂM TRA LOGIC CUỐI CÙNG
SELECT '=== KIỂM TRA LOGIC CUỐI CÙNG ===' as title;

SELECT 
    'Tổng vi phạm trong database' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= '2024-12-01'::date
    AND attr."dateCreated" <= '2024-12-31'::date + interval '1 day'
    AND result."userIdReCheckResult" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND attr."type" IN (16, 18)

UNION ALL

SELECT 
    'Vi phạm sau khi áp dụng break exclusion' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= '2024-12-01'::date
    AND attr."dateCreated" <= '2024-12-31'::date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND attr."type" IN (16, 18)
    -- Logic break time exclusion
    AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
             AND shift_type."startBreak" IS NOT NULL
             AND shift_type."endBreak" IS NOT NULL
             AND shift_type."startBreak"::time > '00:00:00'::time
             AND shift_type."endBreak"::time > '00:00:00'::time
             AND shift_type."startBreak" != shift_type."endBreak");
