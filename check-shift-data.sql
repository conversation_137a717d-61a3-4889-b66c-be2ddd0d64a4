-- <PERSON><PERSON><PERSON> kiểm tra dữ liệu shift và vi phạm
-- Thay userId và date range

-- 1. KIỂM TRA SHIFTUSER CỦA USER
SELECT 
    'ShiftUser data for user' as info,
    su."userId",
    su."shiftId", 
    su."startDate",
    su."endDate",
    st."startHour",
    st."endHour",
    st."startBreak",
    st."endBreak"
FROM "ShiftUser" su
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE su."userId" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND su."startDate" >= '2024-12-01'::date
    AND su."startDate" <= '2024-12-31'::date
ORDER BY su."startDate";

-- 2. KIỂM TRA VI PHẠM VÀ SHIFT MATCHING
SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::date as violation_date,
    attr."dateCreated"::time as violation_time,
    su."startDate" as shift_date,
    su."shiftId",
    st."startHour",
    st."endHour",
    
    -- <PERSON>ểm tra date matching
    CASE 
        WHEN attr."dateCreated"::date = su."startDate"::date 
        THEN '✅ Date MATCH'
        ELSE '❌ Date NOT MATCH'
    END as date_match,
    
    -- Kiểm tra time trong ca
    CASE 
        WHEN attr."dateCreated"::time < st."startHour"::time
        THEN '❌ TRƯỚC ca làm việc'
        WHEN attr."dateCreated"::time > st."endHour"::time  
        THEN '❌ SAU ca làm việc'
        ELSE '✅ TRONG ca làm việc'
    END as time_check,
    
    -- Kết quả cuối cùng
    CASE 
        WHEN attr."dateCreated"::date = su."startDate"::date
             AND attr."dateCreated"::time >= st."startHour"::time
             AND attr."dateCreated"::time <= st."endHour"::time
        THEN '✅ ĐƯỢC TÍNH'
        ELSE '❌ KHÔNG TÍNH'
    END as final_result,
    
    CASE attr."type"
        WHEN 16 THEN 'Đồng phục'
        WHEN 18 THEN 'Thẻ'
        ELSE 'Khác'
    END as violation_type

FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
LEFT JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND attr."dateCreated"::date = su."startDate"::date  -- Điều kiện date matching
LEFT JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2024-12-01'::date
    AND attr."dateCreated" <= '2024-12-31'::date + interval '1 day'
    AND result."userIdReCheckResult" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated"
LIMIT 20;

-- 3. SO SÁNH VI PHẠM CÓ/KHÔNG CÓ SHIFT FILTERING
SELECT '=== SO SÁNH VI PHẠM ===' as title;

-- Tổng vi phạm KHÔNG có shift filtering
SELECT 
    'Tổng vi phạm (không filter shift)' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= '2024-12-01'::date
    AND attr."dateCreated" <= '2024-12-31'::date + interval '1 day'
    AND result."userIdReCheckResult" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND attr."type" IN (16, 18)

UNION ALL

-- Vi phạm CÓ shift filtering (như trong code)
SELECT 
    'Vi phạm trong ca làm việc' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= '2024-12-01'::date
    AND attr."dateCreated" <= '2024-12-31'::date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND attr."dateCreated"::time >= shift_type."startHour"::time
    AND attr."dateCreated"::time <= shift_type."endHour"::time
    AND result."userIdReCheckResult" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND attr."type" IN (16, 18);

-- 4. KIỂM TRA SHIFT PERIOD
SELECT '=== KIỂM TRA SHIFT PERIOD ===' as title;

SELECT 
    su."shiftId",
    su."startDate",
    su."endDate",
    CASE 
        WHEN su."endDate" IS NULL THEN 'Shift vô thời hạn'
        WHEN su."startDate" = su."endDate" THEN 'Shift 1 ngày'
        ELSE 'Shift nhiều ngày: ' || (su."endDate" - su."startDate")::text || ' days'
    END as shift_period,
    st."startHour",
    st."endHour"
FROM "ShiftUser" su
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE su."userId" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND (su."startDate" <= '2024-12-31'::date)
    AND (su."endDate" IS NULL OR su."endDate" >= '2024-12-01'::date)
ORDER BY su."startDate";

-- 5. KIỂM TRA VI PHẠM NGOÀI CA
SELECT '=== VI PHẠM NGOÀI CA LÀM VIỆC ===' as title;

SELECT 
    attr."dateCreated",
    attr."dateCreated"::time as violation_time,
    'Không có shift match' as reason
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= '2024-12-01'::date
    AND attr."dateCreated" <= '2024-12-31'::date + interval '1 day'
    AND result."userIdReCheckResult" = '03c5c8cc-3750-4845-ba67-67a95743993c'
    AND attr."type" IN (16, 18)
    AND NOT EXISTS (
        SELECT 1 FROM "ShiftUser" su
        INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
        WHERE su."userId" = result."userIdReCheckResult"
            AND attr."dateCreated"::date = su."startDate"::date
            AND attr."dateCreated"::time >= st."startHour"::time
            AND attr."dateCreated"::time <= st."endHour"::time
    )
ORDER BY attr."dateCreated"
LIMIT 10;
