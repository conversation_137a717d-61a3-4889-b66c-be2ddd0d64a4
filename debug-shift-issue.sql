-- Script debug shift matching issue
-- Thay userId và date range

-- 1. KIỂM TRA USER CÓ SHIFTUSER DATA KHÔNG?
SELECT 'ShiftUser data for user' as check_type;

SELECT 
    su."userId",
    su."shiftId", 
    su."startDate",
    su."endDate",
    st."startHour",
    st."endHour",
    st."startBreak",
    st."endBreak",
    st."description"
FROM "ShiftUser" su
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE su."userId" = '62417db10f022fca95fa3e40'
ORDER BY su."startDate" DESC
LIMIT 10;

-- 2. KIỂM TRA SHIFTUSER TRONG KHOẢNG THỜI GIAN
SELECT 'ShiftUser in date range' as check_type;

SELECT 
    su."userId",
    su."shiftId", 
    su."startDate",
    su."endDate",
    CASE 
        WHEN su."endDate" IS NULL THEN 'Vô thời hạn'
        ELSE su."endDate"::text
    END as end_date_display
FROM "ShiftUser" su
WHERE su."userId" = '62417db10f022fca95fa3e40'
    AND (
        (su."startDate" <= '2025-06-30'::date)
        AND (su."endDate" IS NULL OR su."endDate" >= '2025-06-01'::date)
    )
ORDER BY su."startDate";

-- 3. KIỂM TRA VI PHẠM VÀ SHIFT MATCHING CHI TIẾT
SELECT 'Detailed violation vs shift matching' as check_type;

SELECT 
    attr."dateCreated",
    attr."dateCreated"::date as violation_date,
    attr."dateCreated"::time as violation_time,
    
    -- Tìm shift có thể match
    (SELECT COUNT(*) 
     FROM "ShiftUser" su 
     WHERE su."userId" = '62417db10f022fca95fa3e40'
       AND attr."dateCreated"::date = su."startDate"::date
    ) as exact_date_matches,
    
    (SELECT COUNT(*) 
     FROM "ShiftUser" su 
     WHERE su."userId" = '62417db10f022fca95fa3e40'
       AND attr."dateCreated"::date BETWEEN su."startDate" AND COALESCE(su."endDate", su."startDate")
    ) as date_range_matches,
    
    -- Shift gần nhất
    (SELECT su."startDate" || ' (' || su."shiftId" || ')'
     FROM "ShiftUser" su 
     WHERE su."userId" = '62417db10f022fca95fa3e40'
     ORDER BY ABS(EXTRACT(EPOCH FROM (attr."dateCreated"::date - su."startDate")))
     LIMIT 1
    ) as nearest_shift

FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated"
LIMIT 10;

-- 4. KIỂM TRA LOGIC JOIN HIỆN TẠI
SELECT 'Current JOIN logic test' as check_type;

SELECT 
    attr."dateCreated",
    su."startDate",
    su."shiftId",
    st."startHour",
    st."endHour",
    'Found match with current logic' as status
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND attr."dateCreated"::date = su."startDate"::date  -- Logic hiện tại
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)
LIMIT 5;

-- 5. THỬ LOGIC JOIN KHÁC (DATE RANGE)
SELECT 'Alternative JOIN logic test' as check_type;

SELECT 
    attr."dateCreated",
    su."startDate",
    su."endDate",
    su."shiftId",
    st."startHour",
    st."endHour",
    'Found match with date range logic' as status
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND attr."dateCreated"::date BETWEEN su."startDate" AND COALESCE(su."endDate", su."startDate")  -- Logic mới
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)
LIMIT 5;

-- 6. KIỂM TRA TẤT CẢ SHIFTUSER CỦA USER
SELECT 'All ShiftUser records for user' as check_type;

SELECT 
    su."userId",
    su."shiftId",
    su."startDate",
    su."endDate",
    st."description",
    st."startHour",
    st."endHour",
    CASE 
        WHEN su."endDate" IS NULL THEN 'Active (no end date)'
        WHEN su."endDate" >= CURRENT_DATE THEN 'Active'
        ELSE 'Expired'
    END as status
FROM "ShiftUser" su
LEFT JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE su."userId" = '62417db10f022fca95fa3e40'
ORDER BY su."startDate" DESC;
