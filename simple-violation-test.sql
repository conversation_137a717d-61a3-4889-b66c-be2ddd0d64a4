-- <PERSON><PERSON>t đơn giản để test violation counting
-- HƯỚNG DẪN SỬ DỤNG:
-- 1. Thay đổi userId và date range bên dưới
-- 2. Chạy script này trong PostgreSQL client (psql, pgAdmin, etc.)

-- ===== THAY ĐỔI CÁC GIÁ TRỊ NÀY =====
-- Thay 'your-user-id' bằng userId thực tế
-- Thay date range theo tháng bạn muốn test

WITH test_params AS (
    SELECT 
        'your-user-id' as user_id,
        '2024-12-01 00:00:00'::timestamp as start_date,
        '2024-12-31 23:59:59'::timestamp as end_date
),

-- Đồng phục: chỉ tính thứ 2
uniform_violations AS (
    SELECT DISTINCT
        attr."dateCreated"::date as violation_date
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    CROSS JOIN test_params tp
    WHERE attr."dateCreated" >= tp.start_date
        AND attr."dateCreated" <= tp.end_date
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND attr."dateCreated"::time >= shift_type."startHour"::time
        AND attr."dateCreated"::time <= shift_type."endHour"::time
        -- Loại trừ break time
        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                AND shift_type."startBreak" != '00:00' 
                AND shift_type."endBreak" != '00:00')
        -- Chỉ tính thứ 2
        AND EXTRACT(DOW FROM attr."dateCreated") = 1
        AND result."userIdReCheckResult" = tp.user_id
        AND attr."type" = 16  -- none_ppe_body

    UNION ALL

    SELECT DISTINCT
        attr."dateCreated"::date as violation_date
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    CROSS JOIN test_params tp
    WHERE attr."dateCreated" >= tp.start_date
        AND attr."dateCreated" <= tp.end_date
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND attr."dateCreated"::time >= shift_type."startHour"::time
        AND attr."dateCreated"::time <= shift_type."endHour"::time
        -- Loại trừ break time
        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                AND shift_type."startBreak" != '00:00' 
                AND shift_type."endBreak" != '00:00')
        -- Chỉ tính thứ 2
        AND EXTRACT(DOW FROM attr."dateCreated") = 1
        AND face."userId" = tp.user_id
        AND attr."type" = 16  -- none_ppe_body
        AND NOT EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackResult" r
            WHERE r."humanTrackId" = attr."trackId"
            AND r."userIdReCheckResult" IS NOT NULL
        )
),

-- Thẻ: tất cả ngày
card_violations AS (
    SELECT DISTINCT attr."trackId"
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    CROSS JOIN test_params tp
    WHERE attr."dateCreated" >= tp.start_date
        AND attr."dateCreated" <= tp.end_date
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND attr."dateCreated"::time >= shift_type."startHour"::time
        AND attr."dateCreated"::time <= shift_type."endHour"::time
        -- Loại trừ break time
        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                AND shift_type."startBreak" != '00:00' 
                AND shift_type."endBreak" != '00:00')
        AND result."userIdReCheckResult" = tp.user_id
        AND attr."type" = 18  -- no_employee_card
        -- Chỉ tính khi không có wearing_employee_card
        AND NOT EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17  -- wearing_employee_card
        )

    UNION ALL

    SELECT DISTINCT attr."trackId"
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    CROSS JOIN test_params tp
    WHERE attr."dateCreated" >= tp.start_date
        AND attr."dateCreated" <= tp.end_date
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND attr."dateCreated"::time >= shift_type."startHour"::time
        AND attr."dateCreated"::time <= shift_type."endHour"::time
        -- Loại trừ break time
        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                AND shift_type."startBreak" != '00:00' 
                AND shift_type."endBreak" != '00:00')
        AND face."userId" = tp.user_id
        AND attr."type" = 18  -- no_employee_card
        AND NOT EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17  -- wearing_employee_card
        )
        AND NOT EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackResult" r
            WHERE r."humanTrackId" = attr."trackId"
            AND r."userIdReCheckResult" IS NOT NULL
        )
)

-- KẾT QUẢ CUỐI CÙNG
SELECT 
    tp.user_id,
    tp.start_date::date as from_date,
    tp.end_date::date as to_date,
    COUNT(DISTINCT uv.violation_date) as uniform_violation_count,
    COUNT(DISTINCT cv."trackId") as card_violation_count,
    (COUNT(DISTINCT uv.violation_date) + COUNT(DISTINCT cv."trackId")) as total_violations
FROM test_params tp
LEFT JOIN uniform_violations uv ON true
LEFT JOIN card_violations cv ON true
GROUP BY tp.user_id, tp.start_date, tp.end_date;

-- THÊM CHI TIẾT DEBUG
SELECT '=== UNIFORM VIOLATIONS DETAIL ===' as info;

SELECT 
    uv.violation_date,
    EXTRACT(DOW FROM uv.violation_date) as day_of_week,
    CASE EXTRACT(DOW FROM uv.violation_date)
        WHEN 1 THEN 'Monday (COUNTED)'
        ELSE 'Not Monday (IGNORED)'
    END as day_status
FROM uniform_violations uv
ORDER BY uv.violation_date;

SELECT '=== CARD VIOLATIONS DETAIL ===' as info;

SELECT 
    cv."trackId",
    attr."dateCreated",
    EXTRACT(DOW FROM attr."dateCreated") as day_of_week,
    'All days counted' as day_status
FROM card_violations cv
JOIN "CoreAiHumanTrackAttribute" attr ON cv."trackId" = attr."trackId"
WHERE attr."type" = 18
ORDER BY attr."dateCreated"
LIMIT 10;
