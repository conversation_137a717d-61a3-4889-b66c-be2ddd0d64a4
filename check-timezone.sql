-- <PERSON><PERSON><PERSON> tra timezone của dữ liệu

-- 1. KIỂM TRA TIMEZONE CỦA DATABASE
SELECT 'Database timezone info' as test_type;

SELECT 
    'current_setting timezone' as setting_name,
    current_setting('TIMEZONE') as value
UNION ALL
SELECT 
    'now()' as setting_name,
    now()::text as value
UNION ALL
SELECT 
    'now() at time zone UTC' as setting_name,
    (now() at time zone 'UTC')::text as value
UNION ALL
SELECT 
    'now() at time zone Asia/Ho_Chi_Minh' as setting_name,
    (now() at time zone 'Asia/Ho_Chi_Minh')::text as value;

-- 2. KIỂM TRA TIMEZONE CỦA VI PHẠM
SELECT 'Violation timezone analysis' as test_type;

SELECT 
    attr."trackId",
    attr."dateCreated" as original_time,
    attr."dateCreated"::text as original_text,
    
    -- Convert to different timezones
    attr."dateCreated" AT TIME ZONE 'UTC' as utc_time,
    attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' as vietnam_time,
    
    -- Extract time components
    attr."dateCreated"::time as original_time_only,
    (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time as vietnam_time_only,
    
    -- Check if Vietnam time is within working hours
    CASE 
        WHEN (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= '08:30:00'::time
             AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= '17:30:00'::time
        THEN '✅ Within working hours (Vietnam time)'
        ELSE '❌ Outside working hours (Vietnam time)'
    END as vietnam_time_check,
    
    -- Check if original time is within working hours
    CASE 
        WHEN attr."dateCreated"::time >= '08:30:00'::time
             AND attr."dateCreated"::time <= '17:30:00'::time
        THEN '✅ Within working hours (original time)'
        ELSE '❌ Outside working hours (original time)'
    END as original_time_check

FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)
    AND attr."dateCreated"::time IN ('05:25:07.311', '10:34:02.767')
ORDER BY attr."dateCreated";

-- 3. KIỂM TRA SHIFT TYPE TIMEZONE
SELECT 'ShiftType timezone analysis' as test_type;

SELECT 
    st."code",
    st."startHour",
    st."endHour",
    st."startHour"::text as start_text,
    st."endHour"::text as end_text,
    
    -- Giả sử ShiftType là Vietnam time, convert to UTC for comparison
    (st."startHour"::time - interval '7 hours') as start_utc_equivalent,
    (st."endHour"::time - interval '7 hours') as end_utc_equivalent
    
FROM "ShiftType" st
WHERE st."code" = 'Default';

-- 4. TEST LOGIC VỚI TIMEZONE CONVERSION
SELECT 'Corrected logic with timezone' as test_type;

SELECT 
    attr."trackId",
    attr."dateCreated",
    
    -- Original logic (có thể sai timezone)
    CASE 
        WHEN attr."dateCreated"::time >= st."startHour"::time
             AND attr."dateCreated"::time <= st."endHour"::time
        THEN '✅ Original logic: Within hours'
        ELSE '❌ Original logic: Outside hours'
    END as original_logic,
    
    -- Corrected logic: Convert violation time to Vietnam timezone
    CASE 
        WHEN (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= st."startHour"::time
             AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= st."endHour"::time
        THEN '✅ Corrected logic: Within hours'
        ELSE '❌ Corrected logic: Outside hours'
    END as corrected_logic,
    
    -- Show times for comparison
    attr."dateCreated"::time as violation_time_original,
    (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time as violation_time_vietnam,
    st."startHour" as shift_start,
    st."endHour" as shift_end

FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND attr."dateCreated"::date = su."startDate"::date
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated";

-- 5. COUNT VIOLATIONS WITH TIMEZONE CORRECTION
SELECT 'Violation count with timezone correction' as test_type;

SELECT 
    'Original logic count' as method,
    COUNT(*) as violation_count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND attr."dateCreated"::date = su."startDate"::date
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND attr."dateCreated"::time >= st."startHour"::time
    AND attr."dateCreated"::time <= st."endHour"::time
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)

UNION ALL

SELECT 
    'Timezone corrected count' as method,
    COUNT(*) as violation_count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::date = su."startDate"::date
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= st."startHour"::time
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= st."endHour"::time
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18);
