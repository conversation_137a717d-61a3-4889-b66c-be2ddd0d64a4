-- <PERSON><PERSON><PERSON> để test violation counting logic trực tiếp trên <PERSON>
-- Thay đổi các biến này theo nhu cầu test của bạn

-- ===== VARIABLES - THAY ĐỔI THEO NHU CẦU =====
-- Thay 'your-user-id' bằng userId thực tế bạn muốn test
\set userId '''test-user-id'''

-- Thay đổi tháng/năm muốn test (format: YYYY-MM-DD)
\set startDate '''2024-12-01 00:00:00'''
\set endDate '''2024-12-31 23:59:59'''

-- ===== MAIN QUERY =====
SELECT
    -- Đồng phục: đếm số ngày vi phạm (chỉ thứ 2)
    (SELECT COUNT(DISTINCT violation_date)
     FROM (
        SELECT DISTINCT
            attr."dateCreated"::date as violation_date
        FROM "CoreAiHumanTrackAttribute" attr
        INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
        INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
        INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
        WHERE attr."dateCreated" >= :startDate::timestamp
            AND attr."dateCreated" <= :endDate::timestamp
            AND attr."dateCreated"::date = shift_user."startDate"::date
            AND attr."dateCreated"::time >= shift_type."startHour"::time
            AND attr."dateCreated"::time <= shift_type."endHour"::time
            -- Loại trừ thời gian nghỉ giải lao
            AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                    AND attr."dateCreated"::time <= shift_type."endBreak"::time
                    AND shift_type."startBreak" != '00:00' 
                    AND shift_type."endBreak" != '00:00')
            -- Vi phạm đồng phục chỉ tính vào thứ 2 (1 = Monday trong PostgreSQL)
            AND EXTRACT(DOW FROM attr."dateCreated") = 1
            AND result."userIdReCheckResult" = :userId
            AND attr."type" = 16  -- none_ppe_body

        UNION ALL

        SELECT DISTINCT
            attr."dateCreated"::date as violation_date
        FROM "CoreAiHumanTrackAttribute" attr
        INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
        INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
        INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
        WHERE attr."dateCreated" >= :startDate::timestamp
            AND attr."dateCreated" <= :endDate::timestamp
            AND attr."dateCreated"::date = shift_user."startDate"::date
            AND attr."dateCreated"::time >= shift_type."startHour"::time
            AND attr."dateCreated"::time <= shift_type."endHour"::time
            -- Loại trừ thời gian nghỉ giải lao
            AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                    AND attr."dateCreated"::time <= shift_type."endBreak"::time
                    AND shift_type."startBreak" != '00:00' 
                    AND shift_type."endBreak" != '00:00')
            -- Vi phạm đồng phục chỉ tính vào thứ 2 (1 = Monday trong PostgreSQL)
            AND EXTRACT(DOW FROM attr."dateCreated") = 1
            AND face."userId" = :userId
            AND attr."type" = 16  -- none_ppe_body
            AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackResult" r
                WHERE r."humanTrackId" = attr."trackId"
                AND r."userIdReCheckResult" IS NOT NULL
            )
     ) uniform_days
    ) as uniform_violation_count,

    -- Thẻ: đếm tất cả lần vi phạm (tất cả ngày)
    (SELECT COUNT(*)
     FROM (
        SELECT DISTINCT attr."trackId"
        FROM "CoreAiHumanTrackAttribute" attr
        INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
        INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
        INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
        WHERE attr."dateCreated" >= :startDate::timestamp
            AND attr."dateCreated" <= :endDate::timestamp
            AND attr."dateCreated"::date = shift_user."startDate"::date
            AND attr."dateCreated"::time >= shift_type."startHour"::time
            AND attr."dateCreated"::time <= shift_type."endHour"::time
            -- Loại trừ thời gian nghỉ giải lao
            AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                    AND attr."dateCreated"::time <= shift_type."endBreak"::time
                    AND shift_type."startBreak" != '00:00' 
                    AND shift_type."endBreak" != '00:00')
            AND result."userIdReCheckResult" = :userId
            AND attr."type" = 18  -- no_employee_card
            -- Chỉ tính khi không có wearing_employee_card trong cùng track
            AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                WHERE attr2."trackId" = attr."trackId"
                AND attr2."type" = 17  -- wearing_employee_card
            )

        UNION ALL

        SELECT DISTINCT attr."trackId"
        FROM "CoreAiHumanTrackAttribute" attr
        INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
        INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
        INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
        WHERE attr."dateCreated" >= :startDate::timestamp
            AND attr."dateCreated" <= :endDate::timestamp
            AND attr."dateCreated"::date = shift_user."startDate"::date
            AND attr."dateCreated"::time >= shift_type."startHour"::time
            AND attr."dateCreated"::time <= shift_type."endHour"::time
            -- Loại trừ thời gian nghỉ giải lao
            AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                    AND attr."dateCreated"::time <= shift_type."endBreak"::time
                    AND shift_type."startBreak" != '00:00' 
                    AND shift_type."endBreak" != '00:00')
            AND face."userId" = :userId
            AND attr."type" = 18  -- no_employee_card
            AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                WHERE attr2."trackId" = attr."trackId"
                AND attr2."type" = 17  -- wearing_employee_card
            )
            AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackResult" r
                WHERE r."humanTrackId" = attr."trackId"
                AND r."userIdReCheckResult" IS NOT NULL
            )
     ) card_violations
    ) as card_violation_count;

-- ===== ADDITIONAL DEBUG QUERIES =====

-- 1. Kiểm tra dữ liệu ShiftType
\echo '=== SHIFT TYPE DATA ==='
SELECT 
    code, 
    description, 
    "startHour", 
    "endHour", 
    "startBreak", 
    "endBreak"
FROM "ShiftType" 
LIMIT 5;

-- 2. Kiểm tra dữ liệu ShiftUser cho user test
\echo '=== SHIFT USER DATA ==='
SELECT 
    "userId", 
    "shiftId", 
    "startDate"::date,
    "endDate"::date
FROM "ShiftUser" 
WHERE "userId" = :userId
AND "startDate" >= :startDate::date
AND "startDate" <= :endDate::date
ORDER BY "startDate"
LIMIT 10;

-- 3. Kiểm tra vi phạm đồng phục theo ngày trong tuần
\echo '=== UNIFORM VIOLATIONS BY DAY OF WEEK ==='
SELECT 
    EXTRACT(DOW FROM attr."dateCreated") as day_of_week,
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'Sunday'
        WHEN 1 THEN 'Monday'
        WHEN 2 THEN 'Tuesday'
        WHEN 3 THEN 'Wednesday'
        WHEN 4 THEN 'Thursday'
        WHEN 5 THEN 'Friday'
        WHEN 6 THEN 'Saturday'
    END as day_name,
    COUNT(*) as violation_count,
    COUNT(DISTINCT attr."dateCreated"::date) as violation_days
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= :startDate::timestamp
    AND attr."dateCreated" <= :endDate::timestamp
    AND result."userIdReCheckResult" = :userId
    AND attr."type" = 16  -- none_ppe_body
GROUP BY EXTRACT(DOW FROM attr."dateCreated")
ORDER BY day_of_week;

-- 4. Kiểm tra vi phạm thẻ với wearing_card logic
\echo '=== CARD VIOLATIONS WITH WEARING_CARD CHECK ==='
SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."type",
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17
        ) THEN 'HAS_WEARING_CARD'
        ELSE 'NO_WEARING_CARD'
    END as wearing_card_status,
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17
        ) THEN 'COUNTED'
        ELSE 'IGNORED'
    END as count_status
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= :startDate::timestamp
    AND attr."dateCreated" <= :endDate::timestamp
    AND result."userIdReCheckResult" = :userId
    AND attr."type" = 18  -- no_employee_card
ORDER BY attr."dateCreated"
LIMIT 20;

-- 5. Kiểm tra break time exclusion
\echo '=== BREAK TIME EXCLUSION CHECK ==='
SELECT 
    attr."dateCreated",
    attr."dateCreated"::time as violation_time,
    shift_type."startBreak"::time as break_start,
    shift_type."endBreak"::time as break_end,
    CASE 
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
             AND shift_type."startBreak" != '00:00' 
             AND shift_type."endBreak" != '00:00'
        THEN 'EXCLUDED (break time)'
        ELSE 'COUNTED (working time)'
    END as break_status
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= :startDate::timestamp
    AND attr."dateCreated" <= :endDate::timestamp
    AND result."userIdReCheckResult" = :userId
    AND attr."type" IN (16, 18)  -- uniform or card violations
ORDER BY attr."dateCreated"
LIMIT 20;
