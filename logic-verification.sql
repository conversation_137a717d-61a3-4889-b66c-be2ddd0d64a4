-- <PERSON><PERSON><PERSON> để verify logic cụ thể của violation counting
-- <PERSON><PERSON><PERSON> tra từng rule một cách chi tiết

-- ===== THAY ĐỔI THÔNG TIN TEST =====
\set userId '''your-user-id'''
\set startDate '''2024-12-01'''
\set endDate '''2024-12-31'''

-- 1. KIỂM TRA LOGIC MONDAY-ONLY CHO UNIFORM
SELECT '=== 1. MONDAY-ONLY UNIFORM LOGIC ===' as test_section;

SELECT 
    attr."dateCreated"::date as violation_date,
    EXTRACT(DOW FROM attr."dateCreated") as day_of_week,
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'Sunday'
        WHEN 1 THEN 'Monday ✅'
        WHEN 2 THEN 'Tuesday ❌'
        WHEN 3 THEN 'Wednesday ❌'
        WHEN 4 THEN 'Thursday ❌'
        WHEN 5 THEN 'Friday ❌'
        WHEN 6 THEN 'Saturday ❌'
    END as day_name,
    COUNT(*) as violation_count,
    CASE 
        WHEN EXTRACT(DOW FROM attr."dateCreated") = 1 THEN 'COUNTED'
        ELSE 'IGNORED'
    END as count_status
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND result."userIdReCheckResult" = :userId
    AND attr."type" = 16  -- none_ppe_body
GROUP BY attr."dateCreated"::date, EXTRACT(DOW FROM attr."dateCreated")
ORDER BY attr."dateCreated"::date;

-- 2. KIỂM TRA LOGIC CARD VIOLATIONS VỚI WEARING_CARD
SELECT '=== 2. CARD VIOLATION LOGIC ===' as test_section;

SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."type",
    CASE attr."type"
        WHEN 17 THEN 'wearing_employee_card'
        WHEN 18 THEN 'no_employee_card'
        ELSE 'other'
    END as type_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17
        ) THEN 'HAS_WEARING_CARD'
        ELSE 'NO_WEARING_CARD'
    END as wearing_card_status,
    CASE 
        WHEN attr."type" = 18 AND NOT EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17
        ) THEN 'COUNTED ✅'
        WHEN attr."type" = 18 THEN 'IGNORED ❌ (has wearing card)'
        ELSE 'NOT_CARD_VIOLATION'
    END as count_status
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND result."userIdReCheckResult" = :userId
    AND attr."type" IN (17, 18)  -- card related
ORDER BY attr."trackId", attr."type";

-- 3. KIỂM TRA BREAK TIME EXCLUSION
SELECT '=== 3. BREAK TIME EXCLUSION ===' as test_section;

SELECT 
    attr."dateCreated",
    attr."dateCreated"::time as violation_time,
    shift_type."startHour"::time as work_start,
    shift_type."endHour"::time as work_end,
    shift_type."startBreak"::time as break_start,
    shift_type."endBreak"::time as break_end,
    CASE 
        WHEN attr."dateCreated"::time < shift_type."startHour"::time 
             OR attr."dateCreated"::time > shift_type."endHour"::time
        THEN 'OUTSIDE_WORK_HOURS ❌'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
             AND shift_type."startBreak" != '00:00' 
             AND shift_type."endBreak" != '00:00'
        THEN 'BREAK_TIME ❌'
        ELSE 'WORKING_TIME ✅'
    END as time_status
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = :userId
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated"
LIMIT 20;

-- 4. KIỂM TRA SHIFT MATCHING
SELECT '=== 4. SHIFT MATCHING ===' as test_section;

SELECT 
    attr."dateCreated"::date as violation_date,
    shift_user."startDate"::date as shift_date,
    shift_user."shiftId",
    shift_type."startHour",
    shift_type."endHour",
    CASE 
        WHEN attr."dateCreated"::date = shift_user."startDate"::date 
        THEN 'MATCHED ✅'
        ELSE 'NOT_MATCHED ❌'
    END as date_match_status
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND result."userIdReCheckResult" = :userId
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated"
LIMIT 10;

-- 5. FINAL COUNT VERIFICATION
SELECT '=== 5. FINAL COUNT VERIFICATION ===' as test_section;

-- Đếm uniform (chỉ thứ 2)
WITH uniform_mondays AS (
    SELECT DISTINCT attr."dateCreated"::date as violation_date
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    WHERE attr."dateCreated" >= :startDate::date
        AND attr."dateCreated" <= :endDate::date + interval '1 day'
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND attr."dateCreated"::time >= shift_type."startHour"::time
        AND attr."dateCreated"::time <= shift_type."endHour"::time
        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                AND shift_type."startBreak" != '00:00' 
                AND shift_type."endBreak" != '00:00')
        AND EXTRACT(DOW FROM attr."dateCreated") = 1  -- Monday only
        AND result."userIdReCheckResult" = :userId
        AND attr."type" = 16
),

-- Đếm card (tất cả ngày)
card_violations AS (
    SELECT DISTINCT attr."trackId"
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    WHERE attr."dateCreated" >= :startDate::date
        AND attr."dateCreated" <= :endDate::date + interval '1 day'
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND attr."dateCreated"::time >= shift_type."startHour"::time
        AND attr."dateCreated"::time <= shift_type."endHour"::time
        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                AND shift_type."startBreak" != '00:00' 
                AND shift_type."endBreak" != '00:00')
        AND result."userIdReCheckResult" = :userId
        AND attr."type" = 18
        AND NOT EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17
        )
)

SELECT 
    :userId as user_id,
    (SELECT COUNT(*) FROM uniform_mondays) as uniform_violation_count,
    (SELECT COUNT(*) FROM card_violations) as card_violation_count,
    (SELECT COUNT(*) FROM uniform_mondays) + (SELECT COUNT(*) FROM card_violations) as total_violations;

-- 6. KIỂM TRA DỮ LIỆU CƠ BẢN
SELECT '=== 6. BASIC DATA CHECK ===' as test_section;

SELECT 
    'Total attributes in period' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'

UNION ALL

SELECT 
    'Uniform violations (type 16)' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND attr."type" = 16

UNION ALL

SELECT 
    'Card violations (type 18)' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND attr."type" = 18

UNION ALL

SELECT 
    'Wearing card (type 17)' as metric,
    COUNT(*) as count
FROM "CoreAiHumanTrackAttribute" attr
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND attr."type" = 17;
