-- <PERSON><PERSON><PERSON> kiểm tra dữ liệu break time trong ShiftType
-- Thay userId và date range

-- 1. KIỂM TRA DỮ LIỆU SHIFTTYPE
SELECT 
    code,
    description,
    "startHour",
    "endHour", 
    "startBreak",
    "endBreak",
    CASE 
        WHEN "startBreak" = '00:00:00' OR "endBreak" = '00:00:00' 
        THEN 'KHÔNG CÓ BREAK TIME'
        ELSE 'CÓ BREAK TIME'
    END as break_status
FROM "ShiftType"
ORDER BY code;

-- 2. KIỂM TRA VI PHẠM TRONG BREAK TIME
WITH test_params AS (
    SELECT 
        '03c5c8cc-3750-4845-ba67-67a95743993c' as user_id,
        '2024-12-01'::date as start_date,
        '2024-12-31'::date as end_date
)
SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::time as violation_time,
    shift_type."startBreak"::time as break_start,
    shift_type."endBreak"::time as break_end,
    shift_type."startHour"::time as work_start,
    shift_type."endHour"::time as work_end,
    
    -- <PERSON><PERSON><PERSON> tra điều kiện break time
    CASE 
        WHEN shift_type."startBreak" = '00:00' AND shift_type."endBreak" = '00:00'
        THEN 'KHÔNG CÓ BREAK TIME'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
        THEN 'TRONG BREAK TIME ❌ (nên bị loại trừ)'
        ELSE 'NGOÀI BREAK TIME ✅ (được tính)'
    END as break_check,
    
    -- Logic cuối cùng trong code (đã sửa theo format database)
    CASE
        WHEN NOT (attr."dateCreated"::time >= shift_type."startBreak"::time
                 AND attr."dateCreated"::time <= shift_type."endBreak"::time
                 AND shift_type."startBreak" IS NOT NULL
                 AND shift_type."endBreak" IS NOT NULL
                 AND shift_type."startBreak"::time > '00:00:00'::time
                 AND shift_type."endBreak"::time > '00:00:00'::time
                 AND shift_type."startBreak" != shift_type."endBreak")
        THEN 'ĐƯỢC TÍNH ✅'
        ELSE 'BỊ LOẠI TRỪ ❌'
    END as final_logic_result,
    
    CASE attr."type"
        WHEN 16 THEN 'Đồng phục'
        WHEN 18 THEN 'Thẻ'
        ELSE 'Khác'
    END as violation_type
    
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
CROSS JOIN test_params tp
WHERE attr."dateCreated" >= tp.start_date
    AND attr."dateCreated" <= tp.end_date + interval '1 day'
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = tp.user_id
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated"
LIMIT 20;

-- 3. KIỂM TRA FORMAT CỦA BREAK TIME
SELECT 
    'startBreak format check' as check_type,
    "startBreak",
    "startBreak"::time,
    CASE 
        WHEN "startBreak" = '00:00' THEN 'Format 00:00'
        WHEN "startBreak" = '00:00:00' THEN 'Format 00:00:00'
        ELSE 'Format khác: ' || "startBreak"::text
    END as format_info
FROM "ShiftType"
WHERE "startBreak" IS NOT NULL
GROUP BY "startBreak"

UNION ALL

SELECT 
    'endBreak format check' as check_type,
    "endBreak",
    "endBreak"::time,
    CASE 
        WHEN "endBreak" = '00:00' THEN 'Format 00:00'
        WHEN "endBreak" = '00:00:00' THEN 'Format 00:00:00'
        ELSE 'Format khác: ' || "endBreak"::text
    END as format_info
FROM "ShiftType"
WHERE "endBreak" IS NOT NULL
GROUP BY "endBreak";
