# Tính năng đếm vi phạm đeo thẻ và đồng phục

## Tổng quan
Đã thêm thành công tính năng đếm số lượt vi phạm đeo thẻ và đồng phục của người dùng vào hệ thống export work month.

## Các thay đổi đã thực hiện

### 1. Database Schema (prisma/schema.prisma)
Thêm 3 trường mới vào model `CalculatedWorkMonth`:
```prisma
uniformViolationCount Int @default(0)  // Số lượt vi phạm đồng phục
cardViolationCount    Int @default(0)  // Số lượt vi phạm thẻ  
totalViolationCount   Int @default(0)  // Tổng số lượt vi phạm
```

### 2. GraphQL Schema
Cập nhật `CalculatedWorkMonthGql` với 3 trường mới:
```typescript
@Field(type => Number, { nullable: false })
uniformViolationCount: number;

@Field(type => Number, { nullable: false })
cardViolationCount: number;

@Field(type => Number, { nullable: false })
totalViolationCount: number;
```

### 3. Violation Count Service
Tạo service mới `ViolationCountService` với 2 method chính:

#### `countViolationsByUserAndMonth(userId, year, month)`
- Đếm vi phạm cho 1 user trong tháng
- Trả về object với `uniformViolationCount`, `cardViolationCount`, `totalViolationCount`

#### `countViolationsByUsersAndMonth(userIds, year, month)`  
- Đếm vi phạm cho nhiều users trong tháng
- Trả về Map với key là userId và value là violation counts

### 4. Logic đếm vi phạm
- **Vi phạm đồng phục**: Track có `PersonMctAttributeEnum.none_ppe_body` (16) mà không có `PersonMctAttributeEnum.wearing_ppe_body` (15)
- **Vi phạm thẻ**: Track có `PersonMctAttributeEnum.no_employee_card` (18) mà không có `PersonMctAttributeEnum.wearing_employee_card` (17)

### 5. Tích hợp vào Export
Cập nhật 2 service export:

#### `CalculatedWorkMonthService.exportWorkRange()`
- Thêm 3 cột mới: "Vi phạm đồng phục", "Vi phạm thẻ", "Tổng số lần vi phạm"
- Lấy violation count cho tất cả users trong khoảng thời gian

#### `ShiftUserService.exportWorkMonthNew()`
- Thêm 3 cột mới vào Excel export
- Hiển thị số lượt vi phạm cho từng user

### 6. Module Configuration
- Tạo `ViolationCountModule` và export service
- Import vào `CalculatedWorkMonthModule` và `ShiftUserModule`
- Thêm vào `AppModule`

## Cách sử dụng

### 1. API GraphQL
Query `getCalculatedWorkMonth` sẽ trả về thêm thông tin vi phạm:
```graphql
query {
  getCalculatedWorkMonth(userId: "user_id", month: "2024-12-01") {
    uniformViolationCount
    cardViolationCount  
    totalViolationCount
    # ... các trường khác
  }
}
```

### 2. Export Excel
Khi export work month, file Excel sẽ có thêm 3 cột:
- **Vi phạm đồng phục**: Số lượt không đeo đồng phục
- **Vi phạm thẻ**: Số lượt không đeo thẻ
- **Tổng số lần vi phạm**: Tổng cộng

### 3. API Endpoints
- `POST /api/calculated-work-month/export-by-range` - Export với violation count
- `POST /api/shift-user/exportWorkMonth` - Export work month với violation count

## Testing
Đã tạo unit test cho `ViolationCountService` để đảm bảo logic đếm vi phạm hoạt động đúng.

## Migration
Cần chạy migration để thêm các trường mới vào database:
```sql
ALTER TABLE "CalculatedWorkMonth" ADD COLUMN "uniformViolationCount" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "CalculatedWorkMonth" ADD COLUMN "cardViolationCount" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "CalculatedWorkMonth" ADD COLUMN "totalViolationCount" INTEGER NOT NULL DEFAULT 0;
```

## Lưu ý
- Violation count được tính dựa trên dữ liệu AI tracking từ bảng `CoreAiHumanTrackAttribute`
- Chỉ đếm các track có user được xác định (qua face recognition hoặc manual check)
- Mỗi track chỉ được đếm 1 lần cho mỗi loại vi phạm
- Dữ liệu được cache và tính toán real-time khi export

## Sửa lỗi đã thực hiện

### Lỗi: `column CoreAiHumanTrackResult.trackId does not exist`
**Nguyên nhân**: Trong database schema, bảng `CoreAiHumanTrackResult` sử dụng trường `humanTrackId` thay vì `trackId`.

**Giải pháp**:
1. Cập nhật JOIN clause từ:
   ```sql
   LEFT JOIN "CoreAiHumanTrackResult" ON "CoreAiHumanTrackAttribute"."trackId" = "CoreAiHumanTrackResult"."trackId"
   ```
   Thành:
   ```sql
   LEFT JOIN "CoreAiHumanTrackResult" ON "CoreAiHumanTrackAttribute"."trackId" = "CoreAiHumanTrackResult"."humanTrackId"
   ```

2. Thêm JOIN với bảng `FaceResult` để lấy thêm thông tin user:
   ```sql
   LEFT JOIN "FaceResult" ON "CoreAiHumanTrackAttribute"."trackId" = "FaceResult"."humanTrackId"
   ```

3. Sử dụng `COALESCE` để lấy user ID từ cả hai nguồn:
   ```sql
   COALESCE("CoreAiHumanTrackResult"."userIdReCheckResult", "FaceResult"."userId") as user_id
   ```

### Kết quả
- Query SQL đã chạy thành công
- Service có thể đếm vi phạm từ cả manual recheck và face recognition
- Tính năng sẵn sàng để sử dụng
