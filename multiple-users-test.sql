-- Script để test violation counting cho nhiều users
-- HƯỚNG DẪN SỬ DỤNG:
-- 1. Thay đổi danh sách userIds và date range bên dưới
-- 2. <PERSON><PERSON>y script này trong PostgreSQL client

-- ===== THAY ĐỔI CÁC GIÁ TRỊ NÀY =====
WITH test_params AS (
    SELECT 
        ARRAY['user1', 'user2', 'user3'] as user_ids,  -- Thay bằng userIds thực tế
        '2024-12-01 00:00:00'::timestamp as start_date,
        '2024-12-31 23:59:59'::timestamp as end_date
),

-- Đồng phục: chỉ tính thứ 2
user_uniform_violations AS (
    SELECT 
        user_id,
        COUNT(DISTINCT violation_date) as uniform_count
    FROM (
        SELECT DISTINCT
            result."userIdReCheckResult" as user_id,
            attr."dateCreated"::date as violation_date
        FROM "CoreAiHumanTrackAttribute" attr
        INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
        INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
        INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
        CROSS JOIN test_params tp
        WHERE attr."dateCreated" >= tp.start_date
            AND attr."dateCreated" <= tp.end_date
            AND attr."dateCreated"::date = shift_user."startDate"::date
            AND attr."dateCreated"::time >= shift_type."startHour"::time
            AND attr."dateCreated"::time <= shift_type."endHour"::time
            -- Loại trừ break time
            AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                    AND attr."dateCreated"::time <= shift_type."endBreak"::time
                    AND shift_type."startBreak" != '00:00' 
                    AND shift_type."endBreak" != '00:00')
            -- Chỉ tính thứ 2
            AND EXTRACT(DOW FROM attr."dateCreated") = 1
            AND result."userIdReCheckResult" = ANY(tp.user_ids)
            AND attr."type" = 16  -- none_ppe_body

        UNION ALL

        SELECT DISTINCT
            face."userId" as user_id,
            attr."dateCreated"::date as violation_date
        FROM "CoreAiHumanTrackAttribute" attr
        INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
        INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
        INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
        CROSS JOIN test_params tp
        WHERE attr."dateCreated" >= tp.start_date
            AND attr."dateCreated" <= tp.end_date
            AND attr."dateCreated"::date = shift_user."startDate"::date
            AND attr."dateCreated"::time >= shift_type."startHour"::time
            AND attr."dateCreated"::time <= shift_type."endHour"::time
            -- Loại trừ break time
            AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                    AND attr."dateCreated"::time <= shift_type."endBreak"::time
                    AND shift_type."startBreak" != '00:00' 
                    AND shift_type."endBreak" != '00:00')
            -- Chỉ tính thứ 2
            AND EXTRACT(DOW FROM attr."dateCreated") = 1
            AND face."userId" = ANY(tp.user_ids)
            AND attr."type" = 16  -- none_ppe_body
            AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackResult" r
                WHERE r."humanTrackId" = attr."trackId"
                AND r."userIdReCheckResult" IS NOT NULL
            )
    ) uniform_violations
    GROUP BY user_id
),

-- Thẻ: tất cả ngày
user_card_violations AS (
    SELECT 
        user_id,
        COUNT(*) as card_count
    FROM (
        SELECT DISTINCT
            result."userIdReCheckResult" as user_id,
            attr."trackId"
        FROM "CoreAiHumanTrackAttribute" attr
        INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
        INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
        INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
        CROSS JOIN test_params tp
        WHERE attr."dateCreated" >= tp.start_date
            AND attr."dateCreated" <= tp.end_date
            AND attr."dateCreated"::date = shift_user."startDate"::date
            AND attr."dateCreated"::time >= shift_type."startHour"::time
            AND attr."dateCreated"::time <= shift_type."endHour"::time
            -- Loại trừ break time
            AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                    AND attr."dateCreated"::time <= shift_type."endBreak"::time
                    AND shift_type."startBreak" != '00:00' 
                    AND shift_type."endBreak" != '00:00')
            AND result."userIdReCheckResult" = ANY(tp.user_ids)
            AND attr."type" = 18  -- no_employee_card
            AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                WHERE attr2."trackId" = attr."trackId"
                AND attr2."type" = 17  -- wearing_employee_card
            )

        UNION ALL

        SELECT DISTINCT
            face."userId" as user_id,
            attr."trackId"
        FROM "CoreAiHumanTrackAttribute" attr
        INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
        INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
        INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
        CROSS JOIN test_params tp
        WHERE attr."dateCreated" >= tp.start_date
            AND attr."dateCreated" <= tp.end_date
            AND attr."dateCreated"::date = shift_user."startDate"::date
            AND attr."dateCreated"::time >= shift_type."startHour"::time
            AND attr."dateCreated"::time <= shift_type."endHour"::time
            -- Loại trừ break time
            AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                    AND attr."dateCreated"::time <= shift_type."endBreak"::time
                    AND shift_type."startBreak" != '00:00' 
                    AND shift_type."endBreak" != '00:00')
            AND face."userId" = ANY(tp.user_ids)
            AND attr."type" = 18  -- no_employee_card
            AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                WHERE attr2."trackId" = attr."trackId"
                AND attr2."type" = 17  -- wearing_employee_card
            )
            AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackResult" r
                WHERE r."humanTrackId" = attr."trackId"
                AND r."userIdReCheckResult" IS NOT NULL
            )
    ) card_violations
    GROUP BY user_id
)

-- KẾT QUẢ CHO TẤT CẢ USERS
SELECT 
    u.user_id,
    COALESCE(uu.uniform_count, 0) as uniform_violation_count,
    COALESCE(uc.card_count, 0) as card_violation_count,
    (COALESCE(uu.uniform_count, 0) + COALESCE(uc.card_count, 0)) as total_violations
FROM (
    SELECT DISTINCT unnest(tp.user_ids) as user_id
    FROM test_params tp
) u
LEFT JOIN user_uniform_violations uu ON u.user_id = uu.user_id
LEFT JOIN user_card_violations uc ON u.user_id = uc.user_id
ORDER BY u.user_id;

-- TỔNG HỢP THỐNG KÊ
SELECT '=== SUMMARY STATISTICS ===' as info;

SELECT 
    COUNT(*) as total_users_tested,
    SUM(COALESCE(uu.uniform_count, 0)) as total_uniform_violations,
    SUM(COALESCE(uc.card_count, 0)) as total_card_violations,
    AVG(COALESCE(uu.uniform_count, 0)) as avg_uniform_per_user,
    AVG(COALESCE(uc.card_count, 0)) as avg_card_per_user
FROM (
    SELECT DISTINCT unnest(tp.user_ids) as user_id
    FROM test_params tp
) u
LEFT JOIN user_uniform_violations uu ON u.user_id = uu.user_id
LEFT JOIN user_card_violations uc ON u.user_id = uc.user_id;

-- KIỂM TRA DỮ LIỆU CÓ SẴN
SELECT '=== DATA AVAILABILITY CHECK ===' as info;

SELECT 
    'CoreAiHumanTrackAttribute' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN "type" = 16 THEN 1 END) as uniform_records,
    COUNT(CASE WHEN "type" = 18 THEN 1 END) as card_records,
    MIN("dateCreated") as earliest_date,
    MAX("dateCreated") as latest_date
FROM "CoreAiHumanTrackAttribute"
CROSS JOIN test_params tp
WHERE "dateCreated" >= tp.start_date
    AND "dateCreated" <= tp.end_date
    AND "type" IN (16, 18);

SELECT 
    'ShiftUser' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT "userId") as unique_users,
    MIN("startDate") as earliest_shift,
    MAX("startDate") as latest_shift
FROM "ShiftUser"
CROSS JOIN test_params tp
WHERE "startDate" >= tp.start_date::date
    AND "startDate" <= tp.end_date::date;
