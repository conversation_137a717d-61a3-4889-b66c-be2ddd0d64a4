// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  // previewFeatures = ["fullTextSearch"]
}

//Enums

enum RoleType {
  SUPER_ADMIN
  ADMIN
  DEPARTMENT_ADMIN
  RECEPTION
  EMPLOYEE
  GUEST
  ATTANDANT_ADMIN
  HR
  ORG_ADMIN
  DEVICE_ADMIN
  I_PARKING_VIEWER
  I_PARKING_ADMIN
}

enum FunctionId {
  LOGIN
  ROLE
  USER_ROLE
  USER_OWNER
  USER_EXPORT
  DEVICES
  DEVICE_CAMERA
  AI_SERVICE
  CAMERA
  CAMERA_AI_SERVICE
  COMPANY
  COMPANY_OWNER
  DEPARTMENT
  DEPARTMENT_ADMIN
  DEVICE_DEPARTMENT
  DEVICE_USERS
  DEVICE_GUEST
  DEVICE_SERVICES
  DEVICE_STREAMS
  SHIFTS
  TIMEKEEPING
  USERS
  USER_APPROVE
  USER_GUESTS
  SERVER_PROFILE
  SERVER_CONFIG
  EKYC_RESULT
  USER_REPORT
  AC_DEVICES
  AC_LOCATIONS
  AC_DEPARTMENT_TIMES
  AC_USER_TIMES
  AC_TIMES
  AC_QR_CODE
  AC_ADMINS
  ATTANDANT_APROVE
  ATTANDANT_EXPLAIN
  PATIENT
  PATIENT_HOME_MATE
  SHIFT_TYPE
  SHIFT_VIN_MEC
  SHIFT_USER
  PERMISSION
  FACE_RESULT
  SHIFT_EXPLANATION
  SYNC_SHIFT_USER
  CIVAMS_CLUSTER
  USER_IMPORT
  FACE_IMAGE_QUALITY
  USER_MANIPULATE
  DASHBOARD
  NOTIFICATION
  USER_ATTANDANT_TIME
  DEVICE_ATTANDANT_TIME
  ALEART_RULE
  ALEART_USER
  ALEART_DEVICE
  FACE_MATCHING_SERVICE
  SEARCH_FEATURE_IMAGE_SERVICE
  ID_CARD
  STUDENT
  NX_SYSTEM
  NX_USER
  NX_SERVER
  NX_DEVICE
  OTT
  WPF_DEVICE
  ORGANIZATION
  ORGANIZATION_USER
  ORGANIZATION_REPORT
  DEVICE_CONFIG
  FAKE_FACE_RESULT
  REMOTE_FACE_RESULT
  REMOTE_ADMIN
  REMOTE_USER
  REMOTE_APPROVE
  USER_FEEDBACK
  CORE_AI_ALRAM_EVENT
  SHIFT_USER_REPORT
  USER_OTP
  INTEGRATION
  INTEGRATION_LOG
  FINGER_PRINT
  DEVICE_ADMIN
  ORPHAN_RESULTS
  VEHICLE_TRACKING
  VEHICLE_SEARCH
  INTEGRATION_API_KEY
  SHIFT_USER_ADMIN_JOB
  I_PARKING_ADMIN
  I_PARKING_VIEWER
  ATT
  WEB_SUPPORT
  AC_CAMERA
  AC_GUEST
  S3_BUCKET
  S3_OBJECT
  S3_SMART_SEARCH
  FACE_SEARCH_REQUEST
  FACE_SEARCH_RESULT
  DEVICE_GROUP_INFO
  DEVICE_GROUP
  ACCESS_CONTROL
  ACCESS_CONTROL_LOG
  EDGE_DEVICE_CACHE
  SECURITY
  DUPLICATE_RESULT_TIME_FILTER
  LOCATION
  DEVICE_LOCATION
  VIDEO_ANALYTICS
}

enum ActiveStatus {
  ACTIVE
  DEACTIVE
  WAITTING_FOR_APPROVE
}

enum PatientStatus {
  ACTIVE
  DEACTIVE
  WAITTING_FOR_APPROVE
}

enum DeviceUpdateStatus {
  FAIL
  SUCCESS
  UPDATING
}

enum ShiftExplanationStatus {
  CREATED
  EDITED
  ACCEPTED
  REJECTED
  REMOVED
}

enum DevicePersonUpdateAction {
  ADD
  UPDATE
  DELETE
}

enum DevicePersonUpdateStatus {
  FAIL
  SUCCESS
  UPDATING
  PENDING
}

enum FaceImageType {
  Top
  Center
  Left
  Right
  FlippedTop
  FlippedCenter
  FlippedLeft
  FlippedRight
  TopLeft
  TopRight
  FlippedTopLeft
  FlippedTopRight
}

enum ServerConfigKey {
  SERVER_UINIQUE_ID
  SERVER_CHANNEL_ID
  DEFAULT_DB_ID
  MQTT_TOPICS_REGISTERFLOW
  TZ
  TIME_LOCALE
  SERVER_URL_TYPE
  SERVER_IP
  SERVER_PORT
  OCR_SERVER_URL
  BROKER_ADDRESS
  FACE_BROKER_ADDRESS
  PERSON_BROKER_ADDRESS
  IPARKING_BROKER_ADDRESS
  BROKER_IS_TLS_ENABLE
  BROKER_IS_USER_REQUIRED
  BROKER_DEBUG
  BROKER_DEBUG_TOPIC
  BROKER_DEBUG_MESS
  BROKER_MATCHER_RESULTS
  BROKER_TOPIC_RESULTS
  BROKER_TOPIC_RESULTS_EDITED
  BROKER_TOPIC_DEVICE_REGISTRATION
  BROKER_TOPIC_DEVICE_FACETERMINAL_STATUS
  BROKER_TOPIC_DEVICE_DB_ACK
  BROKER_TOPIC_DEVICE_REBOOT
  BROKER_TOPIC_DEVICE_CONFIG_REQUEST
  BROKER_TOPIC_DEVICE_CONFIG_STREAM
  BROKER_TOPIC_DEVICE_GENERAL_REQUEST
  BROKER_TOPIC_CLIENT_DBDATA
  BROKER_TOPIC_DBDATA_URL
  BROKER_TOPIC_STREAM_CONFIG_ACK
  BROKER_TOPIC_DEVICE_STREAMS_STATUS
  BROKER_TOPIC_PARAMS_CONFIG_REQUESTS
  BROKER_TOPIC_PARAMS_CONFIG_ACK
  BROKER_TOPIC_PARAMS_CONFIG_DATA
  BROKER_TOPIC_SECURITY_PERSON
  BROKER_TOPIC_SECURITY_LOITERING
  BROKER_TOPIC_AI_SERVICE_CONFIG_REQUEST
  BROKER_TOPIC_AI_SERVICE_ACK
  BROKER_TOPIC_FACE_TERMINAL_UPDATE_SIGNAL
  BROKER_REMOTE_FACE_RECOG_TIMEOUT
  BROKER_FACE_QUALITY_REQUEST_TOPIC
  BROKER_FACE_QUALITY_ACK
  FACE_IMAGE_PATH
  FACE_IMAGE_QUALITY_PATH
  IMAGE_PATH
  AVATAR_PATH
  USER_PACK_PATH
  USER_BACKUP_PATH
  GUEST_PATH
  GUEST_QR_IMAGE_PATH
  GUEST_CARD_IMAGE
  GUEST_FACE_IMAGE
  EKYC_IMAGE_PATH
  EKYC_CROPPED_IMAGE_PATH
  REMOTE_ATTANDANT_IMAGE_PATH
  SERCURITY_EVENT_PATH
  BREAK_IN_IMAGE_PATH
  LOITERING_IMAGE_PATH
  PATIENT_DB_ID
  PATIENT_PATH
  PATIENT_AVATAR_IMAGE_PATH
  PATIENT_FACE_IMAGE_PATH
  PATIENT_HOMEMATE_PATH
  PATIENT_HOMEMATE_AVATAR_PATH
  PATIENT_HOMEMATE_FACE_IMAGE_PATH
  PATIENT_GET_INFO_URL
  PATIENT_GET_INFO_URL_AUTHEN
  EMAIL_ADMIN_USER
  EMAIL_ADMIN_PASSWORD
  EMAIL_ADMIN_DEFAULT_RECEIVER
  EMAIL_ADMIN_HOST
  EMAIL_ADMIN_PORT
  ALEART_CONFIG_MASK
  ALEART_CONFIG_TEMPERATURE_ENABLE
  ALEART_CONFIG_TEMPERATURE
  LDAP_ENABLE
  LDAP_URL
  LDAP_ADMIN_DN
  LDAP_ADMIN_PASS
  LDAP_DEFAULT_SEARCH
  FACE_TERMINAL_IS_SAVE_RESULT_FROM_MQTT
  FACE_TERMINAL_IS_AC_PROCESS
  IS_NFS_ENABLE
  NFS_PATH
  UPDATE_DURATION
  INTERGRATION_FREEE_IS_ENABLE
  INTERGRATION_FREEE_URL
  INTERGRATION_FREEE_SYNC_INTERVAL
  CLUSTER_IS_ENABLE
  CLUSTER_URL
  CLUSTER_AUTEHN_TOKEN
  SHIFT_API
  SHIFT_CLIENT_ID
  SHIFT_CLIENT_SECRET
  SHIFT_API_KEY
  IS_USER_LOGIN_AD
  MQTT_USER
  MQTT_PASS
  WPF_IS_SAVE_RESULT_FROM_MQTT
  WPF_IS_AC_PROCESS
  DEFAULT_TIME_ZONE_OFFSET
  MAXIMUM_NON_USER_CACHE_IN_TERMINAL
  IS_FACE_QUALITY_BYPASS
  LOG_MAX_TIME
  LOG_BACKUP_MAX_TIME
  BROKER_TOPIC_FACE_MATCHING_RESULTS
  BROKER_TOPIC_FACE_MATCHING_REQUESTS
  DEFAULT_FACE_IMAGE_MATCHER
  DEFAULT_AI_DETECTOR
  SHIFT_USER_POLLING_INTERVAL
  SQL_SYNC_INTERVAL
  IS_REPORT_THREAD_STAND_ALONE
  IS_SCHEDULER_THREAD_STAND_ALONE
  IS_SQL_SYNC_THREAD_STAND_ALONE
  IS_FACE_TERMINAL_SYNC_THREAD_STAND_ALONE
  AUTO_SYNC_AC_USER_TO_DEVICE_INTERVAL
  AUTO_CHECK_AI_CORE_INTERVAL
  AUTO_ADD_USER_ATT_INTERVAL
  FACE_RESULT_SQL_SYNC_INTERVAL
  FACE_RESULT_ON_CALL_SQL_SYNC_INTERVAL
  SHIFT_USER_SQL_SYNC_INTERVAL
  AC_USER_SYNC_INTERVAL
  FACE_RESULT_GENERAL_INTEGRATION_ENABLE
  FACE_RESULT_GENERAL_INTEGRATION_URL
  FACE_RESULT_GENERAL_INTEGRATION_TOKEN
  FACE_RESULT_GENERAL_INTEGRATION_INTERVAL
  EVENT_VIDEO_STORAGE_DAYS
  IS_LOCK_USER_UPDATE_FACE_IMAGE
  FAKE_CAM_DEVICE_ID
  FAKE_CAM_CHANNEL_ID
  FAKE_CAM_SRC_ID
  GUEST_DB_ID
  IS_GUEST_AUTO_LOCK
  USER_FEEDBACK
  REMOTE_CHECK_DEFAULT_LIMIT
  REMOTE_CHECK_DEFAULT_ENABLE
  REMOTE_APPROVE_TIME_LIMIT_ENABLE
  REMOTE_APPROVE_DATE_LIMIT
  REMOTE_APPROVE_HOUR_LIMIT
  REMOTE_APPROVE_MONT_OFFSET_LIMIT
  VEHICLE_SEARCH_DEFAULT_DEVICE_ID
  PERSON_SEARCH_DEFAULT_DEVICE_ID
  REDIS_HOST
  REDIS_PORT
  REDIS_PASS
  CACHED_FACE_FPS
  CACHED_PERSON_FPS
  FACE_QUALITY_SIMILARITY_THRESHOLD
  DUPLICATE_RESULT_TIME_FILTER
  IS_SAVE_EVENT_VIDEO
  FACE_RES_MQTT_DEBUG
  HUMAN_RES_MQTT_DEBUG
  HEALTH_CHECK_STORAGE_SIZE
  HEALTH_CHECK_STORAGE_THRESHOLD
  HEALTH_CHECK_RAM_SIZE
  HEALTH_CHECK_RAM_THRESHOLD
  IS_DUPLICATE_CAMERA_PROCESS
  IS_SAVE_HUMAN_CAPTURE
  IS_SAVE_HUMAN_CACHED
  IS_SAVE_FACE_CACHED
  IS_DEBUG_EMOTION
  IS_DEBUG_HUMAN
  IS_DEBUG_GENERAL_LOG
  IS_DEBUG_UNKNOWN_BATCH
  UNKNOWN_INSERT_BATCH_SIZE
  UNKNOWN_INSERT_INTERVAL
  VIDEO_ANALYTICS_DEBUG
  IPARKING_CUSTOMER_SYNC_DEBUG
  IPARKING_IDENTITY_GROUP_SYNC_DEBUG
  IPARKING_VEHICLE_SYNC_DEBUG
  IPARKING_IDENTITY_SYNC_DEBUG
  PERSON_COUNT_THRESHOLD_ALARM
  CORE_AI_SYNC_DEBUG
  JSON_CUSTOME_CONFIG
  IS_POPUP_ALARM_ENALBE
  DEVICE_API_SERVICE_DEBUG
  UNKNOWN_ALARM_CONFIDENCE_THRESHOLD
  UNKNOWN_ALARM_DELAY_SEC
  UNKNOWN_ALARM_QUALITY_THRESHOLD
}

enum ConfigDataType {
  String
  Boolean
  Int
  Float
}

enum Ai_Service_Ids {
  face_rescognition
  person_intrusion
}

enum SqlSyncMode {
  FACE_RESULT
  SHIFT_USER
}

model ServerConfig {
  key             ServerConfigKey @id
  value           String
  isRestartServer Boolean         @default(true)
  dataType        ConfigDataType  @default(String)
  isValueMask     Boolean?        @default(true)
  isClusterSync   Boolean         @default(false)
  dateModified    DateTime        @default(now()) @updatedAt
}

//Company
model Company {
  id             String  @id @default(uuid())
  name           String
  sortKey        String  @default("")
  timezoneOffset String?
  isActive       Boolean @default(true)
  civamsSiteId   String?

  companyType Int?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Users                   User[]
  Departments             Department[]
  Owner                   CompanyOwner[]
  Patient                 Patient[]
  Guests                  Guest[]
  DefaultShift            DefaultShift[]
  CivamsSite              CivamsSite?               @relation(fields: [civamsSiteId], references: [id], onDelete: Cascade)
  ShiftUserExplainAdmin   ShiftUserExplainAdmin[]
  ReportUserFilterCompany ReportUserFilterCompany[]
  TelegramGroupCompany    TelegramGroupCompany[]
  ShiftUserReportByDay    ShiftUserReportByDay[]
  RemoteAdminCom          RemoteAdminCom[]
  RemoteFaceResultLimit   RemoteFaceResultLimit[]
  RemoteApproverCom       RemoteApproverCom[]
  DeviceCompany           DeviceCompany[]
  CameraCompany           CameraCompany[]
  AccessControlCompany    AccessControlCompany[]
  AccessControl           AccessControl[]
}

model CompanyOwner {
  companyId     String
  userId        String
  isActive      Boolean  @default(true)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  User    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([companyId, userId])
}

//Department
model DepartmentAdmin {
  departmentId  String
  userId        String
  activeStatus  ActiveStatus @default(DEACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt
  Department    Department   @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  User          User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([departmentId, userId])
}

model Department {
  id                         String                       @id @default(uuid())
  name                       String
  companyId                  String
  parentId                   String?
  isActive                   Boolean                      @default(true)
  isClusterSync              Boolean                      @default(false)
  civamsSiteId               String?
  dateCreated                DateTime                     @default(now())
  dateModified               DateTime                     @default(now()) @updatedAt
  Users                      User[]
  Company                    Company                      @relation(fields: [companyId], references: [id], onDelete: Cascade)
  AcDepartmentTime           AcDepartmentTime[]
  Patient                    Patient[]
  DepartmentAdmin            DepartmentAdmin[]
  Guests                     Guest[]
  DefaultShift               DefaultShift[]
  CivamsSite                 CivamsSite?                  @relation(fields: [civamsSiteId], references: [id], onDelete: Cascade)
  ShiftUserExplainAdmin      ShiftUserExplainAdmin[]
  ReportUserFilterDepartment ReportUserFilterDepartment[]
  TelegramGroupDepartment    TelegramGroupDepartment[]
  ShiftUserReportByDay       ShiftUserReportByDay[]
  RemoteAdminDep             RemoteAdminDep[]
  RemoteFaceResultLimit      RemoteFaceResultLimit[]
  RemoteApproverDep          RemoteApproverDep[]
  AccessControlDepartment    AccessControlDepartment[]
  AccessControl              AccessControl[]
}

//Organization
model Organization {
  id                         String                       @id @default(uuid())
  name                       String
  enName                     String?
  vnName                     String?
  parentId                   String?
  type                       Int                          @default(0)
  isActive                   Boolean                      @default(true)
  isClusterSync              Boolean                      @default(false)
  civamsSiteId               String?
  dateCreated                DateTime                     @default(now())
  dateModified               DateTime                     @default(now()) @updatedAt
  OrganizationUser           OrganizationUser[]
  RemoteAdminOrg             RemoteAdminOrg[]
  RemoteFaceResultLimit      RemoteFaceResultLimit[]
  OrganizationTemplateDetail OrganizationTemplateDetail[]
  RemoteApproverOrg          RemoteApproverOrg[]
  AccessControlOrg           AccessControlOrg[]
  AccessControl              AccessControl[]
}

model OrganizationTemplate {
  id               String  @id @default(uuid())
  userId           String
  name             String
  description      String?
  isPublicTemplate Boolean @default(false)

  isClusterSync              Boolean                      @default(false)
  civamsSiteId               String?
  dateCreated                DateTime                     @default(now())
  dateModified               DateTime                     @default(now()) @updatedAt
  OrganizationTemplateDetail OrganizationTemplateDetail[]
  User                       User                         @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model OrganizationTemplateDetail {
  templateId     String
  organizationId String

  isClusterSync Boolean  @default(false)
  civamsSiteId  String?
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Template     OrganizationTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  Organization Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@id([templateId, organizationId])
}

model OrganizationUser {
  userId            String
  organizationId    String
  isMaster          Boolean @default(false)
  userTitle         String?
  orgUserId         String?
  orgUserLevel      Int?
  orgIntegrationKey String?
  orgShiftCode      String?

  isClusterSync Boolean  @default(false)
  civamsSiteId  String?
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  Organiztion Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@id([userId, organizationId])
}

//User
model User {
  id              String       @id @default(uuid())
  email           String       @unique
  roleId          RoleType     @default(EMPLOYEE)
  name            String
  password        String
  birthday        DateTime?
  gender          Int          @default(0)
  avatar          String       @default("")
  companyId       String
  departmentId    String
  integrationKey  String?
  integrationKey2 String?
  patientId       String?
  phone           String?
  activeStatus    ActiveStatus @default(ACTIVE)
  isClusterSync   Boolean      @default(false)
  dateCreated     DateTime     @default(now())
  dateModified    DateTime     @default(now()) @updatedAt
  adName          String?
  title           String?

  isLoginAvaiable     Boolean @default(true)
  isFaceReScanEnable  Boolean @default(true)
  isRemoteCheckEnable Boolean @default(false)
  civamsSiteId        String?
  studentId           String?

  ottEnable  Boolean @default(false)
  telegramId String?

  voiceString      String?
  welcomeImagePath String?
  welcomVoicePath  String?
  displayString    String?
  color            String?

  userRank            Int    @default(0)
  MaskConfThreshold   Float?
  NoMaskConfThreshold Float?

  idCardNumber         String?
  idCardJson           String?
  idCardFaceSimilarity Float?

  autoLockTime DateTime?

  isVip       Boolean @default(false)
  isBlackList Boolean @default(false)

  tenantId String?

  Department                     Department                       @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  Company                        Company                          @relation(fields: [companyId], references: [id], onDelete: Cascade)
  Devices                        DeviceUser[]
  // Roles                      UserRole[]
  // Shifts                     UserShift[]
  FaceImages                     UserFaceImage[]
  OwnedCompany                   CompanyOwner[]
  AcAdmin                        AcAdmin[]
  AcUserTime                     AcUserTime[]
  FaceResult                     FaceResult[]
  RemoteFaceResults              RemoteFaceResult[]
  AttandantExplains              AttandantExplain[]
  AttandantModifies              AttandantModify[]
  DepartmentAdmin                DepartmentAdmin[]
  DeletedUserFaceImage           DeletedUserFaceImage[]
  // FaceResultNotes            FaceResultNote[]
  ShiftUser                      ShiftUser[]
  FaceResultUserReport           FaceResultUserReport[]
  UserAuthenToken                UserAuthenToken[]
  Notification                   Notification[]
  SystemLog                      SystemLog[]                      @relation("systemLogActor")
  AffectedSystemLog              SystemLog[]                      @relation("systemLogAffect")
  UserTimeReport                 UserTimeReport[]
  UserAttandantTime              UserAttandantTime[]
  FaceResultError                FaceResultError[]
  AlertMessage                   AlertMessage[]
  UserFaceImageScan              UserFaceImageScan[]
  DefaultShift                   DefaultShift[]
  ShiftUserExplain               ShiftUserExplain[]
  CivamsSite                     CivamsSite?                      @relation(fields: [civamsSiteId], references: [id], onDelete: Cascade)
  AlertUser                      AlertUser[]
  ShiftUserExplainAdmin          ShiftUserExplainAdmin[]
  SmartSearchUserResult          SmartSearchUserResult[]
  IdCard                         IdCard[]
  ReportUserFilterUser           ReportUserFilterUser[]
  ReportUserFilterExUser         ReportUserFilterExUser[]
  ReportUserFilterExRuleUser     ReportUserFilterExRuleUser[]
  ReportUserFilterResultUser     ReportUserFilterResultUser[]
  Student                        Student?                         @relation(fields: [studentId], references: [id], onDelete: SetNull)
  AutoSmartSearchFileUser        AutoSmartSearchFileUser[]
  TelegramGroupUser              TelegramGroupUser[]
  ShiftUserReportByDay           ShiftUserReportByDay[]
  OrganizationUser               OrganizationUser[]
  CalculatedWorkMonth            CalculatedWorkMonth[]
  UserLoginFailed                UserLoginFailed[]
  UserFeedback                   UserFeedback[]
  RemoteAdmin                    RemoteAdminUser[]                @relation("remoteAdmin")
  RemoteUser                     RemoteAdminUser[]                @relation("remoteUser")
  RemoteAdminDep                 RemoteAdminDep[]
  RemoteAdminCom                 RemoteAdminCom[]
  RemoteAdminOrg                 RemoteAdminOrg[]
  RemoteFaceResultLimit          RemoteFaceResultLimit[]
  RemoteApprover                 RemoteApproverUser[]             @relation("remoteApprover")
  RemoteApproveUser              RemoteApproverUser[]             @relation("remoteApprovUser")
  RemoteApproverDep              RemoteApproverDep[]
  RemoteApproverCom              RemoteApproverCom[]
  RemoteApproverOrg              RemoteApproverOrg[]
  OrganizationTemplate           OrganizationTemplate[]
  UserAccessKeys                 FingerPrint[]
  RemoteFaceResultApproveHistory RemoteFaceResultApproveHistory[]
  UserOtp                        UserOtp[]
  FcardUser                      FcardUser[]
  PamlPrint                      PamlPrint[]
  VehicleParkingLicenseAllowed   VehicleParkingLicenseAllowed[]
  UserEkycConfig                 UserEkycConfig[]
  AttUser                        AttUser[]
  DayInvalidReason               DayInvalidReason[]
  FaceSearchApiResult            FaceSearchApiResult[]
  ShiftUserVip                   ShiftUserVip[]
  AccessControlUser              AccessControlUser[]              @relation("acUser")
  AccessControlReplacer          AccessControlUser[]              @relation("acReplacer")
  AccessControlUserWaitting      AccessControlUserWaitting[]      @relation("acUserWaitting")
  AccessControlReplacerWaitting  AccessControlUserWaitting[]      @relation("acReplacerWaitting")
  DataImportJob                  DataImportJob[]
  DataImportRow                  DataImportRow[]
  CalculatedWorkDay              CalculatedWorkDay[]
  ByPassQualityUser              ByPassQualityUser[]
  UserExportDetail               UserExportDetail[]
  CoreAiHumanTrackResult         CoreAiHumanTrackResult[]
  IparkingCustomer               IparkingCustomer[]
  DeviceSyncLog                  DeviceSyncLog[]
  TrackMapHistory                TrackMapHistory[]
  Log                            GlobalCustomeLog[]
  ReIdLabel                      ReIdLabel[]

  @@index([email, name, integrationKey, dateCreated, dateModified])
}

model ByPassQualityUser {
  id            String   @id @default(uuid())
  userId        String
  errorData     String?
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserLoginFailed {
  userId        String @id
  failedCounter Int    @default(1)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model DeletedUsers {
  userId        String   @id
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())

  @@index([dateCreated])
}

model UserAuthenToken {
  userId      String @id
  authenToken String
  User        User   @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model DeletedUserFaceImage {
  id            Int           @id @default(autoincrement())
  userId        String
  faceImageType FaceImageType
  path          String
  size          Int           @default(0)
  isClusterSync Boolean       @default(false)
  User          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserFaceImage {
  id                Int                 @id @default(autoincrement())
  userId            String
  faceImageType     FaceImageType
  path              String
  isClusterSync     Boolean             @default(false)
  dateCreated       DateTime            @default(now())
  dateModified      DateTime            @default(now()) @updatedAt
  User              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  UserFaceImageScan UserFaceImageScan[]
  DeviceSyncLog     DeviceSyncLog[]
}

model IdCard {
  id           String @id @default(uuid())
  serialNumber String
  data         String
  type         Int

  userId    String?
  studentId String?

  isActive    Boolean   @default(true)
  expiredDate DateTime?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User    User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  Student Student? @relation(fields: [studentId], references: [id], onDelete: SetNull)
}

model FingerPrint {
  id   String @id @default(uuid())
  data String
  type Int

  userId    String?
  studentId String?

  image       String?
  isActive    Boolean   @default(true)
  expiredDate DateTime?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User    User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  Student Student? @relation(fields: [studentId], references: [id], onDelete: Cascade)
}

model PamlPrint {
  id   String @id @default(uuid())
  data String
  type Int

  userId    String?
  studentId String?

  image       String?
  isActive    Boolean   @default(true)
  expiredDate DateTime?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User    User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  Student Student? @relation(fields: [studentId], references: [id], onDelete: Cascade)
}

model UserFeedback {
  id           String   @id @default(uuid())
  userId       String
  type         Int      @default(0)
  status       Int      @default(0)
  title        String
  detail       String
  responseTime DateTime

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User                 User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  UserFeedbackImage    UserFeedbackImage[]
  UserFeedbackResponse UserFeedbackResponse[]
}

model UserFeedbackResponse {
  id             String @id @default(uuid())
  userFeedbackId String
  detail         String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  UserFeedback UserFeedback @relation(fields: [userFeedbackId], references: [id], onDelete: Cascade)
}

model UserFeedbackImage {
  id         String @id @default(uuid())
  feedbackId String
  path       String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  UserFeedback UserFeedback @relation(fields: [feedbackId], references: [id], onDelete: Cascade)
}

//Guest
model Guest {
  id           String       @id @default(uuid())
  name         String
  cardId       String       @unique
  email        String?      @unique
  phone        String?
  birthday     DateTime?
  placeOfBirth String       @default("")
  address      String       @default("")
  gender       Int          @default(0)
  nationality  String       @default("")
  avatar       String
  companyId    String?
  Company      Company?     @relation(fields: [companyId], references: [id], onDelete: Cascade)
  departmentId String?
  Department   Department?  @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  status       ActiveStatus @default(DEACTIVE)
  expiredTime  DateTime

  referenceUserId String?
  groupId         String?

  isClusterSync                Boolean                        @default(false)
  dateCreated                  DateTime                       @default(now())
  dateModified                 DateTime                       @default(now()) @updatedAt
  Devices                      DeviceGuest[]
  FaceResults                  GuestFaceResult[]
  AcGuestTime                  AcGuestTime[]
  GuestCardImage               GuestCardImage[]
  FaceImages                   GuestFaceImage[]
  SmartSearchGuestResult       SmartSearchGuestResult[]
  VehicleParkingLicenseAllowed VehicleParkingLicenseAllowed[]
  RemoteFaceResult             RemoteFaceResult[]
  AcGuest                      AcGuest[]
  AttGuest                     AttGuest[]
  FaceSearchApiResult          FaceSearchApiResult[]
  GuestLocation                GuestLocation[]
  IparkingCustomer             IparkingCustomer[]
  AccessControlGuest           AccessControlGuest[]

  @@index([expiredTime, dateCreated, dateModified])
}

model DeletedGuests {
  guestId       String   @id
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())

  @@index([dateCreated])
}

model GuestCardImage {
  id            String   @id @default(uuid())
  path          String
  guestId       String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Guest Guest @relation(fields: [guestId], references: [id], onDelete: Cascade)
}

model GuestFaceImage {
  id            String   @id @default(uuid())
  path          String
  guestId       String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Guest Guest @relation(fields: [guestId], references: [id], onDelete: Cascade)
}

model GuestGroups {
  id            String  @id @default(uuid())
  name          String
  isAutoExpired Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

//Patient
model Patient {
  id              String              @id @default(uuid())
  name            String
  gender          Int                 @default(0)
  birthday        DateTime
  avatar          String
  intergrationKey String              @unique
  companyId       String?
  departmentId    String?
  status          PatientStatus       @default(ACTIVE)
  isClusterSync   Boolean             @default(false)
  dateCreated     DateTime            @default(now())
  dateModified    DateTime            @default(now()) @updatedAt
  FaceImages      PatientFaceImage[]
  Company         Company?            @relation(fields: [companyId], references: [id], onDelete: Cascade)
  Department      Department?         @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  PatientHomeMate PatientHomeMate[]
  Devices         DevicePatient[]
  FaceResults     PatientFaceResult[]
  AcPatientTime   AcPatientTime[]
}

model PatientHomeMate {
  id                       String                     @id @default(uuid())
  name                     String
  gender                   Int                        @default(0)
  birthday                 DateTime
  avatar                   String
  patientId                String
  isClusterSync            Boolean                    @default(false)
  dateCreated              DateTime                   @default(now())
  dateModified             DateTime                   @default(now()) @updatedAt
  Patient                  Patient                    @relation(fields: [patientId], references: [id], onDelete: Cascade)
  PatientHomeMateFaceImage PatientHomeMateFaceImage[]
}

model PatientFaceImage {
  id            String        @id @default(uuid())
  patientId     String
  faceImageType FaceImageType
  path          String
  size          Int
  isClusterSync Boolean       @default(false)
  dateCreated   DateTime      @default(now())
  dateModified  DateTime      @default(now()) @updatedAt
  Patient       Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model PatientHomeMateFaceImage {
  id                String          @id @default(uuid())
  patientHomeMateId String
  faceImageType     FaceImageType
  path              String
  size              Int
  isClusterSync     Boolean         @default(false)
  dateCreated       DateTime        @default(now())
  dateModified      DateTime        @default(now()) @updatedAt
  PatientHomeMate   PatientHomeMate @relation(fields: [patientHomeMateId], references: [id], onDelete: Cascade)
}

//User role
model Role {
  id            RoleType    @id
  name          String
  isClusterSync Boolean     @default(false)
  // Users         UserRole[]
  Permisson     Permisson[]
}

//Device
model Device {
  id                   String             @id
  coreAiUuId           String?
  ip                   String             @default("")
  srcIp                String             @default("")
  name                 String
  type                 Int
  faceCheckType        Int?
  isDefaultDevice      Boolean            @default(false)
  isTimekeepingDevice  Boolean            @default(false)
  isFaceResultEditable Boolean            @default(false)
  isFirmwareAutoUpdate Boolean            @default(false)
  lastUpdateTime       DateTime           @default(now())
  statusUpdate         DeviceUpdateStatus @default(FAIL)
  token                String             @default("")
  password             String?
  activeStatus         ActiveStatus       @default(DEACTIVE)
  isRunning            Boolean?           @default(false)

  uptime           String @default("")
  cpuUsage         String @default("")
  memTotal         String @default("")
  memFree          String @default("")
  freeDisk         String @default("")
  macAddress       String @default("")
  relayCurrentMode Int?

  deviceUserName String?
  devicePassword String?
  direction      Int?

  licenseDeviceId   String?
  licenseExpireDate String?
  licenseNumberUser Int?

  deviceIntegrationKey String?

  runningStatus    Int      @default(1)
  time             DateTime @default(now())
  isAuthen         Boolean  @default(false)
  resetAuthenCount Int      @default(0)
  versionCode      Int      @default(-1)
  versionName      String   @default("")
  serialNumber     String   @default("")
  lived            Int      @default(-1)
  maxAge           Int      @default(-1)
  vol              Int      @default(-1)
  current_vol      Int      @default(-1)

  request_queue  Int @default(-1)
  mask_queue     Int @default(-1)
  quality_queue  Int @default(-1)
  headpose_queue Int @default(-1)
  detect_queue   Int @default(-1)
  feature_queue  Int @default(-1)
  search_queue   Int @default(-1)

  personInCount  Int @default(0)
  personOutCount Int @default(0)

  isClusterSync              Boolean                      @default(false)
  dateCreated                DateTime                     @default(now())
  dateModified               DateTime                     @default(now()) @updatedAt
  Users                      DeviceUser[]
  Guests                     DeviceGuest[]
  Patients                   DevicePatient[]
  Cameras                    DeviceCamera[]
  AcDevice                   AcDevice[]
  FaceResult                 FaceResult[]
  OrphanFaceResult           OrphanFaceResult[]
  FaceTerminalSyncStatus     FaceTerminalSyncStatus[]
  AiCoreStatus               AiCoreStatus[]
  PatientFaceResult          PatientFaceResult[]
  GuestFaceResult            GuestFaceResult[]
  DeviceGroup                DeviceGroup[]
  FaceResultError            FaceResultError[]
  AlertMessage               AlertMessage[]
  DeviceAttandatTime         DeviceAttandatTime[]
  Device4Config              DeviceType4Config[]
  EventVideo                 EventVideo[]
  CoreAiHumanTrackInfo       CoreAiHumanTrackInfo[]
  FaceTracks                 FaceTrackIds[]
  WpfFaceTerminalStatus      WpfFaceTerminalStatus[]
  TelegramGroupDevice        TelegramGroupDevice[]
  DeviceType9Config          DeviceType9Config[]
  DeviceScreenCapture        DeviceScreenCapture[]
  DeviceSignalLog            DeviceSignalLog[]
  DeviceRoi                  DeviceRoi[]
  VmsCameras                 Camera[]
  FaceTerminalConfig         FaceTerminalConfig[]
  CameraRoi                  CameraRoi[]
  FcardUser                  FcardUser[]
  FcardResults               FcardResults[]
  DeviceCompany              DeviceCompany[]
  FcardDeviceTz              FcardDeviceTz[]
  VehiclePackingDevice       VehiclePackingDevice[]
  AttDevice                  AttDevice[]
  AccessControlDevice        AccessControlDevice[]
  DeviceLocation             DeviceLocation[]
  AccessControlInOutCountLog AccessControlInOutCountLog[]
  DeviceSyncLog              DeviceSyncLog[]
}

model DeviceGroupInfo {
  id                       String                     @id @default(uuid())
  name                     String
  piority                  Int?
  isClusterSync            Boolean                    @default(false)
  dateCreated              DateTime                   @default(now())
  dateModified             DateTime                   @default(now()) @updatedAt
  DeviceGroup              DeviceGroup[]
  AccessControlDeviceGroup AccessControlDeviceGroup[]
}

model DeviceGroup {
  deviceGroupId String
  deviceId      String
  isClusterSync Boolean         @default(false)
  dateCreated   DateTime        @default(now())
  dateModified  DateTime        @default(now()) @updatedAt
  Device        Device          @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  DeviceGroup   DeviceGroupInfo @relation(fields: [deviceGroupId], references: [id], onDelete: Cascade)

  @@id([deviceGroupId, deviceId])
}

model DeviceUser {
  deviceId String
  userId   String
  status   ActiveStatus @default(ACTIVE)
  isAdmin  Boolean      @default(false)

  isDeviceSyned      Boolean   @default(false)
  deviceCallSyncTime DateTime?
  deviceSynedTime    DateTime?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([deviceId, userId])
}

model DeviceGuest {
  deviceId      String
  guestId       String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt

  Guest  Guest  @relation(fields: [guestId], references: [id], onDelete: Cascade)
  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([deviceId, guestId])
}

model DevicePatient {
  deviceId      String
  patientId     String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt

  Device  Device  @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  Patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@id([deviceId, patientId])
}

model DeviceCompany {
  deviceId      String
  companyId     String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt

  Device  Device  @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  Company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@id([deviceId, companyId])
}

model CameraCompany {
  cameraId      String
  companyId     String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt

  Company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  Camera  Camera  @relation(fields: [cameraId], references: [id], onDelete: Cascade)

  @@id([cameraId, companyId])
}

model DeviceScreenCapture {
  id       Int     @id @default(autoincrement())
  deviceId String
  cameraId String?
  image    String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

model DeviceSignalLog {
  id       Int      @id @default(autoincrement())
  deviceId String
  time     DateTime
  isOnline Boolean  @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

//Device Config
model DeviceType4Config {
  deviceId         String   @id
  camIp            String   @default("")
  camRtsp          String   @default("")
  isDoorAccess     Boolean  @default(true)
  language         String   @default("vi")
  serverIp         String   @default("")
  serverPort       Int      @default(8883)
  httpBrokerIp     String   @default("")
  httpBrokerPort   Int      @default(8883)
  httpApi          String   @default("")
  isPlaySound      Boolean  @default(true)
  deviceJsonConfig String?
  lastSyncTime     DateTime
  isClusterSync    Boolean  @default(false)
  dateCreated      DateTime @default(now())
  dateModified     DateTime @default(now()) @updatedAt

  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

model DeviceDetectorConfig {
  deviceId String @id

  faceConfident        String @default("0.5")
  alignMode            String @default("0")
  bBoxMargin           String @default("0.25")
  maxSize              String @default("300")
  faceBestShotEnable   String @default("1")
  faceBestShotDuration String @default("1000")
  namedSentDuration    String @default("10000")

  personDetectConfident  String @default("0.5")
  kptConfident           String @default("0.4")
  personBestShotDuration String @default("2000")

  fakeFaceEnable     String @default("1")
  fasValidationCount String @default("2")
  fasConfident       String @default("0.5")

  serverUrl String @default("http://localhost:33000")
  timeout   String @default("5000")

  deviceJsonConfig String?

  lastSyncTime  DateTime?
  isClusterSync Boolean   @default(false)
  dateCreated   DateTime  @default(now())
  dateModified  DateTime  @default(now()) @updatedAt
}

model DeviceType9Config {
  deviceId   String  @id
  cameraIp   String?
  cameraUser String?
  cameraPass String?
  cameraRtsp String?
  cameraFps  Int?

  doorHoldTime Int @default(5000)

  deviceJsonConfig String?

  lastSyncTime  DateTime
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

model FaceTerminalConfig {
  deviceId String @id

  showCompanyName     Boolean @default(true)
  showCompanyLabel    Boolean @default(true)
  companyLabel        String  @default("Công ty")
  showDepartmentLabel Boolean @default(true)
  departmentLabel     String  @default("Phòng ban")
  showTimekeeping     Boolean @default(true)
  showDoorAccess      Boolean @default(true)

  recogMode Int @default(0)
  language  Int @default(0)

  fasRgb         Boolean @default(true)
  fasIr          Boolean @default(true)
  fasStrictModOn Boolean @default(false)

  showIr       Boolean @default(true)
  imageImprove Boolean @default(true)

  ledOn    Boolean @default(true)
  ledLevel Int     @default(50)

  speakerOn  Boolean @default(true)
  soundLevel Int     @default(50)

  autoStart   Boolean @default(true)
  autoRestart Boolean @default(true)

  checkMask        Boolean @default(true)
  checkTemperature Boolean @default(true)
  temOnFaceOnly    Boolean @default(false)
  debugModeOn      Boolean @default(false)

  unknownDefineTime    Int   @default(7000)
  doorHoldTime         Int   @default(5000)
  screenOnTime         Int   @default(120)
  recogDistance        Int   @default(100)
  temperatureThreshold Float @default(37.5)
  logSaveDays          Int   @default(60)
  samePersonDelay      Int   @default(7000)

  mqttBrokerIp     String @default("")
  mqttBrokerPort   Int    @default(8883)
  defaultCompanyId String @default("")
  apiUrl           String @default("https://civams.cmcati.vn")
  socketUrl        String @default("https://civams.cmcati.vn")
  gRpcUrl          String @default("https://civams.cmcati.vn")
  gRpcPort         Int    @default(5001)
  updateUrl        String @default("https://civams.cmcati.vn")

  showIntegrationKey  Boolean @default(false)
  integrationKeyLabel String  @default("Mã nhân viên")
  showResultErrCall   Boolean @default(false)
  showQrCode          Boolean @default(false)
  showAddCard         Boolean @default(false)
  showAddFinger       Boolean @default(false)

  numberUserRequired Int @default(1)
  maxUserWaitTime    Int @default(30)

  deviceJsonConfig String?

  lastSyncTime  DateTime
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

model FaceTerminalLogoConfig {
  id       String  @id @default(uuid())
  path     String
  isActive Boolean @default(true)
  x        Int     @default(0)
  y        Int     @default(0)
  width    Int
  height   Int

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

//Device sync status
model FaceTerminalSyncStatus {
  deviceId                  String    @id
  userLastSyncTime          DateTime?
  userSyned                 Int?      @default(0)
  totalUser                 Int?      @default(0)
  studentLastSyncTime       DateTime?
  studentSyned              Int?      @default(0)
  patientLastSyncTime       DateTime?
  deviceUserLastSyncTime    DateTime
  deviceUserSyned           Int?      @default(0)
  totalDeviceUser           Int?      @default(0)
  deviceStudentLastSyncTime DateTime?
  deviceStudentSyned        Int?      @default(0)
  devicePatientLastSyncTime DateTime?
  acLocationLastSyncTime    DateTime?
  acDeviceLastSyncTime      DateTime?
  acTimeLastSyncTime        DateTime?
  acDepTimeLastSyncTime     DateTime?
  acUserTimeLastSyncTime    DateTime?
  acPatientTimeLastSyncTime DateTime?
  acStudentTimeLastSyncTime DateTime?
  deviceLastSyncTime        DateTime?
  similarityLastSyncTime    DateTime?
  idCardLastSyncTime        DateTime?
  fingerLastSyncTime        DateTime?
  deviceAdminLastSyncTime   DateTime?
  guestLastSyncTime         DateTime?

  accessControlLastSyncTime           DateTime?
  accessControlUserLastSyncTime       DateTime?
  accessControlCompanyLastSyncTime    DateTime?
  accessControlDepartmentLastSyncTime DateTime?
  accessControlOrgLastSyncTime        DateTime?
  accessControlCameraLastSyncTime     DateTime?
  accessControlTimeLastSyncTime       DateTime?

  isClusterSync Boolean @default(false)
  Device        Device  @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

model AiCoreStatus {
  deviceId                  String    @id
  userLastSyncTime          DateTime
  userSyned                 Int?      @default(0)
  totalUser                 Int?      @default(0)
  deviceUserSyned           Int?      @default(0)
  totalDeviceUser           Int?      @default(0)
  videoAnalyticSyned        Int?      @default(0)
  userFaceImageLastSyncTime DateTime
  patientLastSyncTime       DateTime
  guestLastSyncTime         DateTime?
  deviceGuestSyned          Int?
  guestSyned                Int?

  cameraLastSyncTime    DateTime?
  roiLastSyncTime       DateTime?
  aiServiceLastSyncTime DateTime?

  videoAnalyticLastSyncTime DateTime?

  reIdLabelSyncTime DateTime?
  totalReIdLabel    Int?      @default(0)
  reIdLabelSyned    Int?      @default(0)

  isClusterSync Boolean @default(false)
  Device        Device  @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

model DeviceSyncLog {
  id            String  @id @default(uuid())
  deviceId      String
  logType       String
  isSuccess     Boolean
  status        String?
  syncLogDetail String?

  userId          String?
  userName        String?
  userType        String?
  dbId            String?
  userFaceImageId Int?
  videoAnalyticId String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Device        Device                @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  User          User?                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  UserFaceImage UserFaceImage?        @relation(fields: [userFaceImageId], references: [id], onDelete: Cascade)
  VideoAnalytic UploadVideoAnalytics? @relation(fields: [videoAnalyticId], references: [id], onDelete: Cascade)
}

model WpfFaceTerminalStatus {
  deviceId            String   @id
  userLastSyncTime    DateTime
  vipUserLastSyncTime DateTime
  userSyned           Int?     @default(0)
  deviceUserSyned     Int?     @default(0)
  patientLastSyncTime DateTime
  isClusterSync       Boolean  @default(false)
  Device              Device   @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

//Check face quality
model CheckFaceQualityEvent {
  id         String  @id @default(uuid())
  totalImage Int     @default(0)
  matcherId  String
  isFinished Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  FaceQualityResults CheckFaceQualityResult[]
}

model CheckFaceQualityResult {
  imageName        String
  eventId          String
  faceImageType    FaceImageType
  isProcessed      Boolean       @default(false)
  isError          Boolean?
  isCropFailed     Boolean?
  isPoseFailed     Boolean?
  isTooSmall       Boolean?
  isBlur           Boolean?
  isOcclustion     Boolean?
  isLocalDiff      Boolean?
  isGlobalExisted  Boolean?
  isDark           Boolean?
  darkValue        String?
  globalDbId       String?
  globalPersonId   String?
  globalPersonName String?
  globalImage      String?
  globalConfident  Float?
  isMask           Boolean?
  isClusterSync    Boolean       @default(false)
  dateCreated      DateTime      @default(now())
  dateModified     DateTime      @default(now()) @updatedAt

  CheckFaceQualityEvent CheckFaceQualityEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@id([imageName, eventId])
}

//=======================================================================

//Authen
model Function {
  id            FunctionId   @id
  name          String
  parentId      String?
  iconCss       String?
  sortOrder     Int?
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  Permisson     Permisson[]
}

model Permisson {
  roleId        RoleType
  functionId    FunctionId
  canCread      Boolean
  canRead       Boolean
  canUpdate     Boolean
  canDelete     Boolean
  isClusterSync Boolean    @default(false)
  Role          Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  Function      Function   @relation(fields: [functionId], references: [id], onDelete: Cascade)

  @@id([roleId, functionId])
}

//=======================================================================

//Face-results
model FaceResult {
  id     Int    @id @default(autoincrement())
  userId String

  trackingId   String
  humanTrackId String?

  deviceId           String
  srcResultId        String?
  srcDeviceId        String?
  type               Int      @default(2)
  time               DateTime
  image              String
  backupFileName     String?
  cameraId           String?
  cameraIp           String
  hashCode           String?
  temperature        String   @default("100")
  mask               String   @default("0")
  isUserChangeResult Boolean? @default(false)
  recognitionType    String?  @default("1")
  isClusterSync      Boolean  @default(false)
  isSqlSync          Boolean  @default(false)
  isApiSync          Boolean  @default(false)

  isNeedSentToCoreAi Boolean? @default(false)
  isSentCoreAi       Boolean? @default(false)
  isCoreAiResponse   Boolean? @default(false)
  isUserIdMissMatch  Boolean? @default(false)
  coreAiResult       String?

  detectDuration     Int?
  vectorDuration     Int?
  headposePitch      Float?
  headposeRoll       Float?
  headposeYaw        Float?
  faceQualityScore   Float?
  recogConfident     Float?
  reqTimePoint       DateTime?
  reqExDuration      Int?
  searchDuration     Int?
  topResultDebug     String?
  topResultCount     Int?
  isResultReConsider Boolean?  @default(false)
  vectorHash         String?
  reIdConf           Float?
  facekpts           String?

  maskConfident    Float?
  maskDuration     Int?
  headposeDuration Int?
  qualityDuration  Int?

  detectTimePoint Int?

  imageId String?
  roiId   String?

  reId String?

  dateCreated                  DateTime                       @default(now())
  dateModified                 DateTime                       @default(now()) @updatedAt
  User                         User                           @relation(fields: [userId], references: [id], onDelete: Cascade)
  SrcDevice                    Device?                        @relation(fields: [srcDeviceId], references: [id], onDelete: SetNull)
  in                           ShiftUser[]                    @relation("in")
  out                          ShiftUser[]                    @relation("out")
  Notification                 Notification[]
  FaceResultOnCall             FaceResultOnCall?
  AlertMessage                 AlertMessage[]
  RemoteFaceResult             RemoteFaceResult[]
  VehicleParkingLaneFaceResult VehicleParkingLaneFaceResult[]
  AttUser                      AttUser[]
  FaceResultIntegrationQueue   FaceResultIntegrationQueue[]
  AccessControlUserLog         AccessControlUserLog[]

  @@index([time, trackingId, humanTrackId, imageId, cameraId, reId])
}

model FakeFaceResult {
  id                 Int      @id @default(autoincrement())
  userId             String
  trackingId         String
  deviceId           String
  srcResultId        String?
  srcDeviceId        String?
  type               Int      @default(2)
  time               DateTime
  image              String
  backupFileName     String?
  cameraIp           String
  hashCode           String?
  temperature        String   @default("100")
  mask               String   @default("0")
  isUserChangeResult Boolean? @default(false)
  recognitionType    String?  @default("1")
  isClusterSync      Boolean  @default(false)
  isSqlSync          Boolean  @default(false)
  isApiSync          Boolean  @default(false)
  dateCreated        DateTime @default(now())
  dateModified       DateTime @default(now()) @updatedAt

  @@index([time, trackingId])
}

model FaceResultOnCall {
  id            Int        @id
  isClusterSync Boolean    @default(false)
  isSqlSync     Boolean    @default(false)
  synStatus     String     @default("")
  dateCreated   DateTime   @default(now())
  dateModified  DateTime   @default(now()) @updatedAt
  FaceResul     FaceResult @relation(fields: [id], references: [id], onDelete: Cascade)
}

model PatientFaceResult {
  id            Int      @id @default(autoincrement())
  patientId     String
  trackingId    String
  deviceId      String
  srcDeviceId   String?
  type          Int?
  time          DateTime
  image         String
  cameraIp      String
  temperature   String   @default("100")
  mask          String   @default("0")
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  Patient       Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)
  SrcDevice     Device?  @relation(fields: [srcDeviceId], references: [id], onDelete: Cascade)
}

model GuestFaceResult {
  id                            Int                             @id @default(autoincrement())
  guestId                       String
  trackingId                    String
  deviceId                      String
  srcDeviceId                   String?
  type                          Int?
  time                          DateTime
  image                         String
  cameraId                      String?
  cameraIp                      String
  temperature                   String                          @default("100")
  mask                          String                          @default("0")
  isClusterSync                 Boolean                         @default(false)
  dateCreated                   DateTime                        @default(now())
  dateModified                  DateTime                        @default(now()) @updatedAt
  Guest                         Guest                           @relation(fields: [guestId], references: [id])
  SrcDevice                     Device?                         @relation(fields: [srcDeviceId], references: [id], onDelete: SetNull)
  VehicleParkingLaneGuestResult VehicleParkingLaneGuestResult[]
  AttGuest                      AttGuest[]
  FaceResultIntegrationQueue    FaceResultIntegrationQueue[]
  AccessControlUserLog          AccessControlUserLog[]

  @@index([time])
}

model OrphanFaceResult {
  id       Int    @id @default(autoincrement())
  userId   String @default("unknown")
  userName String @default("")

  trackingId   String
  reId         String?
  humanTrackId String?

  deviceId       String
  srcDeviceId    String?
  type           Int      @default(2)
  time           DateTime
  image          String
  cameraIp       String
  cameraId       String?
  hashCode       String?
  backupFileName String?

  detectDuration   Int?
  vectorDuration   Int?
  headposePitch    Float?
  headposeRoll     Float?
  headposeYaw      Float?
  faceQualityScore Float?
  recogConfident   Float?
  reqTimePoint     DateTime?
  reqExDuration    Int?
  searchDuration   Int?
  reIdConf         Float?
  facekpts         String?

  maskConfident    Float?
  maskDuration     Int?
  headposeDuration Int?
  qualityDuration  Int?

  detectTimePoint Int?

  imageId String?
  roiId   String?

  isClusterSync         Boolean                 @default(false)
  dateCreated           DateTime                @default(now())
  dateModified          DateTime                @default(now()) @updatedAt
  SrcDevice             Device?                 @relation(fields: [srcDeviceId], references: [id], onDelete: SetNull)
  FaceImageSearchResult FaceImageSearchResult[]
  AlertMessage          AlertMessage[]

  @@index([time, trackingId, reId, humanTrackId, imageId, cameraId])
}

model CachedUnknownFace {
  id String @id @default(uuid())

  trackingId   String
  humanTrackId String?

  deviceId String
  time     DateTime
  image    String
  cameraId String?

  roiId String?

  isCoreAiSent     Boolean @default(false)
  isCoreAiResponse Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@index([time, trackingId, humanTrackId])
}

model FaceTrackIds {
  trackId       String   @id
  deviceId      String
  srcDeviceId   String?
  cameraId      String?
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  SrcDevice     Device?  @relation(fields: [srcDeviceId], references: [id], onDelete: SetNull)
  Camera        Camera?  @relation(fields: [cameraId], references: [id], onDelete: SetNull)

  @@index([trackId])
}

model RemoteAdminUser {
  adminId String
  userId  String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Admin User @relation(name: "remoteAdmin", fields: [adminId], references: [id], onDelete: Cascade)
  User  User @relation(name: "remoteUser", fields: [userId], references: [id], onDelete: Cascade)

  @@id([adminId, userId])
}

model RemoteAdminDep {
  adminId      String
  departmentId String

  isClusterSync Boolean    @default(false)
  dateCreated   DateTime   @default(now())
  dateModified  DateTime   @default(now()) @updatedAt
  Admin         User       @relation(fields: [adminId], references: [id], onDelete: Cascade)
  Department    Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  @@id([adminId, departmentId])
}

model RemoteAdminCom {
  adminId   String
  companyId String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  Admin         User     @relation(fields: [adminId], references: [id], onDelete: Cascade)
  Company       Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@id([adminId, companyId])
}

model RemoteAdminOrg {
  adminId String
  orgId   String

  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt
  Admin         User         @relation(fields: [adminId], references: [id], onDelete: Cascade)
  Organization  Organization @relation(fields: [orgId], references: [id], onDelete: Cascade)

  @@id([adminId, orgId])
}

model RemoteApproverUser {
  aproverId String
  userId    String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Approver User @relation(name: "remoteApprover", fields: [aproverId], references: [id], onDelete: Cascade)
  User     User @relation(name: "remoteApprovUser", fields: [userId], references: [id], onDelete: Cascade)

  @@id([aproverId, userId])
}

model RemoteApproverDep {
  aproverId    String
  departmentId String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Approver   User       @relation(fields: [aproverId], references: [id], onDelete: Cascade)
  Department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  @@id([aproverId, departmentId])
}

model RemoteApproverCom {
  aproverId String
  companyId String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Approver User    @relation(fields: [aproverId], references: [id], onDelete: Cascade)
  Company  Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@id([aproverId, companyId])
}

model RemoteApproverOrg {
  aproverId String
  orgId     String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Approver     User         @relation(fields: [aproverId], references: [id], onDelete: Cascade)
  Organization Organization @relation(fields: [orgId], references: [id], onDelete: Cascade)

  @@id([aproverId, orgId])
}

model RemoteFaceResultLimit {
  id           String    @id @default(uuid())
  userId       String?
  departmentId String?
  companyId    String?
  orgId        String?
  timesLimit   Int?
  dateLimit    DateTime?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User         User?         @relation(fields: [userId], references: [id], onDelete: SetNull)
  Department   Department?   @relation(fields: [departmentId], references: [id], onDelete: SetNull)
  Company      Company?      @relation(fields: [companyId], references: [id], onDelete: SetNull)
  Organization Organization? @relation(fields: [orgId], references: [id], onDelete: SetNull)
}

model RemoteFaceResult {
  id           Int      @id @default(autoincrement())
  userId       String
  guestId      String?
  userName     String   @default("")
  trackingId   String
  deviceId     String
  type         Int      @default(2)
  time         DateTime
  image        String
  cameraIp     String
  status       Int
  longtitude   String?
  latitude     String?
  faceResultId Int?

  detail String @default("")

  isClusterSync                  Boolean                          @default(false)
  dateCreated                    DateTime                         @default(now())
  dateModified                   DateTime                         @default(now()) @updatedAt
  User                           User                             @relation(fields: [userId], references: [id], onDelete: Cascade)
  Guest                          Guest?                           @relation(fields: [guestId], references: [id], onDelete: SetNull)
  FaceResult                     FaceResult?                      @relation(fields: [faceResultId], references: [id], onDelete: SetNull)
  RemoteFaceResultApproveHistory RemoteFaceResultApproveHistory[]

  @@index([time])
}

model RemoteFaceResultApproveHistory {
  id                 Int     @id @default(autoincrement())
  remoteFaceResultId Int
  approverId         String?
  oldStatus          Int
  newStatus          Int

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  RemoteFaceResult RemoteFaceResult @relation(fields: [remoteFaceResultId], references: [id], onDelete: Cascade)
  Approver         User?            @relation(fields: [approverId], references: [id], onDelete: SetNull)
}

model RemoteIgnoreApproveUsers {
  userId String @id

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

//Face-results integration
model FaceResultIntegrationConfigs {
  endpointType Int     @id
  description  String?
  isActive     Boolean @default(true)
  jsonConfig   String?

  isClusterSync               Boolean                       @default(false)
  dateCreated                 DateTime                      @default(now())
  dateModified                DateTime                      @default(now()) @updatedAt
  FaceResultIntegrationResult FaceResultIntegrationResult[]
}

model FaceResultIntegrationQueue {
  id                Int  @id @default(autoincrement())
  faceResultId      Int?
  guestFaceResultId Int?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  FaceResult                  FaceResult?                   @relation(fields: [faceResultId], references: [id], onDelete: Cascade)
  GuestFaceResult             GuestFaceResult?              @relation(fields: [guestFaceResultId], references: [id], onDelete: Cascade)
  FaceResultIntegrationResult FaceResultIntegrationResult[]
}

model FaceResultIntegrationResult {
  queueId      Int
  endpointType Int

  bodyData            String?
  sendRequestTime     DateTime?
  receiveResponseTime DateTime?
  isSend              Boolean   @default(false)
  isSuccess           Boolean   @default(false)
  lastResponse        String?
  isClusterSync       Boolean   @default(false)
  dateCreated         DateTime  @default(now())
  dateModified        DateTime  @default(now()) @updatedAt

  FaceResultIntegrationQueue  FaceResultIntegrationQueue   @relation(fields: [queueId], references: [id], onDelete: Cascade)
  FaceResultIntegrationConfig FaceResultIntegrationConfigs @relation(fields: [endpointType], references: [endpointType], onDelete: Cascade)

  @@id([queueId, endpointType])
}

//Security events
model FaceSercurityResult {
  id            Int      @id @default(autoincrement())
  trackingId    String
  type          Int
  time          DateTime
  image         String
  cameraIp      String
  cameraId      String?
  deviceId      String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

//New (beta) Access control
model AccessControl {
  id       String  @id @default(uuid())
  name     String
  isEnable Boolean @default(true)

  userNumberRequired   Int     @default(1)
  multipleUserWaitTime Int     @default(10000)
  isUseAdvance         Boolean @default(false)

  doorHoldTime Int @default(3000)

  bindingCompanyId    String?
  BindingCompany      Company?      @relation(fields: [bindingCompanyId], references: [id], onDelete: SetNull)
  bindingDepartmentId String?
  BindingDepartment   Department?   @relation(fields: [bindingDepartmentId], references: [id], onDelete: SetNull)
  bindingOrgId        String?
  BindingOrg          Organization? @relation(fields: [bindingOrgId], references: [id], onDelete: SetNull)

  isClusterSync                          Boolean                                  @default(false)
  dateCreated                            DateTime                                 @default(now())
  dateModified                           DateTime                                 @default(now()) @updatedAt
  AccessControlUser                      AccessControlUser[]
  AccessControlDevice                    AccessControlDevice[]
  AccessControlTime                      AccessControlTime[]
  AccessControlDepartment                AccessControlDepartment[]
  AccessControlCompany                   AccessControlCompany[]
  AccessControlOrg                       AccessControlOrg[]
  AccessControlDeviceGroup               AccessControlDeviceGroup[]
  AccessControlCamera                    AccessControlCamera[]
  AccessControlUserWaitting              AccessControlUserWaitting[]
  AlertMessage                           AlertMessage[]
  AccessControlOpenLog                   AccessControlOpenLog[]
  SystemLog                              SystemLog[]
  AccessControlGuest                     AccessControlGuest[]
  SecurityFaceDailyByAccessControlReport SecurityFaceDailyByAccessControlReport[]
}

model AccessControlOpenLog {
  openDeviceId String
  localId      String
  time         DateTime
  isOpen       Boolean
  type         Int
  openUserIds  String[]

  isValidScan    Boolean  @default(false)
  isValid        Boolean?
  scanUserIds    String[]
  invalidUserIds String[]
  acId           String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl? @relation(fields: [acId], references: [id], onDelete: SetNull)
  AlertMessage  AlertMessage[]

  @@id([openDeviceId, localId])
}

model AccessControlInOutCountLog {
  deviceId String
  localId  String
  cameraId String
  roiId    String
  time     DateTime
  type     Int

  dateCreated  DateTime @default(now())
  dateModified DateTime @default(now()) @updatedAt

  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([deviceId, localId])
}

model AccessControlUser {
  accessControlId String
  userId          String
  isEnable        Boolean @default(true)
  userRank        Int?

  replaceUserId String?
  startTime     DateTime?
  endTime       DateTime?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  User          User          @relation(name: "acUser", fields: [userId], references: [id], onDelete: Cascade)
  Replacer      User?         @relation(name: "acReplacer", fields: [replaceUserId], references: [id], onDelete: SetNull)

  @@id([accessControlId, userId])
}

model AccessControlGuest {
  accessControlId String
  guestId         String
  isEnable        Boolean @default(true)

  startTime DateTime?
  endTime   DateTime?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  Guest         Guest         @relation(fields: [guestId], references: [id], onDelete: Cascade)

  @@id([accessControlId, guestId])
}

model AccessControlUserLog {
  id            String   @id @default(uuid())
  acId          String?
  userId        String?
  time          DateTime
  checkType     Int?
  faceResultId  Int?
  guestResultId Int?

  isValid     Boolean @default(false)
  invalidType Int?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AlertMessage AlertMessage[]
  FaceResult   FaceResult?      @relation(fields: [faceResultId], references: [id], onDelete: SetNull)
  GuestResult  GuestFaceResult? @relation(fields: [guestResultId], references: [id], onDelete: SetNull)
}

model AccessControlUserWaitting {
  accessControlId String
  userId          String
  isEnable        Boolean @default(true)
  isProcessed     Boolean @default(false)
  userRank        Int?

  replaceUserId String?
  startTime     DateTime?
  endTime       DateTime?

  actionType  Int?
  invalidType Int     @default(-1)
  isSentAlert Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl  @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  User          User           @relation(name: "acUserWaitting", fields: [userId], references: [id], onDelete: Cascade)
  Replacer      User?          @relation(name: "acReplacerWaitting", fields: [replaceUserId], references: [id], onDelete: SetNull)
  AlertMessage  AlertMessage[]

  @@id([accessControlId, userId])
}

model AccessControlDepartment {
  accessControlId String
  departmentId    String
  isEnable        Boolean @default(true)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  Department    Department    @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  @@id([accessControlId, departmentId])
}

model AccessControlCompany {
  accessControlId String
  companyId       String
  isEnable        Boolean @default(true)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  Company       Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@id([accessControlId, companyId])
}

model AccessControlOrg {
  accessControlId String
  orgId           String
  isEnable        Boolean @default(true)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  Org           Organization  @relation(fields: [orgId], references: [id], onDelete: Cascade)

  @@id([accessControlId, orgId])
}

model AccessControlDevice {
  accessControlId String
  deviceId        String
  isEnable        Boolean @default(true)

  inOutMode Int?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  Device        Device        @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([accessControlId, deviceId])
}

model AccessControlDeviceGroup {
  accessControlId   String
  deviceGroupInfoId String
  isEnable          Boolean @default(true)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl   AccessControl   @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  DeviceGroupInfo DeviceGroupInfo @relation(fields: [deviceGroupInfoId], references: [id], onDelete: Cascade)

  @@id([accessControlId, deviceGroupInfoId])
}

model AccessControlCamera {
  accessControlId String
  cameraId        String
  isEnable        Boolean @default(true)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
  Camera        Camera        @relation(fields: [cameraId], references: [id], onDelete: Cascade)

  @@id([accessControlId, cameraId])
}

model AccessControlTime {
  id              String    @id @default(uuid())
  accessControlId String
  name            String
  startDate       DateTime?
  endDate         DateTime?
  startTime       String?
  endTime         String?
  weekDays        Int[]
  isEnable        Boolean   @default(true)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AccessControl AccessControl @relation(fields: [accessControlId], references: [id], onDelete: Cascade)
}

//Ac location (old access control)
model AcLocation {
  id               String             @id @default(uuid())
  name             String             @unique
  status           ActiveStatus       @default(ACTIVE)
  isClusterSync    Boolean            @default(false)
  isAlwayOpen      Boolean            @default(false)
  dateCreated      DateTime           @default(now())
  dateModified     DateTime           @default(now()) @updatedAt
  AcAdmin          AcAdmin[]
  AcDevice         AcDevice[]
  AcDepartmentTime AcDepartmentTime[]
  AcUserTime       AcUserTime[]
  AcPatientTime    AcPatientTime[]
  AcGuestTime      AcGuestTime[]
  AcGuest          AcGuest[]
  AcCamera         AcCamera[]
}

model AcAdmin {
  acLocationId  String
  userId        String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt

  AcLocation AcLocation @relation(fields: [acLocationId], references: [id], onDelete: Cascade)
  Admin      User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, acLocationId])
}

model AcDevice {
  deviceId      String
  acLocationId  String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt
  AcLocation    AcLocation   @relation(fields: [acLocationId], references: [id], onDelete: Cascade)
  Device        Device       @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([deviceId, acLocationId])
}

model AcCamera {
  cameraId      String
  acLocationId  String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt

  Camera     Camera     @relation(fields: [cameraId], references: [id], onDelete: Cascade)
  AcLocation AcLocation @relation(fields: [acLocationId], references: [id], onDelete: Cascade)

  @@id([cameraId, acLocationId])
}

model AcTime {
  id               String             @id @default(uuid())
  name             String
  startDate        DateTime
  endDate          DateTime
  startHour        Int
  startMin         Int                @default(0)
  startSec         Int                @default(0)
  endHour          Int
  endMin           Int                @default(0)
  endSec           Int                @default(0)
  weekDays         Int[]
  status           ActiveStatus       @default(ACTIVE)
  timeZoneOffset   String             @default("+07:00")
  isClusterSync    Boolean            @default(false)
  dateCreated      DateTime           @default(now())
  dateModified     DateTime           @default(now()) @updatedAt
  AcDepartmentTime AcDepartmentTime[]
  AcUserTime       AcUserTime[]
  AcPatientTime    AcPatientTime[]
  AcGuestTime      AcGuestTime[]
}

model AcDepartmentTime {
  acLocationId  String
  departmentId  String
  acTimeId      String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt
  AcLocation    AcLocation   @relation(fields: [acLocationId], references: [id], onDelete: Cascade)
  Department    Department   @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  AcTime        AcTime       @relation(fields: [acTimeId], references: [id], onDelete: Cascade)

  @@id([acLocationId, departmentId, acTimeId])
}

model AcUserTime {
  acLocationId  String
  userId        String
  acTimeId      String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt
  AcLocation    AcLocation   @relation(fields: [acLocationId], references: [id], onDelete: Cascade)
  User          User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  AcTime        AcTime       @relation(fields: [acTimeId], references: [id], onDelete: Cascade)

  @@id([acLocationId, userId, acTimeId])
}

model AcGuestTime {
  acLocationId  String
  guestId       String
  acTimeId      String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt
  AcLocation    AcLocation   @relation(fields: [acLocationId], references: [id], onDelete: Cascade)
  AcTime        AcTime       @relation(fields: [acTimeId], references: [id], onDelete: Cascade)
  Guest         Guest        @relation(fields: [guestId], references: [id], onDelete: Cascade)

  @@id([acLocationId, guestId, acTimeId])
}

model AcPatientTime {
  id            String       @id @default(uuid())
  acLocationId  String
  patientId     String
  acTimeId      String
  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt
  AcLocation    AcLocation   @relation(fields: [acLocationId], references: [id], onDelete: Cascade)
  AcTime        AcTime       @relation(fields: [acTimeId], references: [id], onDelete: Cascade)
  Patient       Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model AcGuest {
  acLocationId String
  guestId      String

  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt

  AcLocation AcLocation @relation(fields: [acLocationId], references: [id], onDelete: Cascade)
  Guest      Guest      @relation(fields: [guestId], references: [id], onDelete: Cascade)

  @@id([acLocationId, guestId])
}

//User-shift
model ShiftType {
  code             String         @id
  description      String?
  totalWorkHour    Float
  startHour        String         @default("00:00")
  endHour          String         @default("00:00")
  breakCode        String         @default("")
  startBreak       String         @default("00:00")
  endBreak         String         @default("00:00")
  breakwithPaid    Float          @default(0)
  breakwithoutPaid Float          @default(0)
  breakBonus       Int            @default(0)
  actualWorkHour   Int            @default(0)
  overnight        Float          @default(0)
  work             Int            @default(0)
  autoDays         String?
  isClusterSync    Boolean        @default(false)
  dateCreated      DateTime       @default(now())
  dateModified     DateTime       @default(now()) @updatedAt
  ShiftUser        ShiftUser[]
  DefaultShift     DefaultShift[]
}

model ShiftUserVip {
  userId String @id

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model ShiftUser {
  userId       String
  shiftId      String
  startDate    DateTime  @default(now())
  endDate      DateTime  @default(now())
  checkinTime  DateTime?
  checkinKey   Int?
  checkoutTime DateTime?
  checkoutKey  Int?

  isLate        Boolean @default(true)
  lateSecs      Int     @default(0)
  isEarly       Boolean @default(true)
  earlySecs     Int     @default(0)
  totalInvalid  Int     @default(2)
  shiftWorkTime Float   @default(0)

  isSendEarlyNoti      Boolean @default(false)
  isSendLateNoti       Boolean @default(false)
  isSendNoCheckInNoti  Boolean @default(false)
  isSendNoCheckOutNoti Boolean @default(false)

  User          User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  ShiftType     ShiftType   @relation(fields: [shiftId], references: [code], onDelete: Cascade)
  isClusterSync Boolean     @default(false)
  dateCreated   DateTime    @default(now())
  dateModified  DateTime    @default(now()) @updatedAt
  syncInStatus  Boolean     @default(false)
  syncInTime    DateTime?
  syncInDetail  String      @default("")
  syncOutStatus Boolean     @default(false)
  syncOutTime   DateTime?
  syncOutDetail String      @default("")
  FaceResultIn  FaceResult? @relation(name: "in", fields: [checkinKey], references: [id], onDelete: Cascade)
  FaceResultOut FaceResult? @relation(name: "out", fields: [checkoutKey], references: [id], onDelete: Cascade)

  @@id([userId, shiftId, startDate, endDate])
  @@index([shiftId, startDate, endDate])
}

model CalculatedWorkMonth {
  userId        String
  cycleDate     String
  year          Int
  month         Int
  days          String
  congPhep      Float
  phatTien      Float
  tongCong      Float
  congThucTe    Float
  congCheDo     Float
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, cycleDate])
  @@index([year, month])
}

model CalculatedWorkDay {
  userId        String
  year          Int
  month         Int
  day           String
  displayString String
  congPhep      Float
  phatTien      Float
  tongCong      Float
  congThucTe    Float
  congCheDo     Float
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, day])
  @@index([year, month, day])
}

model ShiftUSerExplainImported {
  id            String   @id
  sfTimeOffId   Int?
  userId        String
  startDate     DateTime
  endDate       DateTime
  startTime     DateTime
  endTime       DateTime
  timeType      String
  timeTypeName  String
  status        Int      @default(0)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model DefaultShift {
  id            String   @id @default(uuid())
  shiftId       String
  userId        String?
  departmentId  String?
  companyId     String?
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  ShiftType  ShiftType   @relation(fields: [shiftId], references: [code], onDelete: Cascade)
  User       User?       @relation(fields: [userId], references: [id], onDelete: Cascade)
  Department Department? @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  Company    Company?    @relation(fields: [companyId], references: [id], onDelete: Cascade)
}

model ShiftUserExplainAdmin {
  id           String  @id @default(uuid())
  userId       String
  companyId    String?
  departmentId String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Admin      User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  Company    Company?    @relation(fields: [companyId], references: [id], onDelete: Cascade)
  Department Department? @relation(fields: [departmentId], references: [id], onDelete: Cascade)
}

model ShiftUserExplain {
  id               String   @id @default(uuid())
  userId           String
  startExplainTime DateTime
  endExplainTime   DateTime
  explainType      Int
  explainText      String?
  status           Int      @default(0)
  isClusterSync    Boolean  @default(false)
  dateCreated      DateTime @default(now())
  dateModified     DateTime @default(now()) @updatedAt

  User                   User                     @relation(fields: [userId], references: [id], onDelete: Cascade)
  ShiftUserExplainImages ShiftUserExplainImages[]
}

model ShiftUserExplainImages {
  id                 String           @id @default(uuid())
  shiftUserExplainId String
  path               String
  isClusterSync      Boolean          @default(false)
  dateCreated        DateTime         @default(now())
  dateModified       DateTime         @default(now()) @updatedAt
  ShiftUserExplain   ShiftUserExplain @relation(fields: [shiftUserExplainId], references: [id], onDelete: Cascade)
}

model DayInvalidReason {
  userId     String
  day        String
  type       Int
  reasonType Int
  note       String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, day, type])
}

model ShiftUserReportByDay {
  id              String  @id
  date            String
  userId          String?
  departmentId    String?
  companyId       String?
  totalLateTimes  Int     @default(0)
  totalLateMins   Int     @default(0)
  totalEarlyTimes Int     @default(0)
  totalEarlyMins  Int     @default(0)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User       User?       @relation(fields: [userId], references: [id], onDelete: Cascade)
  Department Department? @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  Company    Company?    @relation(fields: [companyId], references: [id], onDelete: Cascade)
}

//Face result scheduler report
model FaceResultUserReport {
  userId         String
  day            String
  timeZoneOffset String   @default("+07:00")
  checkInTimes   Int      @default(0)
  checkOutTimes  Int      @default(0)
  undefinedTimes Int      @default(0)
  firstInTime    String?
  lastOutTime    String?
  isClusterSync  Boolean  @default(false)
  dateCreated    DateTime @default(now())
  dateModified   DateTime @default(now()) @updatedAt
  User           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, day, timeZoneOffset])
  @@index([userId, day])
}

model FaceResultDailyReport {
  id             Int      @id @default(autoincrement())
  timeZoneOffset String   @default("+07:00")
  day            DateTime
  totalCheckIn   Int      @default(0)
  totalCheckOut  Int      @default(0)
  totalUndefine  Int      @default(0)
  isClusterSync  Boolean  @default(false)
  dateCreated    DateTime @default(now())
  dateModified   DateTime @default(now()) @updatedAt

  @@index([day])
}

model FaceResultHourReport {
  id             Int      @id @default(autoincrement())
  timeZoneOffset String   @default("+07:00")
  day            DateTime
  hour           Int
  totalCheckIn   Int      @default(0)
  totalCheckOut  Int      @default(0)
  totalUndefine  Int      @default(0)
  isClusterSync  Boolean  @default(false)
  dateCreated    DateTime @default(now())
  dateModified   DateTime @default(now()) @updatedAt

  @@index([day, hour])
}

model UserTimeReport {
  userId        String
  date          String
  User          User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  workTime      Float   @default(0)
  checkInTimes  Int     @default(0)
  checkOutTimes Int     @default(0)
  unknownTimes  Int     @default(0)
  firstInTime   String?
  lastOutTime   String?

  isAllShiftValid   Boolean @default(false)
  shiftExplainCount Int     @default(0)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@id([userId, date])
  @@index([userId, date])
}

model CustomeHoliday {
  day           DateTime @id
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model CustomeReportJob {
  id             String   @id @default(uuid())
  companyIds     String?
  departmentIds  String?
  userIds        String?
  userFilterId   String?
  totalUser      Int      @default(0)
  processedUser  Int      @default(0)
  reportFileName String
  isRunning      Boolean  @default(true)
  isClusterSync  Boolean  @default(false)
  dateCreated    DateTime @default(now())
  dateModified   DateTime @default(now()) @updatedAt

  UserFilter ReportUserFilter? @relation(fields: [userFilterId], references: [id], onDelete: Cascade)
}

model ReportUserFilter {
  id                         String                       @id @default(uuid())
  name                       String
  isClusterSync              Boolean                      @default(false)
  dateCreated                DateTime                     @default(now())
  dateModified               DateTime                     @default(now()) @updatedAt
  CustomeReportJob           CustomeReportJob[]
  ReportUserFilterCompany    ReportUserFilterCompany[]
  ReportUserFilterDepartment ReportUserFilterDepartment[]
  ReportUserFilterUser       ReportUserFilterUser[]
  ReportUserFilterExUser     ReportUserFilterExUser[]
  ReportUserFilterExRuleUser ReportUserFilterExRuleUser[]
  ReportUserFilterResultUser ReportUserFilterResultUser[]
}

model ReportUserFilterCompany {
  filterId         String
  companyId        String
  isClusterSync    Boolean          @default(false)
  dateCreated      DateTime         @default(now())
  dateModified     DateTime         @default(now()) @updatedAt
  ReportUserFilter ReportUserFilter @relation(fields: [filterId], references: [id], onDelete: Cascade)
  Company          Company          @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@id([filterId, companyId])
}

model ReportUserFilterDepartment {
  filterId         String
  departmentId     String
  isClusterSync    Boolean          @default(false)
  dateCreated      DateTime         @default(now())
  dateModified     DateTime         @default(now()) @updatedAt
  ReportUserFilter ReportUserFilter @relation(fields: [filterId], references: [id], onDelete: Cascade)
  Department       Department       @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  @@id([filterId, departmentId])
}

model ReportUserFilterUser {
  filterId         String
  userId           String
  isClusterSync    Boolean          @default(false)
  dateCreated      DateTime         @default(now())
  dateModified     DateTime         @default(now()) @updatedAt
  ReportUserFilter ReportUserFilter @relation(fields: [filterId], references: [id], onDelete: Cascade)
  User             User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([filterId, userId])
}

model ReportUserFilterExUser {
  filterId         String
  userId           String
  isClusterSync    Boolean          @default(false)
  dateCreated      DateTime         @default(now())
  dateModified     DateTime         @default(now()) @updatedAt
  ReportUserFilter ReportUserFilter @relation(fields: [filterId], references: [id], onDelete: Cascade)
  User             User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([filterId, userId])
}

model ReportUserFilterExRuleUser {
  filterId         String
  userId           String
  isClusterSync    Boolean          @default(false)
  dateCreated      DateTime         @default(now())
  dateModified     DateTime         @default(now()) @updatedAt
  ReportUserFilter ReportUserFilter @relation(fields: [filterId], references: [id], onDelete: Cascade)
  User             User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([filterId, userId])
}

model ReportUserFilterResultUser {
  filterId         String
  userId           String
  isClusterSync    Boolean          @default(false)
  dateCreated      DateTime         @default(now())
  dateModified     DateTime         @default(now()) @updatedAt
  ReportUserFilter ReportUserFilter @relation(fields: [filterId], references: [id], onDelete: Cascade)
  User             User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([filterId, userId])
}

//Mobile notification & alert
model Mobile {
  id                     Int      @id @default(autoincrement())
  userId                 String
  operatingSystem        String
  operatingSystemVersion Int
  deviceName             String
  tokenType              Int
  token                  String
  status                 Int
  isClusterSync          Boolean  @default(false)
  dateCreated            DateTime @default(now())
  dateModified           DateTime @default(now()) @updatedAt
}

model Notification {
  id            Int         @id @default(autoincrement())
  userId        String
  message       String
  FaceResultId  Int?
  dateCreated   DateTime    @default(now())
  dateModified  DateTime    @default(now()) @updatedAt
  isClusterSync Boolean     @default(false)
  User          User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  FaceResult    FaceResult? @relation(fields: [FaceResultId], references: [id], onDelete: Cascade)

  @@index([userId, dateCreated, dateModified])
}

model AlertRule {
  id            String         @id @default(uuid())
  type          Int
  alertLevel    Int
  name          String
  description   String         @default("Aleart rule")
  isRangeLimit  Boolean        @default(false)
  isTimeLimit   Boolean        @default(false)
  startDate     DateTime?
  endDate       DateTime?
  startHour     String?
  endHour       String?
  weekDays      String?
  isClusterSync Boolean        @default(false)
  dateCreated   DateTime       @default(now())
  dateModified  DateTime       @default(now()) @updatedAt
  AlertUser     AlertUser[]
  AlertMessage  AlertMessage[]
  AlertScript   AlertScript[]
}

model AlertScript {
  type          Int
  alertRuleId   String
  receiverEmail String
  receiverId    String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AlertRule AlertRule @relation(fields: [alertRuleId], references: [id], onDelete: Cascade)

  @@id([type, alertRuleId, receiverEmail, receiverId])
}

model AlertUser {
  alertRuleId   String
  userId        String
  isActive      Boolean        @default(true)
  isClusterSync Boolean        @default(false)
  dateCreated   DateTime       @default(now())
  dateModified  DateTime       @default(now()) @updatedAt
  AlertRule     AlertRule      @relation(fields: [alertRuleId], references: [id], onDelete: Cascade)
  User          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  AlertMessage  AlertMessage[]

  @@id([alertRuleId, userId])
}

model AlertMessage {
  id                     Int      @id @default(autoincrement())
  messeage               String
  time                   DateTime
  alertRuleId            String
  userId                 String?
  deviceId               String?
  faceResultId           Int?
  orphanFaceResultId     Int?
  accessControlId        String?
  acUserId               String?
  accessControlUserLogId String?
  isRead                 Boolean  @default(false)

  accessControlOpenLogOpenDeviceId String?
  accessControlOpenLogLocalId      String?
  acAlertType                      Int?

  alertRegisUserId String?

  cachedRelationRawData String?
  cachedImagePath       String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AlertRule            AlertRule                  @relation(fields: [alertRuleId], references: [id], onDelete: Cascade)
  User                 User?                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  Device               Device?                    @relation(fields: [deviceId], references: [id], onDelete: SetNull)
  FaceResult           FaceResult?                @relation(fields: [faceResultId], references: [id], onDelete: Cascade)
  Unknown              OrphanFaceResult?          @relation(fields: [orphanFaceResultId], references: [id], onDelete: SetNull)
  AlertUser            AlertUser?                 @relation(fields: [alertRuleId, userId], references: [alertRuleId, userId], onDelete: Cascade)
  AccessControl        AccessControl?             @relation(fields: [accessControlId], references: [id], onDelete: SetNull)
  AcUserWaitting       AccessControlUserWaitting? @relation(fields: [accessControlId, acUserId], references: [accessControlId, userId], onDelete: SetNull)
  AccessControlUserLog AccessControlUserLog?      @relation(fields: [accessControlUserLogId], references: [id], onDelete: SetNull)
  AccessControlOpenLog AccessControlOpenLog?      @relation(fields: [accessControlOpenLogOpenDeviceId, accessControlOpenLogLocalId], references: [openDeviceId, localId], onDelete: SetNull)

  @@index([time, userId])
}

//SQL integration
model SQLServerIntegrationConfig {
  id              String                      @id @default(uuid())
  isActive        Boolean                     @default(true)
  user            String                      @default("vmec.faceid")
  password        String                      @default("QTXdv2f64y6WRQsZ")
  dbName          String                      @default("VG_FINGERPRINT")
  server          String                      @default("10.100.20.26")
  port            Int?
  responseModelId String?
  tbl             String                      @default("VIN_CENTRALIZED")
  syncTable       SqlSyncMode                 @default(SHIFT_USER)
  Template        FaceResultApiResponseModel? @relation(fields: [responseModelId], references: [id], onDelete: SetNull)
}

model SynchronizedShiftUserData {
  PDSNR         Int      @default(autoincrement())
  ERDAT         String
  PERNR         String
  ERTIM         String
  SATZA         String
  TERID         String
  LDATE         String?
  LTIME         String?
  ULDATE        String?
  ULTIME        String?
  IP            String
  LOCATION      String?
  isUpdated     Boolean  @default(false)
  detail        String?
  userId        String
  shiftId       String
  startDate     DateTime @default(now())
  endDate       DateTime @default(now())
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@id([PDSNR, ERDAT])
}

//Active domain - LDAP
model ActiveDomainConfig {
  id                   String   @id @default(uuid())
  isActive             Boolean  @default(true)
  url                  String
  port                 Int      @default(389)
  OU                   String   @default("OU=vinmec,DC=vingroup,DC=local")
  filter               String   @default("sAMAccountName")
  userInfoUrl          String?  @default("https://api.vingroup.net/api/sap/hcm/v1/ws/backend/hrms/employee/profile/basic")
  shiftUserInfoUrl     String?  @default("https://api.vingroup.net/api/sap/hcm/v1/ws/backend/hrms/employee/timeoverview")
  isAutoPullShiftUser  Boolean? @default(true)
  clientId             String?  @default("180bd5a8490b4f53afa8ef7755734200")
  clientSecret         String?  @default("D90378a2E02847F28a59E11515901678")
  apiKey               String?  @default("VmECF1dY83DkGa12")
  adminUser            String?
  adminPass            String?
  userSearchConfig     String?
  userSearchFilter     String?
  userSearchScope      String?
  userSearchAttributes String?
}

//Web layout
model WebLayout {
  roleId        RoleType
  path          String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  WebPage       WebPage  @relation(fields: [path], references: [path], onDelete: Cascade)

  @@id([roleId, path])
}

model WebPage {
  path          String      @id
  label         String
  icon          String
  index         Int
  isCollapsible Boolean     @default(false)
  isNoTimeLimit Boolean     @default(false)
  isClusterSync Boolean     @default(false)
  dateCreated   DateTime    @default(now())
  dateModified  DateTime    @default(now()) @updatedAt
  WebLayout     WebLayout[]
}

//System logs
model SystemLog {
  id             String     @id @default(uuid())
  userId         String?
  affectedUserId String?
  function       FunctionId
  changeType     String
  tableName      String
  changeNote     String     @default("")
  oldValue       String?
  newValue       String?

  label String?
  acId  String?

  dateCreated   DateTime       @default(now())
  dateModified  DateTime       @default(now()) @updatedAt
  isClusterSync Boolean        @default(false)
  User          User?          @relation(name: "systemLogActor", fields: [userId], references: [id], onDelete: SetNull)
  AffectedUser  User?          @relation(name: "systemLogAffect", fields: [affectedUserId], references: [id], onDelete: SetNull)
  AccessControl AccessControl? @relation(fields: [acId], references: [id], onDelete: SetNull)

  @@index([dateCreated, dateModified, userId])
}

model ServerUsageLog {
  id            Int      @id @default(autoincrement())
  cpuUsage      Int
  totalMem      Int
  freeMem       Int
  totalDisk     Int
  freeDisk      Int
  uptime        Int
  time          DateTime
  isClusterSync Boolean  @default(false)

  @@index([time])
}

//User attandant
model AttandantTime {
  id                 String               @id @default(uuid())
  name               String
  startHour          String
  endHour            String
  isNoTimeLimit      Boolean              @default(true)
  isNoUserLimit      Boolean              @default(true)
  isActive           Boolean              @default(true)
  isClusterSync      Boolean              @default(false)
  dateCreated        DateTime             @default(now())
  dateModified       DateTime             @default(now()) @updatedAt
  UserAttandantTime  UserAttandantTime[]
  DeviceAttandatTime DeviceAttandatTime[]
}

model DeviceAttandatTime {
  deviceId        String
  attandantTimeId String
  isClusterSync   Boolean       @default(false)
  dateCreated     DateTime      @default(now())
  dateModified    DateTime      @default(now()) @updatedAt
  AttandantTime   AttandantTime @relation(fields: [attandantTimeId], references: [id], onDelete: Cascade)
  Device          Device        @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([deviceId, attandantTimeId])
}

model UserAttandantTime {
  userId        String
  attandantId   String
  startDate     DateTime
  endDate       DateTime
  isActive      Boolean       @default(true)
  isChecked     Boolean       @default(false)
  checkTime     DateTime?
  isClusterSync Boolean       @default(false)
  dateCreated   DateTime      @default(now())
  dateModified  DateTime      @default(now()) @updatedAt
  User          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  AttandantTime AttandantTime @relation(fields: [attandantId], references: [id], onDelete: Cascade)

  @@id([userId, attandantId, startDate, endDate])
}

model AttandantExplain {
  id            Int      @id @default(autoincrement())
  userId        String
  detail        String   @default("")
  time          DateTime
  type          Int      @default(2)
  status        Int      @default(0)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  User          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model AttandantModify {
  id            Int      @id @default(autoincrement())
  faceResultId  Int
  userId        String
  detail        String   @default("")
  type          Int      @default(2)
  status        Int      @default(0)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  User          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

//New attandant tables
model AttInfo {
  id        String   @id @default(uuid())
  name      String
  startTime DateTime
  endTime   DateTime

  isClusterSync Boolean     @default(false)
  dateCreated   DateTime    @default(now())
  dateModified  DateTime    @default(now()) @updatedAt
  AttDevice     AttDevice[]
  AttUser       AttUser[]
  AttGuest      AttGuest[]
}

model AttDevice {
  id        String  @id @default(uuid())
  attInfoId String
  deviceId  String?
  cameraId  String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  AttInfo AttInfo @relation(fields: [attInfoId], references: [id], onDelete: Cascade)
  Device  Device? @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  Camera  Camera? @relation(fields: [cameraId], references: [id], onDelete: Cascade)
}

model AttUser {
  userId    String
  attInfoId String

  isChecked    Boolean @default(false)
  faceResultId Int?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  AttInfo    AttInfo     @relation(fields: [attInfoId], references: [id], onDelete: Cascade)
  FaceResult FaceResult? @relation(fields: [faceResultId], references: [id], onDelete: Cascade)

  @@id([userId, attInfoId])
}

model AttGuest {
  guestId   String
  attInfoId String

  isChecked     Boolean @default(false)
  guestResultId Int?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Guest           Guest            @relation(fields: [guestId], references: [id], onDelete: Cascade)
  AttInfo         AttInfo          @relation(fields: [attInfoId], references: [id], onDelete: Cascade)
  GuestFaceResult GuestFaceResult? @relation(fields: [guestResultId], references: [id], onDelete: SetNull)

  @@id([guestId, attInfoId])
}

//API integration
model APIIntegrationConfig {
  id            String   @id @default(uuid())
  isActive      Boolean  @default(true)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model KafkaIntegrationConfig {
  id              String                     @id @default(uuid())
  isActive        Boolean                    @default(true)
  isClusterSync   Boolean                    @default(false)
  brokers         String
  isSsl           Boolean
  saslMechanism   String
  username        String
  password        String
  producerTopic   String
  consumberTopic  String
  responseModelId String
  ResponseModel   FaceResultApiResponseModel @relation(fields: [responseModelId], references: [id], onDelete: Cascade)
  clientId        String?                    @default(uuid())
  groupId         String?                    @default(uuid())
  dateCreated     DateTime                   @default(now())
  dateModified    DateTime                   @default(now()) @updatedAt
}

model FaceResultApiResponseModel {
  id                         String                       @id @default(uuid())
  messageKeys                String                       @default("")
  messageValues              String                       @default("")
  defaultValues              String                       @default("")
  modifyValues               String                       @default("")
  isClusterSync              Boolean                      @default(false)
  dateCreated                DateTime                     @default(now())
  dateModified               DateTime                     @default(now()) @updatedAt
  KafkaIntegrationConfig     KafkaIntegrationConfig[]
  SQLServerIntegrationConfig SQLServerIntegrationConfig[]
}

//User-face-images quality
model FaceResultError {
  userId        String
  time          DateTime
  srcDeviceId   String?
  errorType     Int      @default(0)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  SrcDevice Device? @relation(fields: [srcDeviceId], references: [id], onDelete: Cascade)

  @@id([userId, time])
}

model FaceImageSearchEvent {
  id                    String                  @id @default(uuid())
  srcFaceImage          String
  totalImageSearch      Int
  startSearchDate       DateTime?
  endSearchDate         DateTime?
  isFinished            Boolean                 @default(false)
  isClusterSync         Boolean                 @default(false)
  dateCreated           DateTime                @default(now())
  dateModified          DateTime                @default(now()) @updatedAt
  FaceImageSearchResult FaceImageSearchResult[]
}

model FaceImageSearchResult {
  id                   Int                  @id @default(autoincrement())
  eventId              String
  unknownEventId       Int
  isFinished           Boolean              @default(false)
  confident            Float?
  isClusterSync        Boolean              @default(false)
  dateCreated          DateTime             @default(now())
  dateModified         DateTime             @default(now()) @updatedAt
  FaceImageSearchEvent FaceImageSearchEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  UnknownEvent         OrphanFaceResult     @relation(fields: [unknownEventId], references: [id], onDelete: Cascade)
}

model FaceImageRescanJob {
  id                String              @id @default(uuid())
  totalUser         Int
  totalImage        Int
  isFinished        Boolean             @default(false)
  isClusterSync     Boolean             @default(false)
  dateCreated       DateTime            @default(now())
  dateModified      DateTime            @default(now()) @updatedAt
  UserFaceImageScan UserFaceImageScan[]
}

model UserFaceImageScan {
  userId        String
  faceImageId   Int
  isQualified   Boolean
  errorType     String?
  rescanJobId   String?
  isClusterSync Boolean             @default(false)
  dateCreated   DateTime            @default(now())
  dateModified  DateTime            @default(now()) @updatedAt
  User          User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  FaceImage     UserFaceImage       @relation(fields: [faceImageId], references: [id], onDelete: Cascade)
  RescanJob     FaceImageRescanJob? @relation(fields: [rescanJobId], references: [id], onDelete: Cascade)

  @@id([userId, faceImageId])
}

//Human tracking
model CoreAiHumanTrackInfo {
  deviceId                  String
  cameraId                  String
  cameraIp                  String
  trackId                   String
  isClusterSync             Boolean                     @default(false)
  dateCreated               DateTime                    @default(now())
  dateModified              DateTime                    @default(now()) @updatedAt
  Device                    Device                      @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  CoreAiHumanTrackResult    CoreAiHumanTrackResult[]
  SmartSearchMctResult      SmartSearchMctResult[]
  CoreAiHumanTrackAttribute CoreAiHumanTrackAttribute[]

  @@id([deviceId, trackId])
  @@index([trackId, dateCreated, dateModified])
}

model CoreAiHumanTrackAttribute {
  deviceId      String
  trackId       String
  type          Int
  confident     Float                @default(0)
  isClusterSync Boolean              @default(false)
  dateCreated   DateTime             @default(now())
  dateModified  DateTime             @default(now()) @updatedAt
  TrackInfo     CoreAiHumanTrackInfo @relation(fields: [deviceId, trackId], references: [deviceId, trackId], onDelete: Cascade)

  @@id([deviceId, trackId, type])
  @@index([trackId, dateCreated, dateModified, confident])
}

model CoreAiHumanTrackResult {
  deviceId            String
  humanTrackId        String
  imageId             String
  path                String
  faceImagePath       String?
  isHaveFace          Boolean  @default(false)
  userIdReCheckResult String?
  time                DateTime
  index               Int
  bodyR               String?
  bodyG               String?
  bodyB               String?
  lowerR              String?
  lowerG              String?
  lowerB              String?
  headPercent         Float?
  bodyPercent         Float?
  lowerPercent        Float?

  uniform_conf       Float?
  emp_card_conf      Float?
  bad_kpt_head       Float?
  bad_kpt_body       Float?
  bad_kpt_lower      Float?
  male_conf          Float?
  female_conf        Float?
  child_conf         Float?
  adult_conf         Float?
  backpack_conf      Float?
  hat_conf           Float?
  long_hair_conf     Float?
  short_hair_conf    Float?
  long_sleeve_conf   Float?
  short_sleeve_conf  Float?
  trouser_jeans_conf Float?
  skirt_conf         Float?
  shorts_conf        Float?

  isClusterSync        Boolean                @default(false)
  dateCreated          DateTime               @default(now())
  dateModified         DateTime               @default(now()) @updatedAt
  TrackInfo            CoreAiHumanTrackInfo   @relation(fields: [deviceId, humanTrackId], references: [deviceId, trackId], onDelete: Cascade)
  SmartSearchMctResult SmartSearchMctResult[]
  UserRecheck          User?                  @relation(fields: [userIdReCheckResult], references: [id], onDelete: SetNull)

  @@id([deviceId, humanTrackId, imageId])
  @@index([humanTrackId, time])
}

model CachedPersonDetected {
  id         String   @id @default(uuid())
  time       DateTime
  trackingId String
  cameraId   String?
  deviceId   String
  image      String

  roiId String?

  isCoreAiSent     Boolean @default(false)
  isCoreAiResponse Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@index([time, trackingId])
}

//Human action tracking
model CoreAiActionResult {
  id          String   @id @default(uuid())
  deviceId    String
  cameraIp    String
  cameraId    String
  trackId     String
  action      Int
  confident   Float
  startTime   DateTime
  endTime     DateTime
  base64Image String

  listImage String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@index([startTime, endTime, confident])
}

model CoreAiAlarmEvent {
  id            String   @id @default(uuid())
  cameraIp      String
  cameraId      String
  deviceId      String
  trackId       String
  type          String
  base64Image   String?
  filePath      String?
  roiId         String?
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@index([trackId, dateCreated, dateModified])
}

//Auto smart search from folder
model AutoSmartSearchS3Config {
  id            String    @id @default(uuid())
  isEnable      Boolean   @default(true)
  bucketName    String
  prefix        String
  resizedPrefix String?
  lastScanTime  DateTime?

  isClusterSync      Boolean              @default(false)
  dateCreated        DateTime             @default(now())
  dateModified       DateTime             @default(now()) @updatedAt
  SmartSearchS3Files SmartSearchS3Files[]
}

model SmartSearchS3Files {
  s3ConfigId    String
  key           String
  resizedKey    String?
  smartSearchId String?
  isProcessing  Boolean @default(false)
  processed     Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  S3Config         AutoSmartSearchS3Config @relation(fields: [s3ConfigId], references: [id], onDelete: Cascade)
  SmartSearchEvent SmartSearchEvent?       @relation(fields: [smartSearchId], references: [id], onDelete: Cascade)

  @@id([s3ConfigId, key])
}

model AutoSmartSearchConfig {
  id                   String                 @id @default(uuid())
  scanFolderPath       String
  outputFolderPath     String
  searchMode           Int
  isClusterSync        Boolean                @default(false)
  dateCreated          DateTime               @default(now())
  dateModified         DateTime               @default(now()) @updatedAt
  AutoSmartSearchFiles AutoSmartSearchFiles[]
}

model AutoSmartSearchFiles {
  id                         String                       @id @default(uuid())
  time                       DateTime                     @default(now())
  fileName                   String
  resizedFile                String
  autoSmartSearchConfigId    String?
  outputFile                 String?
  isSendMqtt                 Boolean                      @default(false)
  isClusterSync              Boolean                      @default(false)
  dateCreated                DateTime                     @default(now())
  dateModified               DateTime                     @default(now()) @updatedAt
  AutoSmartSearchConfig      AutoSmartSearchConfig?       @relation(fields: [autoSmartSearchConfigId], references: [id], onDelete: SetNull)
  AutoSmartSearchFileUser    AutoSmartSearchFileUser[]
  SmartSearchFaceResult      SmartSearchFaceResult[]
  AutoSmartSearchFileTrackId AutoSmartSearchFileTrackId[]
}

model AutoSmartSearchFileTrackId {
  trackId       String
  fileId        String
  isClusterSync Boolean              @default(false)
  dateCreated   DateTime             @default(now())
  dateModified  DateTime             @default(now()) @updatedAt
  File          AutoSmartSearchFiles @relation(fields: [fileId], references: [id], onDelete: Cascade)

  @@id([trackId, fileId])
}

model AutoSmartSearchFileUser {
  userId        String
  fileId        String
  isClusterSync Boolean              @default(false)
  dateCreated   DateTime             @default(now())
  dateModified  DateTime             @default(now()) @updatedAt
  File          AutoSmartSearchFiles @relation(fields: [fileId], references: [id], onDelete: Cascade)
  User          User                 @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, fileId])
}

//Smart search
model SmartSearchEvent {
  id                                  String                                @id @default(uuid())
  srcImgPath                          String?
  resizedImgPath                      String?
  gender                              Int?
  maskFilter                          Int?
  thresholdNoMask                     Float                                 @default(0)
  thresholdMask                       Float                                 @default(0)
  upperColor                          String?
  lowerColor                          String?
  isBag                               Boolean?
  startTime                           DateTime
  endTime                             DateTime
  isSearchFace                        Boolean
  isSearchFeature                     Boolean
  cameraId                            String?
  isRunning                           Boolean                               @default(true)
  totalResult                         Int                                   @default(0)
  isClusterSync                       Boolean                               @default(false)
  dateCreated                         DateTime                              @default(now())
  dateModified                        DateTime                              @default(now()) @updatedAt
  MctResults                          SmartSearchMctResult[]
  FaceResults                         SmartSearchFaceResult[]
  SearchCamera                        Camera?                               @relation(fields: [cameraId], references: [id], onDelete: Cascade)
  SmartSearchUserResult               SmartSearchUserResult[]
  SmartSearchPersonCaptureFromRequest SmartSearchPersonCaptureFromRequest[]
  SmartSearchGuestResult              SmartSearchGuestResult[]
  SmartSearchS3Files                  SmartSearchS3Files[]
}

model SmartSearchPersonCaptureFromRequest {
  eventId String
  imageId String
  path    String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  SmartSearchEvent     SmartSearchEvent       @relation(fields: [eventId], references: [id], onDelete: Cascade)
  SmartSearchMctResult SmartSearchMctResult[]

  @@id([eventId, imageId])
}

model SmartSearchMctResult {
  eventId         String
  deviceId        String
  personCaptureId String
  trackId         String
  imageId         String
  cameraId        String
  cameraIp        String
  time            DateTime
  confidence      Float
  isClusterSync   Boolean  @default(false)
  dateCreated     DateTime @default(now())
  dateModified    DateTime @default(now()) @updatedAt

  SmartSearchEvent SmartSearchEvent                    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  PersonCaptured   SmartSearchPersonCaptureFromRequest @relation(fields: [eventId, personCaptureId], references: [eventId, imageId], onDelete: Cascade)
  HumanTrack       CoreAiHumanTrackInfo                @relation(fields: [deviceId, trackId], references: [deviceId, trackId], onDelete: Cascade)
  ResultImages     CoreAiHumanTrackResult              @relation(fields: [deviceId, trackId, imageId], references: [deviceId, humanTrackId, imageId], onDelete: Cascade)

  @@id([eventId, deviceId, personCaptureId, trackId, imageId])
}

model SmartSearchFaceCaptureFromRequest {
  eventId                String
  imageId                String
  path                   String
  isMask                 Boolean?
  isClusterSync          Boolean                  @default(false)
  dateCreated            DateTime                 @default(now())
  dateModified           DateTime                 @default(now()) @updatedAt
  SmartSearchFaceResult  SmartSearchFaceResult[]
  SmartSearchUserResult  SmartSearchUserResult[]
  SmartSearchGuestResult SmartSearchGuestResult[]

  @@id([eventId, imageId])
}

model SmartSearchFaceResult {
  eventId             String
  imageId             String
  similarity          Float
  cameraId            String
  cameraIp            String?
  time                DateTime
  isMask              Boolean                           @default(false)
  captureRequestId    String
  personId            String?
  personName          String?
  trackId             String?
  autoFileId          String?
  isClusterSync       Boolean                           @default(false)
  dateCreated         DateTime                          @default(now())
  dateModified        DateTime                          @default(now()) @updatedAt
  SmartSearchEvent    SmartSearchEvent                  @relation(fields: [eventId], references: [id], onDelete: Cascade)
  CaptureRequestImage SmartSearchFaceCaptureFromRequest @relation(fields: [eventId, captureRequestId], references: [eventId, imageId], onDelete: Cascade)
  AutoFile            AutoSmartSearchFiles?             @relation(fields: [autoFileId], references: [id], onDelete: SetNull)

  @@id([eventId, imageId])
}

model SmartSearchUserResult {
  eventId              String
  resultUserId         String
  userName             String
  userId               String?
  userMaskConfidence   Float
  userNoMaskConfidence Float
  captureRequestId     String
  SmartSearchEvent     SmartSearchEvent                  @relation(fields: [eventId], references: [id], onDelete: Cascade)
  User                 User?                             @relation(fields: [userId], references: [id], onDelete: Cascade)
  CaptureRequestImage  SmartSearchFaceCaptureFromRequest @relation(fields: [eventId, captureRequestId], references: [eventId, imageId], onDelete: Cascade)

  @@id([eventId, resultUserId])
}

model SmartSearchGuestResult {
  eventId              String
  resultUserId         String
  userName             String
  guestId              String?
  userMaskConfidence   Float
  userNoMaskConfidence Float
  captureRequestId     String
  SmartSearchEvent     SmartSearchEvent                  @relation(fields: [eventId], references: [id], onDelete: Cascade)
  Guest                Guest?                            @relation(fields: [guestId], references: [id], onDelete: Cascade)
  CaptureRequestImage  SmartSearchFaceCaptureFromRequest @relation(fields: [eventId, captureRequestId], references: [eventId, imageId], onDelete: Cascade)

  @@id([eventId, resultUserId])
}

//Face attibute tracking
model FaceTrackAttributeResult {
  deviceId String
  trackId  String
  cameraId String
  cameraIp String

  avgEmotion          String?
  avgEmotionConfident Float?

  gender          String?
  genderConfident Float?

  ageFrom      Int?
  ageTo        Int?
  ageConfident Float?

  totalFrame         Int?
  emoNegativePercent Float?
  emoNeutralPercent  Float?
  emoPositivePercent Float?
  ageEdge            Float?
  genderEdge         String?

  isClusterSync       Boolean               @default(false)
  dateCreated         DateTime              @default(now())
  dateModified        DateTime              @default(now()) @updatedAt
  FaceAttributeResult FaceAttributeResult[]

  @@id([deviceId, trackId])
  @@index([trackId, ageConfident, genderConfident, avgEmotionConfident, dateCreated, dateModified])
}

model FaceAttributeResult {
  id       String @id @default(uuid())
  deviceId String
  trackId  String

  emotion          String?
  emotionConfident Float?

  gender          String?
  genderConfident Float?

  ageFrom      Int?
  ageTo        Int?
  ageConfident Float?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Track FaceTrackAttributeResult @relation(fields: [deviceId, trackId], references: [deviceId, trackId], onDelete: Cascade)

  @@index([trackId, ageConfident, genderConfident, emotionConfident, dateCreated, dateModified])
}

//AI Service config
model AiService {
  id      String @id @default(uuid())
  name    String
  type    Int
  dropFps Int    @default(0)

  isTimeLimit    Boolean @default(false)
  startTime      String  @default("00:00")
  endTime        String  @default("23:59")
  isPopupAlert   Boolean @default(false)
  alertDelaySecs Int     @default(0)

  isActive           Boolean              @default(true)
  isClusterSync      Boolean              @default(false)
  dateCreated        DateTime             @default(now())
  dateModified       DateTime             @default(now()) @updatedAt
  AiServiceConfigs   AiServiceConfig[]
  CameraRoi          RoiAiService[]
  DeviceRoiAiService DeviceRoiAiService[]
}

model AiServiceConfig {
  aiServiceId   String
  configType    Int
  value         String
  isActive      Boolean   @default(true)
  isClusterSync Boolean   @default(false)
  dateCreated   DateTime  @default(now())
  dateModified  DateTime  @default(now()) @updatedAt
  AiService     AiService @relation(fields: [aiServiceId], references: [id], onDelete: Cascade)

  @@id([aiServiceId, configType])
}

model DeletedAiService {
  id            String   @id
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@index([dateCreated, dateModified])
}

//Camera
model Camera {
  id          String  @id @default(uuid())
  ip          String  @default("")
  name        String
  rtsp        String  @default("")
  subRtsp     String  @default("")
  snapShot    String?
  nxFirmware  String?
  nxSystemId  String?
  nxIp        String?
  nxLogicalId String?
  nxModel     String?
  nxName      String?
  nxShareId   String?
  nxUrl       String?

  frameRate      String? @default("10")
  streamMode     String? @default("0")
  recordDuration String? @default("5000")
  inputCodec     String? @default("0")
  outputCodec    String? @default("1")
  distanceMin    String? @default("10")
  faceService    String? @default("0")
  personService  String? @default("0")

  vmsDeviceId   String?
  nxAuthenToken String?
  isActive      Boolean @default(false)

  isEzCam                 Boolean @default(false)
  ezStreamMode            Int     @default(0)
  ezState                 Int?
  ezPrivateStreamUrl      String?
  ezPrivateStreamUserName String?
  ezPrivateStreamPass     String?
  ezPublicStreamUrl       String?
  ezPublicStreamUserName  String?
  ezPublicStreamPass      String?

  isStreaming          Boolean                @default(false)
  isClusterSync        Boolean                @default(false)
  dateCreated          DateTime               @default(now())
  dateModified         DateTime               @default(now()) @updatedAt
  Devices              DeviceCamera[]
  CameraRois           CameraRoi[]
  SmartSearchEvent     SmartSearchEvent[]
  VMS                  Device?                @relation(fields: [vmsDeviceId], references: [id], onDelete: Cascade)
  VehicleTrackInfo     VehicleTrackInfo[]
  VehiclePackingDevice VehiclePackingDevice[]
  AttDevice            AttDevice[]
  CameraCompany        CameraCompany[]
  FaceTrackIds         FaceTrackIds[]
  AcCamera             AcCamera[]
  AccessControlCamera  AccessControlCamera[]
  CameraMap            CameraMap[]
}

model CameraRoi {
  id            String         @id @default(uuid())
  name          String
  xs            String
  ys            String
  cameraId      String
  deviceId      String
  isActive      Boolean        @default(true)
  isClusterSync Boolean        @default(false)
  dateCreated   DateTime       @default(now())
  dateModified  DateTime       @default(now()) @updatedAt
  Camera        Camera         @relation(fields: [cameraId], references: [id], onDelete: Cascade)
  Device        Device         @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  AiServices    RoiAiService[]
}

model RoiAiService {
  cameraRoiId   String
  aiServiceId   String
  isClusterSync Boolean   @default(false)
  dateCreated   DateTime  @default(now())
  dateModified  DateTime  @default(now()) @updatedAt
  CameraRoi     CameraRoi @relation(fields: [cameraRoiId], references: [id], onDelete: Cascade)
  AiService     AiService @relation(fields: [aiServiceId], references: [id], onDelete: Cascade)

  @@id([cameraRoiId, aiServiceId])
}

model EventVideo {
  deviceId      String
  srcVideoId    String
  cameraId      String?
  cameraIp      String?
  path          String
  duration      Float
  startTime     DateTime
  endTime       DateTime
  metaData      String?
  thumnail      String?
  isEventFull   Boolean  @default(false)
  isScanned     Boolean  @default(false)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([deviceId, srcVideoId])
  @@index([startTime, endTime])
}

model DeletedCamera {
  id            String   @id
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@index([dateCreated, dateModified])
}

model DeletedDeviceCamera {
  deviceId      String
  cameraId      String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@id([deviceId, cameraId])
  @@index([dateCreated, dateModified])
}

model DeletedRoi {
  id            String   @id
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@index([dateCreated, dateModified])
}

model DeletedAiRoi {
  roiId         String
  aiServiceId   String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@id([roiId, aiServiceId])
  @@index([dateCreated, dateModified])
}

// Device roi config
model DeviceRoi {
  id                 String               @id @default(uuid())
  name               String
  xs                 String
  ys                 String
  deviceId           String
  isActive           Boolean              @default(true)
  isClusterSync      Boolean              @default(false)
  dateCreated        DateTime             @default(now())
  dateModified       DateTime             @default(now()) @updatedAt
  Device             Device               @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  DeviceRoiAiService DeviceRoiAiService[]
}

model DeviceRoiAiService {
  deviceRoiId   String
  aiServiceId   String
  isActive      Boolean  @default(true)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  DeviceRoi DeviceRoi @relation(fields: [deviceRoiId], references: [id], onDelete: Cascade)
  AiService AiService @relation(fields: [aiServiceId], references: [id], onDelete: Cascade)

  @@id([deviceRoiId, aiServiceId])
}

// Device-camera config
model DeviceCamera {
  deviceId      String
  cameraId      String
  isDisplay     Boolean
  isStreaming   Boolean  @default(true)
  isActive      Boolean  @default(true)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  Device        Device   @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  Camera        Camera   @relation(fields: [cameraId], references: [id], onDelete: Cascade)

  @@id([deviceId, cameraId])
}

//Cluster
model CivamsCluster {
  clusterId     String   @id
  clusterName   String
  url           String
  isEnable      Boolean  @default(false)
  isClusterSync Boolean  @default(false)
  authenToken   String   @default("")
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model CivamsSite {
  id           String       @id
  name         String       @unique
  url          String       @default("")
  isActive     Boolean      @default(false)
  isDelete     Boolean      @default(false)
  authenToken  String
  dateCreated  DateTime     @default(now())
  dateModified DateTime     @default(now()) @updatedAt
  User         User[]
  Company      Company[]
  Department   Department[]
}

//Students
model Student {
  id            String        @id @default(uuid())
  classCode     String
  className     String
  grade         String
  majorCode     String
  majorName     String
  validUntil    DateTime
  isClusterSync Boolean       @default(false)
  dateCreated   DateTime      @default(now())
  dateModified  DateTime      @default(now()) @updatedAt
  IdCard        IdCard[]
  User          User[]
  FingerPrint   FingerPrint[]
  PamlPrint     PamlPrint[]
}

//Data backup
model DataBackupJob {
  id              String   @id @default(uuid())
  isRunning       Boolean  @default(true)
  tableName       String
  backupTimeStamp DateTime
  backupFilePath  String

  isClusterSync    Boolean            @default(false)
  dateCreated      DateTime           @default(now())
  dateModified     DateTime           @default(now()) @updatedAt
  UserExportDetail UserExportDetail[]
}

model UserExportDetail {
  jobId  String
  userId String

  processed Boolean  @default(false)
  path      String?
  isErr     Boolean?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  DataBackupJob DataBackupJob @relation(fields: [jobId], references: [id], onDelete: Cascade)
  User          User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([jobId, userId])
}

//Data import
model DataImportJob {
  id      String @id @default(uuid())
  jobType Int

  importFileName String?
  isOverwrite    Boolean @default(false)
  importerId     String?

  isClusterSync Boolean         @default(false)
  dateCreated   DateTime        @default(now())
  dateModified  DateTime        @default(now()) @updatedAt
  DataRows      DataImportRow[]

  Importer User? @relation(fields: [importerId], references: [id], onDelete: SetNull)
}

model DataImportRow {
  id              String    @id @default(uuid())
  dataImportJobId String
  processed       Boolean   @default(false)
  processTime     DateTime?

  rawData         String
  rootUnzipFolder String?
  errs            String?
  isOverwrite     Boolean @default(false)
  resultType      Int?

  faceImageUserId    String?
  civamsImportUserId String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  DataImportJob DataImportJob @relation(fields: [dataImportJobId], references: [id], onDelete: Cascade)
  FaceImageUser User?         @relation(fields: [faceImageUserId], references: [id], onDelete: SetNull)
}

//NX Integration
model NxSystem {
  id            String     @id @default(uuid())
  nxLocalId     String     @unique() @default("")
  baseUrl       String
  name          String
  isClusterSync Boolean    @default(false)
  dateCreated   DateTime   @default(now())
  dateModified  DateTime   @default(now()) @updatedAt
  NxUser        NxUser[]
  NxServer      NxServer[]
}

model NxUser {
  id            String   @id @default(uuid())
  nxLocalId     String   @unique() @default("")
  nxSystemId    String
  userName      String
  password      String
  authenToken   String
  ageS          Int      @default(0)
  expiresInS    Int      @default(0)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  NxSystem NxSystem @relation(fields: [nxSystemId], references: [id], onDelete: Cascade)
}

model NxServer {
  id            String   @id
  serverUrl     String
  nxSystemId    String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  NxSystem      NxSystem @relation(fields: [nxSystemId], references: [id], onDelete: Cascade)
}

//OTT integrations
model UserOtp {
  userId        String   @id
  otp           String
  expireTime    Int      @default(120)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  User          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model OttGateWay {
  id            String   @id @default(uuid())
  name          String   @default("")
  authenToken   String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model TelegramBot {
  id            String   @id
  userName      String   @unique
  token         String
  first_name    String
  isEnable      Boolean
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model TelegramReceiverInfo {
  id                    String                  @id
  is_bot                Boolean?
  first_name            String?
  last_name             String?
  username              String?
  language_code         String?
  isClusterSync         Boolean                 @default(false)
  dateCreated           DateTime                @default(now())
  dateModified          DateTime                @default(now()) @updatedAt
  TelegramGroupReceiver TelegramGroupReceiver[]
}

model TelegramGroup {
  id                      String                    @id @default(uuid())
  name                    String
  isEnable                Boolean                   @default(true)
  type                    Int                       @default(0)
  isClusterSync           Boolean                   @default(false)
  dateCreated             DateTime                  @default(now())
  dateModified            DateTime                  @default(now()) @updatedAt
  TelegramGroupUser       TelegramGroupUser[]
  TelegramGroupDepartment TelegramGroupDepartment[]
  TelegramGroupCompany    TelegramGroupCompany[]
  TelegramGroupDevice     TelegramGroupDevice[]
  TelegramGroupReceiver   TelegramGroupReceiver[]
  TelegramGroupTime       TelegramGroupTime[]
}

model NotificationTime {
  id                String              @id @default(uuid())
  name              String
  startDate         DateTime?
  endDate           DateTime?
  startHour         Int                 @default(0)
  startMin          Int                 @default(0)
  startSec          Int                 @default(0)
  endHour           Int                 @default(23)
  endMin            Int                 @default(59)
  endSec            Int                 @default(59)
  isClusterSync     Boolean             @default(false)
  dateCreated       DateTime            @default(now())
  dateModified      DateTime            @default(now()) @updatedAt
  TelegramGroupTime TelegramGroupTime[]
}

model TelegramGroupTime {
  notifyTimeId    String
  telegramGroupId String
  isClusterSync   Boolean          @default(false)
  dateCreated     DateTime         @default(now())
  dateModified    DateTime         @default(now()) @updatedAt
  TelegramGroup   TelegramGroup    @relation(fields: [telegramGroupId], references: [id], onDelete: Cascade)
  NotifyTime      NotificationTime @relation(fields: [notifyTimeId], references: [id], onDelete: Cascade)

  @@id([notifyTimeId, telegramGroupId])
}

model TelegramGroupReceiver {
  receiverId    String
  groupId       String
  isClusterSync Boolean              @default(false)
  dateCreated   DateTime             @default(now())
  dateModified  DateTime             @default(now()) @updatedAt
  TeleGroup     TelegramGroup        @relation(fields: [groupId], references: [id], onDelete: Cascade)
  TeleInfo      TelegramReceiverInfo @relation(fields: [receiverId], references: [id], onDelete: Cascade)

  @@id([groupId, receiverId])
}

model TelegramGroupDevice {
  groupId       String
  deviceId      String
  isClusterSync Boolean       @default(false)
  dateCreated   DateTime      @default(now())
  dateModified  DateTime      @default(now()) @updatedAt
  TeleGroup     TelegramGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  Device        Device        @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([groupId, deviceId])
}

model TelegramGroupUser {
  groupId       String
  userId        String
  isClusterSync Boolean       @default(false)
  dateCreated   DateTime      @default(now())
  dateModified  DateTime      @default(now()) @updatedAt
  TeleGrop      TelegramGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  User          User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([groupId, userId])
}

model TelegramGroupDepartment {
  groupId       String
  departmentId  String
  isClusterSync Boolean       @default(false)
  dateCreated   DateTime      @default(now())
  dateModified  DateTime      @default(now()) @updatedAt
  TeleGrop      TelegramGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  Department    Department    @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  @@id([groupId, departmentId])
}

model TelegramGroupCompany {
  groupId       String
  companyId     String
  isClusterSync Boolean       @default(false)
  dateCreated   DateTime      @default(now())
  dateModified  DateTime      @default(now()) @updatedAt
  TeleGrop      TelegramGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  Company       Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@id([groupId, companyId])
}

//ekyc integrations
model EkycIntegation {
  id            String   @id @default(uuid())
  name          String   @default("")
  token         String
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model UserEkycConfig {
  userId  String @id
  token   String
  ekycUrl String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

//ctel integrations
model CtelIntegrationConfig {
  id              String   @id @default(uuid())
  grant_type      String   @default("password")
  client_id       String   @default("payroll-api")
  client_secret   String   @default("O2l3orjHcf6RozA3mqQ3VVOTgwUa5EZO")
  username        String   @default("face-id")
  password        String   @default("eyJhbGciOiJSUzI1NiIsInR5")
  iamRootPath     String   @default("https://rnd2-sso.cmctelecom.vn/")
  iamRealm        String   @default("rnd2-dev")
  payrollRootPath String   @default("https://rnd2-dev-api-v2.cmctelecom.vn/payroll/cviams")
  cookie          String   @default("JSESSIONID=C0595FFACEC1282F34C0B55993D389E1")
  isClusterSync   Boolean  @default(false)
  dateCreated     DateTime @default(now())
  dateModified    DateTime @default(now()) @updatedAt
}

//BioStar 2 integrations
model SupremaBioStarConfig {
  id         String @id @default(uuid())
  serverName String
  serverIp   String
  serverPort Int
  userName   String
  password   String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

//Traffic
model VehicleTrackInfo {
  trackId  String  @id
  cameraIp String
  cameraId String
  deviceId String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Camera             Camera               @relation(fields: [cameraId], references: [id], onDelete: Cascade)
  VehicleTrackResult VehicleTrackResult[]

  @@index([trackId])
}

model VehicleTrackResult {
  id        String   @id @default(uuid())
  trackId   String
  imagePath String
  imageId   String
  time      DateTime
  bodyR     String?
  bodyG     String?
  bodyB     String?

  isVehicleDetected      Boolean @default(false)
  vehicleDetectCof       Float?
  vehicleType            Int?
  vehicleTypeName        String? @default("")
  isLicensePlateDetected Boolean @default(false)
  licensePlate           String?

  isSynedFromEdge Boolean @default(true)
  isSentCore      Boolean @default(false)
  isCoreResponse  Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  TrackInfo VehicleTrackInfo @relation(fields: [trackId], references: [trackId], onDelete: Cascade)

  @@index([time, trackId, licensePlate, bodyR, bodyG, bodyB])
}

model VehicleSearchEvent {
  id              String   @id @default(uuid())
  deviceId        String
  imagePath       String
  topN            Int      @default(200)
  startSearchTime DateTime
  endSearchTime   DateTime
  camFilterIds    String   @default("")

  isRunning Boolean @default(true)

  isClusterSync        Boolean                @default(false)
  dateCreated          DateTime               @default(now())
  dateModified         DateTime               @default(now()) @updatedAt
  VehicleSearchCapture VehicleSearchCapture[]
}

model VehicleSearchCapture {
  id      String  @id @default(uuid())
  eventId String
  image   String
  imageId String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  VehicleSearchEvent  VehicleSearchEvent    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  VehicleSearchResult VehicleSearchResult[]
}

model VehicleSearchResult {
  id         String  @id @default(uuid())
  captureId  String
  trackId    String
  imageId    String
  cameraId   String?
  cameraIp   String?
  confidence Float

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  VehicleSearchCapture VehicleSearchCapture @relation(fields: [captureId], references: [id], onDelete: Cascade)
}

//I-Parking
model IparkingConfig {
  url           String  @id
  authen_url    String  @default("")
  client_id     String
  client_secret String
  isActive      Boolean @default(true)
  configs       String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model IparkingGroup {
  id                        String @id @default(uuid())
  name                      String
  code                      String @unique
  type                      String
  plateNumberValidationType Int
  plateNumberComparisonType Int
  vehicleType               Int

  iparkingSyncTime DateTime?
  isClusterSync    Boolean      @default(false)
  dateCreated      DateTime     @default(now())
  dateModified     DateTime     @default(now()) @updatedAt
  IparkingId       IparkingId[]
}

model IparkingCustomer {
  id      String   @id @default(uuid())
  userId  String?
  guestId String?
  code    String
  name    String
  phone   String
  address String
  dob     DateTime

  iparkingSyncTime DateTime?
  isClusterSync    Boolean   @default(false)
  dateCreated      DateTime  @default(now())
  dateModified     DateTime  @default(now()) @updatedAt

  User            User?             @relation(fields: [userId], references: [id], onDelete: Cascade)
  Guest           Guest?            @relation(fields: [guestId], references: [id], onDelete: Cascade)
  IparkingVehicle IparkingVehicle[]
}

model IparkingId {
  id              String @id @default(uuid())
  name            String
  code            String @unique
  type            Int
  status          String
  note            String
  iparkingGroupId String

  iparkingSyncTime DateTime?
  isClusterSync    Boolean   @default(false)
  dateCreated      DateTime  @default(now())
  dateModified     DateTime  @default(now()) @updatedAt

  IparkingGroup           IparkingGroup             @relation(fields: [iparkingGroupId], references: [id], onDelete: Cascade)
  IparkingVehicleIdentity IparkingVehicleIdentity[]
}

model IparkingVehicle {
  id          String @id @default(uuid())
  name        String
  plateNumber String
  type        Int
  customerId  String

  expiredDate       DateTime
  lastActivatedDate DateTime
  checkInByPlate    Boolean
  checkOutByPlate   Boolean

  iparkingSyncTime DateTime?
  isClusterSync    Boolean   @default(false)
  dateCreated      DateTime  @default(now())
  dateModified     DateTime  @default(now()) @updatedAt

  IparkingCustomer        IparkingCustomer          @relation(fields: [customerId], references: [id], onDelete: Cascade)
  IparkingVehicleIdentity IparkingVehicleIdentity[]
}

model DeletedVehicle {
  code String @id

  iparkingSyncTime DateTime?
  isClusterSync    Boolean   @default(false)
  dateCreated      DateTime  @default(now())
  dateModified     DateTime  @default(now()) @updatedAt
}

model IparkingVehicleIdentity {
  identityId String
  vehicleId  String

  iparkingSyncTime DateTime?
  isClusterSync    Boolean   @default(false)
  dateCreated      DateTime  @default(now())
  dateModified     DateTime  @default(now()) @updatedAt

  Identity IparkingId      @relation(fields: [identityId], references: [id], onDelete: Cascade)
  Vehicle  IparkingVehicle @relation(fields: [vehicleId], references: [id], onDelete: Cascade)

  @@id([identityId, vehicleId])
}

model IparkingLane {
  id   String @id @default(uuid())
  name String
  code String
  type Int

  iparkingSyncTime DateTime?
  isClusterSync    Boolean   @default(false)
  dateCreated      DateTime  @default(now())
  dateModified     DateTime  @default(now()) @updatedAt
}

model IparkingVehicleDaileReport {
  day            DateTime
  laneId         String
  timeZoneOffset String   @default("+07:00")

  countCar      Int @default(0)
  countMotobike Int @default(0)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@id([day, laneId])
  @@index([day])
}

//iparking decrpted
model VehicleParkingLane {
  id          String @id @default(uuid())
  name        String
  laneType    Int
  vehicleType Int
  barrieMode  Int

  isClusterSync                   Boolean                           @default(false)
  dateCreated                     DateTime                          @default(now())
  dateModified                    DateTime                          @default(now()) @updatedAt
  VehiclePackingDevice            VehiclePackingDevice[]
  VehicleParkingLaneFaceResult    VehicleParkingLaneFaceResult[]
  VehicleParkingLaneLicenseResult VehicleParkingLaneLicenseResult[]
  VehicleParkingLaneOpenHistory   VehicleParkingLaneOpenHistory[]
  VehicleParkingLaneGuestResult   VehicleParkingLaneGuestResult[]
}

model VehiclePackingDevice {
  id         String  @id @default(uuid())
  parkingId  String
  deviceId   String
  cameraId   String?
  deviceType Int
  channel    Int     @default(0)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  VehicleParkingLane VehicleParkingLane @relation(fields: [parkingId], references: [id], onDelete: Cascade)
  Device             Device             @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  Camera             Camera?            @relation(fields: [cameraId], references: [id], onDelete: Cascade)
}

model VehicleParkingLicenseAllowed {
  licensePlate String  @id
  userId       String?
  guestId      String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User  User?  @relation(fields: [userId], references: [id], onDelete: Cascade)
  Guest Guest? @relation(fields: [guestId], references: [id], onDelete: Cascade)
}

model VehicleParkingLaneFaceResult {
  id        Int     @id
  parkingId String
  isOpen    Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  VehicleParkingLane            VehicleParkingLane              @relation(fields: [parkingId], references: [id], onDelete: Cascade)
  FaceResult                    FaceResult                      @relation(fields: [id], references: [id], onDelete: Cascade)
  VehicleParkingLaneOpenHistory VehicleParkingLaneOpenHistory[]
}

model VehicleParkingLaneGuestResult {
  id        Int     @id
  parkingId String
  isOpen    Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  VehicleParkingLane            VehicleParkingLane              @relation(fields: [parkingId], references: [id], onDelete: Cascade)
  GuestFaceResult               GuestFaceResult                 @relation(fields: [id], references: [id], onDelete: Cascade)
  VehicleParkingLaneOpenHistory VehicleParkingLaneOpenHistory[]
}

model VehicleParkingLaneLicenseResult {
  id        Int     @id @default(autoincrement())
  parkingId String
  isOpen    Boolean @default(false)

  deviceId  String
  cameraId  String
  cameraIp  String
  trackId   String
  ocrPlate  String
  className String @default("other")

  path String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  VehicleParkingLane            VehicleParkingLane              @relation(fields: [parkingId], references: [id], onDelete: Cascade)
  VehicleParkingLaneOpenHistory VehicleParkingLaneOpenHistory[]
}

model VehicleParkingLaneOpenHistory {
  id              Int    @id @default(autoincrement())
  parkingId       String
  type            Int
  faceResultId    Int?
  guestResultId   Int?
  licenseResultId Int?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  VehicleParkingLane              VehicleParkingLane               @relation(fields: [parkingId], references: [id], onDelete: Cascade)
  VehicleParkingLaneFaceResult    VehicleParkingLaneFaceResult?    @relation(fields: [faceResultId], references: [id], onDelete: SetNull)
  VehicleParkingLaneGuestResult   VehicleParkingLaneGuestResult?   @relation(fields: [guestResultId], references: [id], onDelete: SetNull)
  VehicleParkingLaneLicenseResult VehicleParkingLaneLicenseResult? @relation(fields: [licenseResultId], references: [id], onDelete: SetNull)
}

//Fcard face terminal integrations
model FcardServerConfig {
  id  String @id @default(uuid())
  url String

  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt
}

model FcardUser {
  deviceId     String
  fcardUserId  Int
  fcardTzId    Int?
  userId       String?
  syncResponse String?

  cardData                    Int?
  password                    String?
  expiry                      DateTime?
  timeGroup                   Int?
  openTime                    Int?
  identity                    Int?
  cardType                    Int?
  cardStatus                  Int?
  enterStatus                 Int?
  pName                       String?
  pCode                       String?
  dept                        String?
  job                         String?
  recordTime                  DateTime?
  isFaceFeatureCode           Boolean?
  holiday                     String?
  fingerPrintFeatureCodeCount Int?
  palmCount                   Int?

  isSyncDeviceTz Boolean @default(false)

  status        ActiveStatus @default(ACTIVE)
  isClusterSync Boolean      @default(false)
  dateCreated   DateTime     @default(now())
  dateModified  DateTime     @default(now()) @updatedAt

  Device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  User   User?  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([deviceId, fcardUserId])
}

model FcardResults {
  deviceId      String
  fcardResultId Int
  faceResultId  Int?
  fcardUserId   Int
  time          DateTime
  recogType     Int
  image         String?

  isSyncFaceResultTable Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  Device        Device   @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@id([deviceId, fcardResultId])
}

model FcardTzSegment {
  id        String @id @default(uuid())
  startTime String @default("00:00")
  endTime   String @default("00:00")

  isClusterSync  Boolean      @default(false)
  dateCreated    DateTime     @default(now())
  dateModified   DateTime     @default(now()) @updatedAt
  FcardTzWeekDay FcardTzDay[]
}

model FcardTzDay {
  day       Int
  segmentId String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Segment          FcardTzSegment     @relation(fields: [segmentId], references: [id], onDelete: Cascade)
  FcardTzDayMapped FcardTzDayMapped[]

  @@id([day, segmentId])
}

model FcardTzDayMapped {
  day       Int
  segmentId String
  tzId      String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Day FcardTzDay @relation(fields: [day, segmentId], references: [day, segmentId], onDelete: Cascade)
  TZ  FcardTz    @relation(fields: [tzId], references: [id], onDelete: Cascade)

  @@id([tzId, day, segmentId])
}

model FcardTz {
  id String @id @default(uuid())

  isClusterSync    Boolean            @default(false)
  dateCreated      DateTime           @default(now())
  dateModified     DateTime           @default(now()) @updatedAt
  FcardTzDayMapped FcardTzDayMapped[]
  FcardDeviceTz    FcardDeviceTz[]
}

model FcardDeviceTz {
  deviceId     String
  fcardTzId    Int
  tzId         String
  isSyncDevice Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Device  Device  @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  FcardTz FcardTz @relation(fields: [tzId], references: [id], onDelete: Cascade)

  @@id([deviceId, tzId])
}

//3rd API token
model IntegraionApiKeys {
  id            String  @id @default(uuid())
  token         String
  integrationId String
  description   String?
  isActive      Boolean @default(true)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

//face search api
model FaceSearchApiEvent {
  id         String  @id @default(uuid())
  sourcePath String
  dbId       String
  topK       Int
  finished   Boolean @default(false)
  error      String?

  sourceType Int?
  sourceKey  String?

  headposeYaw    Float?
  headposePitch  Float?
  headposeRoll   Float?
  mask           Boolean?
  faceDetectConf Float?
  faceVector     String?

  isClusterSync       Boolean               @default(false)
  dateCreated         DateTime              @default(now())
  dateModified        DateTime              @default(now()) @updatedAt
  FaceSearchApiResult FaceSearchApiResult[]
}

model FaceSearchApiResult {
  id      String @id @default(uuid())
  eventId String

  conf    Float
  userId  String?
  guestId String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  SearchEvent FaceSearchApiEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  User        User?              @relation(fields: [userId], references: [id], onDelete: Cascade)
  Guest       Guest?             @relation(fields: [guestId], references: [id], onDelete: Cascade)
}

//integration functions
model ThirdPartyIntegration {
  id         String  @id @default(uuid())
  isEnable   Boolean @default(false)
  type       Int     @default(0)
  configData String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

//person detect

//location
model Location {
  id          String  @id @default(uuid())
  name        String
  description String?
  parentId    String?

  isClusterSync  Boolean          @default(false)
  dateCreated    DateTime         @default(now())
  dateModified   DateTime         @default(now()) @updatedAt
  DeviceLocation DeviceLocation[]
  GuestLocation  GuestLocation[]
}

model DeviceLocation {
  deviceId   String @unique
  locationId String

  isCheckInDevice  Boolean @default(false)
  isCheckOutDevice Boolean @default(false)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Device   Device   @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  Location Location @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@id([deviceId, locationId])
}

model GuestLocation {
  guestId    String
  locationId String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Location Location @relation(fields: [locationId], references: [id], onDelete: Cascade)
  Guest    Guest    @relation(fields: [guestId], references: [id], onDelete: Cascade)

  @@id([guestId, locationId])
}

//add-in mqtt configs
model MqttConfigs {
  id            String  @id @default(uuid())
  address       String
  isMqtts       Boolean @default(true)
  user          String  @default("")
  password      String  @default("")
  type          Int
  topic         String?
  dataFormat    String?
  isDeviceLimit Boolean @default(false)

  idDeviceDelay  Boolean  @default(false)
  delayDeviceIds String[]
  delayTime      Float?

  isClusterSync     Boolean             @default(false)
  dateCreated       DateTime            @default(now())
  dateModified      DateTime            @default(now()) @updatedAt
  MqttConfigDevices MqttConfigDevices[]
}

model MqttConfigDevices {
  configId String
  deviceId String

  isDeleted     Boolean  @default(false)
  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  MqttConfig MqttConfigs @relation(fields: [configId], references: [id], onDelete: Cascade)

  @@id([configId, deviceId])
}

//video analytics
model IgnoreReId {
  reId     String  @id
  isEnable Boolean @default(true)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model ReIdLabel {
  reid   String  @id
  label  String?
  userId String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
  User          User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
}

model UploadVideoAnalytics {
  id       String @id @default(uuid())
  groupId  String
  fileName String
  filePath String
  fileType String
  fileSize Int

  processDeviceId String?

  frameWidth      Int?
  frameHeight     Int?
  frameRate       Float?
  totalFrame      Int?
  duration        Float?
  bitRate         Float?
  processingFrame Int?
  processStatus   String?

  s2tStatus Int?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  DeviceSyncLog            DeviceSyncLog[]
  VideoAnalyticGroup       VideoAnalyticGroup         @relation(fields: [groupId], references: [id], onDelete: Cascade)
  UploadAudioAnalytics     UploadAudioAnalytics[]
  VideoAnalyticsFaceResult VideoAnalyticsFaceResult[]

  @@index([dateCreated, dateModified])
}

model VideoAnalyticGroup {
  id          String  @id @default(uuid())
  name        String
  type        String?
  description String?

  faceIdEnable             Boolean @default(true)
  faceSmartSearchEnable    Boolean @default(false)
  faceReIdEnable           Boolean @default(true)
  ageGenderEnable          Boolean @default(false)
  humanAttEnable           Boolean @default(true)
  humanSmartSearchEnable   Boolean @default(false)
  vehicleAttEnable         Boolean @default(false)
  vehicleSmartSearchEnable Boolean @default(false)
  videoAbnormalEnable      Boolean @default(false)
  s2tEnable                Boolean @default(false)

  isClusterSync        Boolean                @default(false)
  dateCreated          DateTime               @default(now())
  dateModified         DateTime               @default(now()) @updatedAt
  UploadVideoAnalytics UploadVideoAnalytics[]
}

model UploadAudioAnalytics {
  id               String  @id @default(uuid())
  audioFilePath    String
  uploadVideoId    String?
  duration         Float?
  startVideoOffset Float?

  processStatus Int?
  taskId        String?
  lastStatus    String?
  lastMess      String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  UploadVideo          UploadVideoAnalytics?  @relation(fields: [uploadVideoId], references: [id], onDelete: SetNull)
  AudioAnalyticResults AudioAnalyticResults[]
}

model AudioAnalyticResults {
  uploadAudioId      String
  sectionNumber      Int
  start              Float?
  end                Float?
  listStart          Float[]
  listEnd            Float[]
  humanReadableStart String?
  humanReadableEnd   String?
  content            String?
  speakerId          String?
  confidentScore     Float?
  meanVolume         Float?
  emotion            String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  UploadAudio UploadAudioAnalytics @relation(fields: [uploadAudioId], references: [id], onDelete: Cascade)

  @@id([uploadAudioId, sectionNumber])
}

model DeletedUploadVideoId {
  id String @id

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model VideoAnalyticsFaceResult {
  id       Int     @id @default(autoincrement())
  userId   String?
  userName String?

  trackingId   String
  reId         String?
  humanTrackId String?

  deviceId String
  time     DateTime
  image    String
  cameraIp String
  videoId  String

  detectDuration   Int?
  vectorDuration   Int?
  headposePitch    Float?
  headposeRoll     Float?
  headposeYaw      Float?
  faceQualityScore Float?
  recogConfident   Float?
  reqTimePoint     DateTime?
  reqExDuration    Int?
  searchDuration   Int?
  reIdConf         Float?
  facekpts         String?

  maskConfident    Float?
  maskDuration     Int?
  headposeDuration Int?
  qualityDuration  Int?

  detectTimePoint Int?

  imageId String?
  roiId   String?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  UploadVideo UploadVideoAnalytics @relation(fields: [videoId], references: [id], onDelete: Cascade)

  @@index([videoId, trackingId, reId, time, humanTrackId, imageId])
}

//security auto report
model SecurityTotalFaceDailyReport {
  day            DateTime @id
  timeZoneOffset String   @default("+07:00")

  countUser      Int @default(0)
  countGuest     Int @default(0)
  countReId      Int @default(0)
  countFaceTrack Int @default(0)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@index([day])
}

model SecurityFaceDailyByAccessControlReport {
  day            DateTime
  acId           String
  timeZoneOffset String   @default("+07:00")

  countUser      Int @default(0)
  countGuest     Int @default(0)
  countReId      Int @default(0)
  countFaceTrack Int @default(0)

  AccessControl AccessControl @relation(fields: [acId], references: [id], onDelete: Cascade)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  @@id([day, acId])
  @@index([day])
}

model ObjectCountLog {
  id         String  @id @default(uuid())
  deviceId   String?
  cameraId   String?
  roiId      String?
  image      String?
  count      Int     @default(0)
  objectType Int     @default(0)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

//========================Global customize========================
//Map
model Map {
  id                String            @id @default(uuid())
  name              String            @default("")
  path              String
  cameraMaps        CameraMap[]
  heatMapHistories  HeatMapHistory[]
  trackMapHistories TrackMapHistory[]
  status            Int               @default(1)

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

// Camera Map 
model CameraMap {
  id       String @id @default(uuid())
  cameraId String
  xAxis    Float
  yAxis    Float
  color    String
  mapId    String

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  Map    Map?   @relation(fields: [mapId], references: [id])
  Camera Camera @relation(fields: [cameraId], references: [id])
}

model TrackMapHistory {
  id       String   @id @default(uuid())
  username String
  userId   String
  mapId    String
  from     DateTime @default(now())
  to       DateTime @default(now())

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt

  User User @relation(fields: [userId], references: [id])
  Map  Map  @relation(fields: [mapId], references: [id])
}

model HeatMapHistory {
  id    String   @id @default(uuid())
  name  String
  mapId String
  map   Map      @relation(fields: [mapId], references: [id])
  from  DateTime @default(now())
  to    DateTime @default(now())

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}

model GlobalCustomeLog {
  id          String   @id @default(uuid())
  userId      String?
  User        User?    @relation(fields: [userId], references: [id])
  method      String
  endpoint    String
  status      Int
  action      String
  duration    Decimal?
  requestBody Json?

  isClusterSync Boolean  @default(false)
  dateCreated   DateTime @default(now())
  dateModified  DateTime @default(now()) @updatedAt
}
