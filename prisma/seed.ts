import { ConfigDataType, FunctionId, Prisma, PrismaClient, RoleType, ServerConfigKey } from '@prisma/client'
import { v4 } from 'uuid';
const prisma = new PrismaClient()

async function main() {
    console.log(`==============Seeding server config==============`);
    await seedServerConfig();
    await seedFilePathConfig();

    console.log(`==============Seeding user's roles==============`);
    await seedRole();

    console.log(`==============Seeding user's functions==============`);
    await seedFuntion();

    console.log(`==============Seeding user's permissions==============`);
    await seedPermission();

    console.log(`==============Seeding company==============`);
    await seedCompany();

    console.log(`==============Seeding department==============`);
    await seedDepartment();

    // console.log(`==============Seeding web pages==============`);
    // await seedWebLayout();
    // await seedWebLayoutV2();
}

async function seedServerConfig() {
    const ServerConfigKeys: ServerConfigKey[] = [];
    let seedingConfigDatas: Map<string, Prisma.ServerConfigCreateInput> = new Map();

    const exitedConfigs = await prisma.serverConfig.findMany({
        select: {
            key: true
        }
    });
    const exitedKey = exitedConfigs.map(x => x.key);

    for (var key in ServerConfigKey) {
        const configKey: ServerConfigKey = ServerConfigKey[key];
        if (!exitedKey.includes(configKey)) {
            ServerConfigKeys.push(configKey)
        }
    }
    console.log(`Seed config ${ServerConfigKeys.length} key(s)`)
    console.log(ServerConfigKeys)

    for (let index = 0; index < ServerConfigKeys.length; index++) {
        const configKey = ServerConfigKeys[index];
        let createData: Prisma.ServerConfigCreateInput = {
            key: configKey,
            value: "",
            dataType: ConfigDataType.String,
            isValueMask: false
        };
        switch (configKey) {
            case ServerConfigKey.SERVER_UINIQUE_ID:
                createData.value = v4();
                break;

            case ServerConfigKey.SERVER_CHANNEL_ID:
                createData.value = v4();
                break;

            case ServerConfigKey.DEFAULT_DB_ID:
                createData.value = "CMC_CIST_60486a32eab84ec7589a1c1f";
                break;

            case ServerConfigKey.MQTT_TOPICS_REGISTERFLOW:
                createData.value = "topic/RegisterFlow/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.TZ:
                createData.value = "+07:00";
                break;

            case ServerConfigKey.SERVER_URL_TYPE:
                createData.value = "http://";
                break;

            case ServerConfigKey.SERVER_IP:
                createData.value = "localhost";
                break;

            case ServerConfigKey.SERVER_PORT:
                createData.value = "33000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.OCR_SERVER_URL:
                createData.value = "http://**********:23000/api/ocr-mqtt/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_ADDRESS:
                createData.value = "mqtts://**********:8883";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.FACE_BROKER_ADDRESS:
                createData.value = "mqtts://**********:8883";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.PERSON_BROKER_ADDRESS:
                createData.value = "mqtts://**********:8883";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.IPARKING_BROKER_ADDRESS:
                createData.value = "mqtts://**********:8883";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_IS_TLS_ENABLE:
                createData.value = "true";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.BROKER_IS_USER_REQUIRED:
                createData.value = "true";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.BROKER_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.BROKER_DEBUG_TOPIC:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.BROKER_DEBUG_MESS:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.BROKER_MATCHER_RESULTS:
                createData.value = "topic/matcher_results/";
                break;

            case ServerConfigKey.BROKER_TOPIC_RESULTS:
                createData.value = "topic/results/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_RESULTS_EDITED:
                createData.value = "topic/results/edited/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DEVICE_REGISTRATION:
                createData.value = "topic/DeviceIdentification/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DEVICE_FACETERMINAL_STATUS:
                createData.value = "topic/RegisterFlow/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DEVICE_DB_ACK:
                createData.value = "topic/DB_Status_Ack/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DEVICE_REBOOT:
                createData.value = "topic/GeneralConfigRequests/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DEVICE_CONFIG_REQUEST:
                createData.value = "topic/GeneralConfigRequests/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DEVICE_CONFIG_STREAM:
                createData.value = "topic/StreamConfigData/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DEVICE_GENERAL_REQUEST:
                createData.value = "topic/GeneralExternalRequests/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_CLIENT_DBDATA:
                createData.value = "topic/DB_Data/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DBDATA_URL:
                createData.value = "topic/DB_Data_URL/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_STREAM_CONFIG_ACK:
                createData.value = "topic/StreamConfigAck/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_DEVICE_STREAMS_STATUS:
                createData.value = "topic/status/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_PARAMS_CONFIG_REQUESTS:
                createData.value = "topic/ParamsConfigRequests";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_PARAMS_CONFIG_ACK:
                createData.value = "topic/ParamsConfigAck";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_PARAMS_CONFIG_DATA:
                createData.value = "topic/ParamsConfigData";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_SECURITY_PERSON:
                createData.value = "topic/persons/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_SECURITY_LOITERING:
                createData.value = "topic/loiter/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_AI_SERVICE_CONFIG_REQUEST:
                createData.value = "topic/ai_service_request/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_AI_SERVICE_ACK:
                createData.value = "topic/ai_service_request/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_REMOTE_FACE_RECOG_TIMEOUT:
                createData.value = "50";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.BROKER_FACE_QUALITY_REQUEST_TOPIC:
                createData.value = "topic/FaceQualityRequest/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_FACE_QUALITY_ACK:
                createData.value = "topic/FaceQualityAck/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.EMAIL_ADMIN_USER:
                createData.value = "<EMAIL>";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.EMAIL_ADMIN_PASSWORD:
                createData.value = "Cmcglobal@2021";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.EMAIL_ADMIN_DEFAULT_RECEIVER:
                createData.value = "<EMAIL>";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.EMAIL_ADMIN_HOST:
                createData.value = "mail.cmcglobal.vn";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.EMAIL_ADMIN_PORT:
                createData.value = "25";
                createData.dataType = ConfigDataType.Int;
                createData.isValueMask = true;
                break;

            case ServerConfigKey.ALEART_CONFIG_MASK:
                createData.value = "true";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.ALEART_CONFIG_TEMPERATURE_ENABLE:
                createData.value = "true";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.ALEART_CONFIG_TEMPERATURE:
                createData.value = "38";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.LDAP_ENABLE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.LDAP_ADMIN_DN:
                createData.value = "";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.LDAP_ADMIN_PASS:
                createData.value = "";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.LDAP_URL:
                createData.value = "";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.LDAP_DEFAULT_SEARCH:
                createData.value = "";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.FACE_TERMINAL_IS_SAVE_RESULT_FROM_MQTT:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.FACE_TERMINAL_IS_AC_PROCESS:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.IS_NFS_ENABLE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.NFS_PATH:
                createData.value = "/mnt/nfs_clientshare/civams/public";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.UPDATE_DURATION:
                createData.value = "1720000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.BROKER_TOPIC_FACE_TERMINAL_UPDATE_SIGNAL:
                createData.value = "topic/face_terminal/sync_request/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.INTERGRATION_FREEE_IS_ENABLE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.INTERGRATION_FREEE_URL:
                createData.value = "https://freee/";
                break;

            case ServerConfigKey.INTERGRATION_FREEE_SYNC_INTERVAL:
                createData.value = "10000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.CLUSTER_IS_ENABLE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.CLUSTER_URL:
                createData.value = "http://cluster:33000";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.CLUSTER_AUTEHN_TOKEN:
                createData.value = "";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.SHIFT_API:
                createData.value = "http://10.111.176.90:8091";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.SHIFT_CLIENT_ID:
                createData.value = "48acd7ef6c8d475da2b01c931d1857ad";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.SHIFT_CLIENT_SECRET:
                createData.value = "CC6F571Aa710459A8BCb5DBF67129550";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.SHIFT_API_KEY:
                createData.value = "HrMsdkLSIF3L8sAb";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.PATIENT_DB_ID:
                createData.value = v4();
                break;

            case ServerConfigKey.PATIENT_GET_INFO_URL:
                createData.value = "https://apigw.vinmec.com:8243/ivf/1.0/patient";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.PATIENT_GET_INFO_URL_AUTHEN:
                createData.value = "Bearer 788eaa62-9b2b-3741-929a-270db4a50dc2";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.IS_USER_LOGIN_AD:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.MQTT_USER:
                createData.value = "civams_29122020";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.MQTT_PASS:
                createData.value = "tkNPwLKqEV6h47LCr*ky&cV_7v!+ZuED6&CSnSKzbgn6rNe8xG!%nhu%&Rb#uRa?s4!BS7QAVp!bw^cRhC4bt5EHE9LDAjrtsL@+MEZJEv!k?!u#hJsv+9CDqbFpwkKL";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.WPF_IS_SAVE_RESULT_FROM_MQTT:
                createData.value = "true";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.WPF_IS_AC_PROCESS:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.DEFAULT_TIME_ZONE_OFFSET:
                createData.value = "+07:00";
                break;

            case ServerConfigKey.MAXIMUM_NON_USER_CACHE_IN_TERMINAL:
                createData.value = "300";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.IS_FACE_QUALITY_BYPASS:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.LOG_MAX_TIME:
                createData.value = "2";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.LOG_BACKUP_MAX_TIME:
                createData.value = "30";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.BROKER_TOPIC_FACE_MATCHING_RESULTS:
                createData.value = "topic/similarity_results/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.BROKER_TOPIC_FACE_MATCHING_REQUESTS:
                createData.value = "topic/similarity_request/";
                createData.isValueMask = true;
                break;

            case ServerConfigKey.DEFAULT_FACE_IMAGE_MATCHER:
                createData.value = "civams-face-id";
                break;

            case ServerConfigKey.DEFAULT_AI_DETECTOR:
                createData.value = "civams_ds_dgpu_test_detect";
                break;

            case ServerConfigKey.SHIFT_USER_POLLING_INTERVAL:
                createData.value = "900000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.SQL_SYNC_INTERVAL:
                createData.value = "100000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.IS_REPORT_THREAD_STAND_ALONE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.IS_SCHEDULER_THREAD_STAND_ALONE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.IS_SQL_SYNC_THREAD_STAND_ALONE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.IS_FACE_TERMINAL_SYNC_THREAD_STAND_ALONE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.AUTO_SYNC_AC_USER_TO_DEVICE_INTERVAL:
                createData.value = "60000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.AUTO_CHECK_AI_CORE_INTERVAL:
                createData.value = "5000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.AUTO_ADD_USER_ATT_INTERVAL:
                createData.value = "3600000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.FACE_RESULT_SQL_SYNC_INTERVAL:
                createData.value = "10000";
                createData.dataType = ConfigDataType.Int;
                break;

            case ServerConfigKey.FACE_RESULT_ON_CALL_SQL_SYNC_INTERVAL:
                createData.value = "10000";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.SHIFT_USER_SQL_SYNC_INTERVAL:
                createData.value = "10000";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.AC_USER_SYNC_INTERVAL:
                createData.value = "60000";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.TIME_LOCALE:
                createData.value = "7";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.FACE_RESULT_GENERAL_INTEGRATION_ENABLE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.FACE_RESULT_GENERAL_INTEGRATION_URL:
                createData.value = "";
                break;
            case ServerConfigKey.FACE_RESULT_GENERAL_INTEGRATION_TOKEN:
                createData.value = "";
                break;
            case ServerConfigKey.EVENT_VIDEO_STORAGE_DAYS:
                createData.value = "7";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.FACE_RESULT_GENERAL_INTEGRATION_INTERVAL:
                createData.value = "5000";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.IS_LOCK_USER_UPDATE_FACE_IMAGE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.FAKE_CAM_DEVICE_ID:
                createData.value = v4();
                break;
            case ServerConfigKey.FAKE_CAM_CHANNEL_ID:
                createData.value = v4();
                break;
            case ServerConfigKey.FAKE_CAM_SRC_ID:
                createData.value = v4();
                break;
            case ServerConfigKey.GUEST_DB_ID:
                createData.value = v4();
                break;
            case ServerConfigKey.IS_GUEST_AUTO_LOCK:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.REMOTE_CHECK_DEFAULT_LIMIT:
                createData.value = "0";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.REMOTE_CHECK_DEFAULT_ENABLE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.REMOTE_APPROVE_TIME_LIMIT_ENABLE:
                createData.value = "true";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.REMOTE_APPROVE_DATE_LIMIT:
                createData.value = "5";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.REMOTE_APPROVE_HOUR_LIMIT:
                createData.value = "12:00";
                break;
            case ServerConfigKey.REMOTE_APPROVE_MONT_OFFSET_LIMIT:
                createData.value = "1";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.REDIS_HOST:
                createData.value = "**********";
                createData.isValueMask = true;
                break;
            case ServerConfigKey.REDIS_PORT:
                createData.value = "36379";
                createData.dataType = ConfigDataType.Int;
                createData.isValueMask = true;

                break;
            case ServerConfigKey.REDIS_PASS:
                createData.value = "kVPPDtakzr85W7bd";
                createData.isValueMask = true;
                break;
            case ServerConfigKey.CACHED_FACE_FPS:
                createData.value = "1000";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.CACHED_PERSON_FPS:
                createData.value = "1000";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.FACE_QUALITY_SIMILARITY_THRESHOLD:
                createData.value = "0.55";
                createData.dataType = ConfigDataType.Float;
                break;
            case ServerConfigKey.DUPLICATE_RESULT_TIME_FILTER:
                createData.value = "5";
                createData.dataType = ConfigDataType.Float;
                break;
            case ServerConfigKey.IS_SAVE_EVENT_VIDEO:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.FACE_RES_MQTT_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.HUMAN_RES_MQTT_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            //#region health check
            case ServerConfigKey.HEALTH_CHECK_STORAGE_SIZE:
                createData.value = "100";
                createData.dataType = ConfigDataType.Float;
                break;
            case ServerConfigKey.HEALTH_CHECK_STORAGE_THRESHOLD:
                createData.value = "0.8";
                createData.dataType = ConfigDataType.Float;
                break;
            case ServerConfigKey.HEALTH_CHECK_RAM_SIZE:
                createData.value = "4";
                createData.dataType = ConfigDataType.Float;
                break;
            case ServerConfigKey.HEALTH_CHECK_RAM_THRESHOLD:
                createData.value = "0.8";
                createData.dataType = ConfigDataType.Float;
                break;
            case ServerConfigKey.IS_DUPLICATE_CAMERA_PROCESS:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IS_SAVE_HUMAN_CAPTURE:
                createData.value = "true";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IS_SAVE_HUMAN_CACHED:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IS_SAVE_FACE_CACHED:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IS_DEBUG_EMOTION:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IS_DEBUG_HUMAN:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IS_DEBUG_GENERAL_LOG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            //unknown batch config
            case ServerConfigKey.IS_DEBUG_UNKNOWN_BATCH:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.UNKNOWN_INSERT_BATCH_SIZE:
                createData.value = "100";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.UNKNOWN_INSERT_INTERVAL:
                createData.value = "10000";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.VIDEO_ANALYTICS_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.IPARKING_CUSTOMER_SYNC_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IPARKING_IDENTITY_GROUP_SYNC_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IPARKING_VEHICLE_SYNC_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.IPARKING_IDENTITY_SYNC_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            case ServerConfigKey.PERSON_COUNT_THRESHOLD_ALARM:
                createData.value = "5";
                createData.dataType = ConfigDataType.Int;
                break;
            //#endregion

            //#region core ai debug
            case ServerConfigKey.CORE_AI_SYNC_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;
            //#endregion

            //custome config
            case ServerConfigKey.JSON_CUSTOME_CONFIG:
                createData.value = "";
                break;

            case ServerConfigKey.IS_POPUP_ALARM_ENALBE:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            case ServerConfigKey.DEVICE_API_SERVICE_DEBUG:
                createData.value = "false";
                createData.dataType = ConfigDataType.Boolean;
                break;

            //unknown alarm config
            case ServerConfigKey.UNKNOWN_ALARM_CONFIDENCE_THRESHOLD:
                createData.value = "0.3";
                createData.dataType = ConfigDataType.Float;
                break;

            case ServerConfigKey.UNKNOWN_ALARM_DELAY_SEC:
                createData.value = "4";
                createData.dataType = ConfigDataType.Int;
                break;
            case ServerConfigKey.UNKNOWN_ALARM_QUALITY_THRESHOLD:
                createData.value = "1.6";
                createData.dataType = ConfigDataType.Float;
                break;

        }

        seedingConfigDatas.set(configKey, createData);
    }

    await prisma.serverConfig.createMany({
        skipDuplicates: true,
        data: Array.from(seedingConfigDatas.values())
    });
}

async function seedRole() {
    const exitedRoles = (await prisma.role.findMany({})).map(x => x.id);

    let newRoles: Set<RoleType> = new Set();

    for (var key in RoleType) {
        const roleType: RoleType = RoleType[key];
        if (exitedRoles.includes(roleType)) continue;
        newRoles.add(roleType);
    }

    console.log(`Seeding missing ${newRoles.size} role(s)`)
    console.log(newRoles)
    await prisma.role.createMany({
        data: Array.from(newRoles).map(x => ({ id: x, name: x })),
        skipDuplicates: true,
    })
}

async function seedFuntion() {
    const SuperAdminFunctionIds: FunctionId[] = [];
    for (var key in FunctionId) {
        const funcId: FunctionId = FunctionId[key];
        SuperAdminFunctionIds.push(funcId)
    }
    //Remove not authorrized functions
    let deleted = await prisma.function.deleteMany({
        where: {
            id: {
                notIn: SuperAdminFunctionIds
            }
        }
    });

    //Init all functions
    for (let index = 0; index < SuperAdminFunctionIds.length; index++) {
        const funcId = SuperAdminFunctionIds[index];
        let upsertResult = await prisma.function.upsert({
            where: {
                id: funcId
            }, update: {}, create: {
                id: funcId,
                name: funcId
            }
        });
    }
}

async function seedPermission() {
    const SuperAdminFunctionIds: FunctionId[] = [];
    for (var key in FunctionId) {
        const funcId: FunctionId = FunctionId[key];
        SuperAdminFunctionIds.push(funcId)
    }
    const EmployeeFuntions = [
        {
            functionId: 'DASHBOARD',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USER_REPORT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'ATTANDANT_EXPLAIN',
            canCreate: true,
            canRead: true,
            canUpdate: false,
            canDelete: true
        },
        {
            functionId: 'SHIFT_USER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'FACE_RESULT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_EXPLANATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'TIMEKEEPING',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEVICES',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_TYPE',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USERS',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'COMPANY',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: false,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'FACE_IMAGE_QUALITY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'NOTIFICATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'REMOTE_FACE_RESULT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USER_FEEDBACK',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'ALEART_RULE',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
    ]

    const AdminFunctions = [
        ...EmployeeFuntions,
        {
            functionId: 'USER_REPORT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'LOGIN',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USER_EXPORT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },

        {
            functionId: 'DEVICES',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'CAMERA',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'COMPANY',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'DEVICE_USERS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SHIFTS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'TIMEKEEPING',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USERS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'AC_DEVICES',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'AC_LOCATIONS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'AC_DEPARTMENT_TIMES',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'AC_USER_TIMES',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'AC_TIMES',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SHIFT_EXPLANATION',
            canCreate: true,
            canRead: true,
            canUpdate: false,
            canDelete: true
        },
        {
            functionId: 'SHIFT_TYPE',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },

        {
            functionId: 'PATIENT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'FACE_IMAGE_QUALITY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'NOTIFICATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'REMOTE_ADMIN',
            canCreate: true,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'ORGANIZATION',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_USER_REPORT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USER_GUESTS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'I_PARKING_VIEWER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SECURITY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'ACCESS_CONTROL',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
    ]

    const DepartmentAdminFunctions = [
        ...EmployeeFuntions,
        {
            functionId: 'USER_REPORT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'LOGIN',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USER_EXPORT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'COMPANY',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SHIFTS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'TIMEKEEPING',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USERS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SHIFT_EXPLANATION',
            canCreate: true,
            canRead: true,
            canUpdate: false,
            canDelete: true
        },
        {
            functionId: 'SHIFT_TYPE',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'FACE_IMAGE_QUALITY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'NOTIFICATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'SHIFT_USER_REPORT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
    ]

    const ReceptionFuntions = [
        {
            functionId: 'DASHBOARD',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USER_REPORT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_USER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'FACE_RESULT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_EXPLANATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'TIMEKEEPING',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEVICES',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_TYPE',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USERS',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'PATIENT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'COMPANY',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: false,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'FACE_IMAGE_QUALITY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
    ]

    const AttandantAdminFuntions = [
        {
            functionId: 'USER_REPORT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'ATTANDANT_EXPLAIN',
            canCreate: true,
            canRead: true,
            canUpdate: false,
            canDelete: true
        },
        {
            functionId: 'FACE_RESULT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'TIMEKEEPING',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEVICES',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USERS',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'COMPANY',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: false,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USER_ATTANDANT_TIME',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'ATT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        // ...EmployeeFuntions,
    ]

    const HrFunctions = [
        {
            functionId: 'DASHBOARD',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USER_REPORT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'LOGIN',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USER_EXPORT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'DEVICES',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'CAMERA',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'COMPANY',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: false,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'DEVICE_USERS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SHIFTS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'TIMEKEEPING',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USERS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SHIFT_EXPLANATION',
            canCreate: true,
            canRead: true,
            canUpdate: false,
            canDelete: true
        },
        {
            functionId: 'SHIFT_TYPE',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_USER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'FACE_IMAGE_QUALITY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'NOTIFICATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        ...EmployeeFuntions,
    ]

    const OrgAdminFunctions = [
        {
            functionId: 'DASHBOARD',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USER_REPORT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'ATTANDANT_EXPLAIN',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SHIFT_USER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'FACE_RESULT',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_EXPLANATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'TIMEKEEPING',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'DEVICES',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'SHIFT_TYPE',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'USERS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'COMPANY',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: false,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'FACE_IMAGE_QUALITY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'NOTIFICATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'ORGANIZATION',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'ORGANIZATION_USER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        // {
        //     functionId: 'REMOTE_ADMIN',
        //     canCreate: false,
        //     canRead: true,
        //     canUpdate: false,
        //     canDelete: false
        // },
        {
            functionId: 'REMOTE_FACE_RESULT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        // {
        //     functionId: 'REMOTE_USER',
        //     canCreate: true,
        //     canRead: true,
        //     canUpdate: true,
        //     canDelete: true
        // },
        // {
        //     functionId: 'REMOTE_APPROVE',
        //     canCreate: false,
        //     canRead: true,
        //     canUpdate: false,
        //     canDelete: false
        // },
        ...EmployeeFuntions,
    ]

    const DeviceAdminFunctions = [
        {
            functionId: 'DASHBOARD',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'DEVICES',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SERVER_CONFIG',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        ...EmployeeFuntions,
    ]

    const IparkingViewerFunctions = [
        {
            functionId: 'USERS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'I_PARKING_VIEWER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USER_GUESTS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'COMPANY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
    ]

    const IparkingAdminFunctions = [
        {
            functionId: 'USERS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'I_PARKING_VIEWER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'I_PARKING_ADMIN',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USER_GUESTS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'DEPARTMENT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'COMPANY',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: false
        },
        {
            functionId: 'DEVICES',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'CAMERA',
            canCreate: false,
            canRead: true,
            canUpdate: false,
            canDelete: false
        },
        {
            functionId: 'TIMEKEEPING',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'USER_REPORT',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'ORPHAN_RESULTS',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SEARCH_FEATURE_IMAGE_SERVICE',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'VEHICLE_TRACKING',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'VEHICLE_SEARCH',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'AI_SERVICE',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'DEVICE_CAMERA',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'DASHBOARD',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'DEVICE_CONFIG',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'SHIFT_USER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
        {
            functionId: 'ORGANIZATION_USER',
            canCreate: true,
            canRead: true,
            canUpdate: true,
            canDelete: true
        },
    ]

    //Remove un-allowd permission
    let deleted = await prisma.permisson.deleteMany({
        where: {
            functionId: {
                notIn: SuperAdminFunctionIds
            }
        }
    });

    //Seeding SuperAdmin permissions
    for (let index = 0; index < SuperAdminFunctionIds.length; index++) {
        let funcId: FunctionId = SuperAdminFunctionIds[index];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.SUPER_ADMIN,
                    functionId: funcId
                }
            },
            update: {
                canCread: true,
                canRead: true,
                canUpdate: true,
                canDelete: true
            }, create: {
                roleId: RoleType.SUPER_ADMIN,
                functionId: funcId,
                canCread: true,
                canRead: true,
                canUpdate: true,
                canDelete: true,

            }
        })
    }

    //Seeding Admin permisstions
    for (let index = 0; index < AdminFunctions.length; index++) {
        const adminFunction = AdminFunctions[index];
        let funcId: FunctionId = FunctionId[adminFunction.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.ADMIN,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.ADMIN,
                functionId: funcId,
                canCread: adminFunction.canCreate,
                canRead: adminFunction.canRead,
                canUpdate: adminFunction.canUpdate,
                canDelete: adminFunction.canDelete
            }
        });
    }

    //Seeding Department Admin permisstions
    for (let index = 0; index < DepartmentAdminFunctions.length; index++) {
        const departmentAdminFunction = DepartmentAdminFunctions[index];
        let funcId: FunctionId = FunctionId[departmentAdminFunction.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.DEPARTMENT_ADMIN,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.DEPARTMENT_ADMIN,
                functionId: funcId,
                canCread: departmentAdminFunction.canCreate,
                canRead: departmentAdminFunction.canRead,
                canUpdate: departmentAdminFunction.canUpdate,
                canDelete: departmentAdminFunction.canDelete
            }
        });
    }

    //Seeding Attandant Admin permisstions
    for (let index = 0; index < AttandantAdminFuntions.length; index++) {
        const attandantAdminFuntion = AttandantAdminFuntions[index];
        let funcId: FunctionId = FunctionId[attandantAdminFuntion.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.ATTANDANT_ADMIN,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.ATTANDANT_ADMIN,
                functionId: funcId,
                canCread: attandantAdminFuntion.canCreate,
                canRead: attandantAdminFuntion.canRead,
                canUpdate: attandantAdminFuntion.canUpdate,
                canDelete: attandantAdminFuntion.canDelete
            }
        });
    }

    //#region Employees permisstions
    for (let index = 0; index < EmployeeFuntions.length; index++) {
        const empFunction = EmployeeFuntions[index];
        let funcId: FunctionId = FunctionId[empFunction.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.EMPLOYEE,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.EMPLOYEE,
                functionId: funcId,
                canCread: empFunction.canCreate,
                canRead: empFunction.canRead,
                canUpdate: empFunction.canUpdate,
                canDelete: empFunction.canDelete
            }
        });
    }
    //#endregion

    //#region Reception permisstions
    for (let index = 0; index < ReceptionFuntions.length; index++) {
        const receptionFunc = ReceptionFuntions[index];
        let funcId: FunctionId = FunctionId[receptionFunc.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.RECEPTION,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.RECEPTION,
                functionId: funcId,
                canCread: receptionFunc.canCreate,
                canRead: receptionFunc.canRead,
                canUpdate: receptionFunc.canUpdate,
                canDelete: receptionFunc.canDelete
            }
        });
    }
    //#endregion

    //#region HR permisstions
    for (let index = 0; index < HrFunctions.length; index++) {
        const hrFunction = HrFunctions[index];
        let funcId: FunctionId = FunctionId[hrFunction.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.HR,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.HR,
                functionId: funcId,
                canCread: hrFunction.canCreate,
                canRead: hrFunction.canRead,
                canUpdate: hrFunction.canUpdate,
                canDelete: hrFunction.canDelete
            }
        });
    }
    //#endregion

    //#region ORG_ADMIN permisstions
    for (let index = 0; index < OrgAdminFunctions.length; index++) {
        const orgAdminFunction = OrgAdminFunctions[index];
        let funcId: FunctionId = FunctionId[orgAdminFunction.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.ORG_ADMIN,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.ORG_ADMIN,
                functionId: funcId,
                canCread: orgAdminFunction.canCreate,
                canRead: orgAdminFunction.canRead,
                canUpdate: orgAdminFunction.canUpdate,
                canDelete: orgAdminFunction.canDelete
            }
        });
    }
    //#endregion

    //#region ORG_ADMIN permisstions
    for (let index = 0; index < DeviceAdminFunctions.length; index++) {
        const devAdminFunction = DeviceAdminFunctions[index];
        let funcId: FunctionId = FunctionId[devAdminFunction.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.DEVICE_ADMIN,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.DEVICE_ADMIN,
                functionId: funcId,
                canCread: devAdminFunction.canCreate,
                canRead: devAdminFunction.canRead,
                canUpdate: devAdminFunction.canUpdate,
                canDelete: devAdminFunction.canDelete
            }
        });
    }
    //#endregion

    //#region I_PARKING_VIEWER permisstions
    for (let index = 0; index < IparkingViewerFunctions.length; index++) {
        const iparkingViewerFunction = IparkingViewerFunctions[index];
        let funcId: FunctionId = FunctionId[iparkingViewerFunction.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.I_PARKING_VIEWER,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.I_PARKING_VIEWER,
                functionId: funcId,
                canCread: iparkingViewerFunction.canCreate,
                canRead: iparkingViewerFunction.canRead,
                canUpdate: iparkingViewerFunction.canUpdate,
                canDelete: iparkingViewerFunction.canDelete
            }
        });
    }
    //#endregion
    //#region I_PARKING_ADMIN permisstions
    for (let index = 0; index < IparkingAdminFunctions.length; index++) {
        const iparkingAdminFunction = IparkingAdminFunctions[index];
        let funcId: FunctionId = FunctionId[iparkingAdminFunction.functionId];
        await prisma.permisson.upsert({
            where: {
                roleId_functionId: {
                    roleId: RoleType.I_PARKING_ADMIN,
                    functionId: funcId
                }
            }, update: {}, create: {
                roleId: RoleType.I_PARKING_ADMIN,
                functionId: funcId,
                canCread: iparkingAdminFunction.canCreate,
                canRead: iparkingAdminFunction.canRead,
                canUpdate: iparkingAdminFunction.canUpdate,
                canDelete: iparkingAdminFunction.canDelete
            }
        });
    }
    //#endregion
}

async function seedCompany() {
    let countCom = await prisma.company.count();
    if (countCom == 0) {
        await prisma.company.create({
            data: {
                name: "CATI",
            }
        });
    }
}

async function seedDepartment() {
    let countDep = await prisma.department.count();
    if (countDep == 0) {
        let company = await prisma.company.findFirst();
        if (company)
            await prisma.department.create({
                data: {
                    name: "Admin",
                    companyId: company.id
                }
            });
        else console.error("Seeding company/department failed");
    }
}

async function seedFilePathConfig() {
    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.FACE_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.FACE_IMAGE_PATH,
            value: "face-images"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.IMAGE_PATH,
            value: "images"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.AVATAR_PATH },
        update: {},
        create: {
            key: ServerConfigKey.AVATAR_PATH,
            value: "images/avatar"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.USER_PACK_PATH },
        update: {},
        create: {
            key: ServerConfigKey.USER_PACK_PATH,
            value: "user-packs"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.USER_BACKUP_PATH },
        update: {},
        create: {
            key: ServerConfigKey.USER_BACKUP_PATH,
            value: "user-backups"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.GUEST_PATH },
        update: {},
        create: {
            key: ServerConfigKey.GUEST_PATH,
            value: "guest"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.GUEST_QR_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.GUEST_QR_IMAGE_PATH,
            value: "guest/qrcode"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.GUEST_CARD_IMAGE },
        update: {},
        create: {
            key: ServerConfigKey.GUEST_CARD_IMAGE,
            value: "guest/card-images"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.GUEST_FACE_IMAGE },
        update: {},
        create: {
            key: ServerConfigKey.GUEST_FACE_IMAGE,
            value: "guest/face-images"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.EKYC_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.EKYC_IMAGE_PATH,
            value: "ekyc-images"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.EKYC_CROPPED_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.EKYC_CROPPED_IMAGE_PATH,
            value: "ekyc-images/cropped"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.REMOTE_ATTANDANT_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.REMOTE_ATTANDANT_IMAGE_PATH,
            value: "remote-attendance"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.SERCURITY_EVENT_PATH },
        update: {},
        create: {
            key: ServerConfigKey.SERCURITY_EVENT_PATH,
            value: "security-events"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.BREAK_IN_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.BREAK_IN_IMAGE_PATH,
            value: "security-events/break-in"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.LOITERING_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.LOITERING_IMAGE_PATH,
            value: "security-events/loitering"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.PATIENT_PATH },
        update: {},
        create: {
            key: ServerConfigKey.PATIENT_PATH,
            value: "patients"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.PATIENT_AVATAR_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.PATIENT_AVATAR_IMAGE_PATH,
            value: "patients/avatar"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.PATIENT_FACE_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.PATIENT_FACE_IMAGE_PATH,
            value: "patients/face-images"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.PATIENT_HOMEMATE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.PATIENT_HOMEMATE_PATH,
            value: "patients-home-mate"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.PATIENT_HOMEMATE_AVATAR_PATH },
        update: {},
        create: {
            key: ServerConfigKey.PATIENT_HOMEMATE_AVATAR_PATH,
            value: "patients-home-mate/avatar"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.PATIENT_HOMEMATE_FACE_IMAGE_PATH },
        update: {},
        create: {
            key: ServerConfigKey.PATIENT_HOMEMATE_FACE_IMAGE_PATH,
            value: "patients-home-mate/face-images"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.VEHICLE_SEARCH_DEFAULT_DEVICE_ID },
        update: {},
        create: {
            key: ServerConfigKey.VEHICLE_SEARCH_DEFAULT_DEVICE_ID,
            value: "vehicle_reid_matcher"
        }
    })

    await prisma.serverConfig.upsert({
        where: { key: ServerConfigKey.PERSON_SEARCH_DEFAULT_DEVICE_ID },
        update: {},
        create: {
            key: ServerConfigKey.PERSON_SEARCH_DEFAULT_DEVICE_ID,
            value: "civams_person_reid_POC_matcher"
        }
    })
}

async function seedWebLayout() {
    const webPaths = [
        //#region Dashboard
        // { label: "Dashboard", path: "/dashboards/events", icon: "fas fa-tachometer-alt" },
        { label: "Dashboard", path: "/dashboards", icon: "fas fa-tachometer-alt", isCollapsible: true },
        { label: "Events", path: "/dashboards/events", icon: "fas fa-chart-line" },
        { label: "Monitor", path: "/dashboards/monitor", icon: "fas fa-border-all" },
        // { label: "Video Wall", path: "/dashboards/video-wall", icon: "fas fa-border-all" },
        //#endregion

        //#region Admin
        { label: "Admin", path: "/roles", icon: "fas fa-user-lock", isCollapsible: true },
        { label: "User Roles", path: "/roles/role", icon: "fas fa-users-cog" },
        { label: "Permission", path: "/roles/permission", icon: "fas fa-user-cog" },
        //#endregion

        //#region Users
        { label: "Users", path: "/usr", icon: "fas fa-user-circle", isCollapsible: true },
        { label: "All Users", path: "/usr/all-users", icon: "fas fa-user-circle" },
        { label: "VIP Users", path: "/usr/vip", icon: "fas fa-user-tie" },
        { label: "Black List", path: "/usr/black-list", icon: "fas fa-user-secret" },
        { label: "Organizations", path: "/usr/com-dept", icon: "fas fa-building" },
        { label: "Timeshift", path: "/usr/time-shift", icon: "fas fa-user-clock" },
        // { label: "Shift Report", path: "/usr/shift-report", icon: "fas fa-file" },
        { label: "Shift Explanation", path: "/usr/shift-explanation", icon: "fas fa-circle-exclamation" },
        { label: "User In/Out", path: "/usr/user-in-out", icon: "fas fa-file" },
        //#endregion

        //#region Shift management
        { label: "Shift Management", path: "/shift-management", icon: "fas fa-file-lines", isCollapsible: true },
        { label: "All Shift Type", path: "/shift-management/all-shift-type", icon: "fas fa-file-circle-check", isCollapsible: true },
        { label: "Shift Assignment", path: "/shift-management/shift-assignment", icon: "fas fa-file-signature", isCollapsible: true },
        // { label: "Shift User", path: "/shift-management/shift-report", icon: "fas fa-file", isCollapsible: true },
        //#endregion

        //#region Attandant
        { label: "Attendant", path: "/attendant", icon: "fas fa-user-check", isCollapsible: true },
        { label: "History", path: "/attendant/history", icon: "fas fa-history" },
        { label: "Attendant Time", path: "/attendant/att-time", icon: "fas fa-calendar-week" },
        { label: "User Config", path: "/attendant/user-config", icon: "fas fa-pen" },
        //#endregion

        //#region Guest
        { label: "Guests", path: "/guest", icon: "fas fa-user", isCollapsible: true },
        { label: "Guests", path: "/guest/guests", icon: "fas fa-user" },
        { label: "History", path: "/guest/history", icon: "fas fa-history" },
        { label: "Access", path: "/guest/access", icon: "fas fa-door-open" },
        //#endregion

        //#region Student
        { label: "School", path: "/school", icon: "fas fa-school", isCollapsible: true },
        { label: "All Students", path: "/school/all-students", icon: "fas fa-graduation-cap" },
        //#endregion

        //#region Medical
        { label: "Medical", path: "/medical", icon: "fas fa-hospital", isCollapsible: true },
        { label: "Patients", path: "/medical/patients", icon: "fas fa-solid fa-file-medical" },
        { label: "Access", path: "/medical/access", icon: "fas fa-door-open" },
        //#endregion

        //#region FaceID
        { label: "FaceID", path: "/faceid", icon: "fas fa-smile-beam", isCollapsible: true },
        { label: "History", path: "/faceid/check-in-out-data", icon: "fas fa-history" },
        { label: "Unknown", path: "/faceid/timekeeping_unknown", icon: "fas fa-meh-blank" },
        { label: "Face Results", path: "/faceid/face-result", icon: "fas fa-smile-beam" },
        { label: "Reports", path: "/faceid/report", icon: "fas fa-table" },
        // { label: "Search By Face", path: "/faceid/unknown-search", icon: "fas fa-search" },
        //#endregion

        //#region Security
        { label: "Security", path: "/security", icon: "fas fa-building-shield", isCollapsible: true },
        { label: "Human Tracking", path: "/security/mct", icon: "fas fa-arrows-down-to-people" },
        { label: "Human Action", path: "/security/human-action", icon: "fas fa-person-falling-burst" },
        { label: "Smart Search", path: "/security/search-feature-image", icon: "fas fa-person-circle-question" },
        { label: "Social Search", path: "/security/social-search", icon: "fa-brands fa-square-facebook" },
        { label: "Alert Rules", path: "/security/alert-rule", icon: "fas fa-bell" },
        //#endregion

        //#region Devices
        { label: "Devices", path: "/devices", icon: "fas fa-hard-drive", isCollapsible: true },
        { label: "AI Devices", path: "/devices/all-devices", icon: "fas fa-microchip" },
        { label: "AI Services", path: "/devices/ai-services", icon: "fas fa-boxes-stacked" },
        { label: "Camera", path: "/devices/camera", icon: "fas fa-video" },
        { label: "Device Group", path: "/devices/device-group", icon: "fas fa-group-arrows-rotate" },
        //#endregion

        //#region Access Control
        { label: "Access Control", path: "/security/access-control", icon: "fas fa-door-closed" },
        //#endregion

        //#region Server Settings
        { label: "Settings", path: "/settings", icon: "fas fa-gear", isCollapsible: true },
        { label: "OTT Config", path: "/settings/ott", icon: "fas fa-server" },
        { label: "Server Config", path: "/settings/server-settings", icon: "fas fa-server" },
        { label: "System Log", path: "/settings/system-log", icon: "fas fa-pen" },
        { label: "Data Backup", path: "/settings/data-backup", icon: "fas fa-floppy-disk" },
        //#endregion
    ];
    for (let index = 0; index < webPaths.length; index++) {
        webPaths[index]['index'] = index;
    }

    //#region Seed pages
    //Delete un-authen pages
    await prisma.webPage.deleteMany({
        where: { index: { not: { in: webPaths.map(webPath => webPath['index']) } } }
    });
    await prisma.webPage.deleteMany({
        where: { path: { not: { in: webPaths.map(webPath => webPath.path) } } },
    });
    await prisma.webPage.deleteMany({
        where: { label: { not: { in: webPaths.map(webPath => webPath.label) } } }
    });

    //Upsert indexes
    for (let index = 0; index < webPaths.length; index++) {
        const webPath = webPaths[index];
        await prisma.webPage.upsert({
            where: { path: webPath.path },
            update: {
                path: webPath.path,
                label: webPath.label,
                icon: webPath.icon,
                isCollapsible: webPath.isCollapsible,
                index: webPath['index'],
            },
            create: {
                path: webPath.path,
                label: webPath.label,
                icon: webPath.icon,
                isCollapsible: webPath.isCollapsible,
                index: webPath['index'],
            }
        });
    }
    //#endregion

    //#region Seed super admin pages
    await prisma.webLayout.deleteMany({
        where: { path: { not: { in: webPaths.map(webPath => webPath.path) } } }
    });

    for (let index = 0; index < webPaths.length; index++) {
        const webPath = webPaths[index];
        await prisma.webLayout.upsert({
            where: {
                roleId_path: {
                    roleId: RoleType.SUPER_ADMIN,
                    path: webPath.path
                }
            },
            update: {},
            create: {
                roleId: RoleType.SUPER_ADMIN,
                WebPage: { connect: { path: webPath.path } }
            }
        });
    }
    //#endregion

    //#region Seeding Admin pages
    let adminPathUnAuth = [
        "/dashboards/video-wall",
        "/roles", "/roles/role", "/roles/permission",
        "/usr/vip", "/usr/black-list", "/usr/com-dept",
        "/settings", "/settings/server-settings", "/settings/system-log",
        "/faceid/unknown-search",
        "/guest", "/guest/guests", "/guest/access",
        "/medical", "/medical/patients", "/medical/access",
        "/permission",
    ];
    let adminPaths = webPaths.filter(path => !adminPathUnAuth.includes(path.path))
        .map(p => p.path);

    await prisma.webLayout.deleteMany({
        where: {
            roleId: RoleType.ADMIN,
            path: { notIn: adminPaths }
        }
    });
    for (let index = 0; index < adminPaths.length; index++) {
        const adminPath = adminPaths[index];
        await prisma.webLayout.upsert({
            where: {
                roleId_path: {
                    roleId: RoleType.ADMIN,
                    path: adminPath
                }
            },
            update: {},
            create: {
                roleId: RoleType.ADMIN,
                WebPage: { connect: { path: adminPath } }
            }
        });
    }
    //#endregion

    //#region Seeding HR pages
    let hRPathUnAuth = ["/settings", "/settings/server-settings", "/settings/system-log",
        "/faceid/unknown-search",
        "/roles", "/roles/role",
        "/guest", "/guest/guests", "/guest/access",
        "/medical", "/medical/patients", "/medical/access",
        "/devices", "/devices/all-devices", "/devices/camera",
        "/security/access-control", "/devices/all-devices",
        "/usr/com-dept",
        "/attendant", "/attendant/history", "/attendant/user-config", "/attendant/att-time",
        "/permission",];
    let hrPaths = webPaths.filter(path => !hRPathUnAuth.includes(path.path))
        .map(p => p.path);

    await prisma.webLayout.deleteMany({
        where: {
            roleId: RoleType.HR,
            path: { notIn: hrPaths }
        }
    });
    for (let index = 0; index < hrPaths.length; index++) {
        const hrPath = hrPaths[index];
        await prisma.webLayout.upsert({
            where: {
                roleId_path: {
                    roleId: RoleType.HR,
                    path: hrPath
                }
            },
            update: {},
            create: {
                roleId: RoleType.HR,
                WebPage: { connect: { path: hrPath } }
            }
        });
    }
    //#endregion

    //#region Seeding Employee pages
    let employePathUnAuth = ["/usr/all-users", "/usr/com-dept", "/usr/shift-report", "/usr/user-in-out",
        "/roles", "/roles/role",
        "/guest", "/guest/guests", "/guest/access",
        "/medical", "/medical/patients", "/medical/access",
        "/devices", "/devices/all-devices", "/devices/camera",
        "/security/access-control", "/devices/all-devices",
        "/permission",
        "/attendant", "/attendant/history", "/attendant/user-config", "/attendant/att-time",
        "/settings", "/settings/server-settings", "/settings/system-log",
        "/faceid/unknown-search", "/faceid/report"
    ];
    let employeePaths = webPaths.filter(path => !employePathUnAuth.includes(path.path))
        .map(p => p.path);

    await prisma.webLayout.deleteMany({
        where: {
            roleId: RoleType.EMPLOYEE,
            path: { notIn: employeePaths }
        }
    });
    for (let index = 0; index < employeePaths.length; index++) {
        const employeePath = employeePaths[index];
        await prisma.webLayout.upsert({
            where: {
                roleId_path: {
                    roleId: RoleType.EMPLOYEE,
                    path: employeePath
                }
            },
            update: {},
            create: {
                roleId: RoleType.EMPLOYEE,
                WebPage: { connect: { path: employeePath } }
            }
        });
    }
    //#endregion

    //#region Seeding Org admin pages
    let orgAdminPathUnAuth = [
        "/roles", "/roles/role",
        "/guest", "/guest/guests", "/guest/access",
        "/medical", "/medical/patients", "/medical/access",
        "/devices", "/devices/all-devices", "/devices/camera",
        "/security/access-control", "/devices/all-devices",
        "/permission",
        "/attendant", "/attendant/history", "/attendant/user-config", "/attendant/att-time",
        "/settings", "/settings/server-settings", "/settings/system-log",
        "/faceid/unknown-search", "/faceid/report"
    ];
    let orgAdminPaths = webPaths.filter(path => !orgAdminPathUnAuth.includes(path.path))
        .map(p => p.path);

    await prisma.webLayout.deleteMany({
        where: {
            roleId: RoleType.ORG_ADMIN,
            path: { notIn: orgAdminPaths }
        }
    });
    for (let index = 0; index < orgAdminPaths.length; index++) {
        const orgAdminPath = orgAdminPaths[index];
        await prisma.webLayout.upsert({
            where: {
                roleId_path: {
                    roleId: RoleType.ORG_ADMIN,
                    path: orgAdminPath
                }
            },
            update: {},
            create: {
                roleId: RoleType.ORG_ADMIN,
                WebPage: { connect: { path: orgAdminPath } }
            }
        });
    }
    //#endregion

    //#region Seeding Reception pages
    let receptionPathUnAuth = ["/usr/all-users", "/usr/com-dept", "/usr/shift-report", "/usr/user-in-out",
        "/guest", "/guest/guests", "/guest/access",
        "/medical/access",
        "/roles", "/roles/role",
        "/security/access-control", "/devices/all-devices",
        "/permission",
        "/devices", "/devices/all-devices", "/devices/camera",
        "/attendant", "/attendant/history", "/attendant/user-config", "/attendant/att-time",
        "/settings", "/settings/server-settings", "/settings/system-log",
        "/faceid/unknown-search", "/faceid/report"
    ];
    let receptionPaths = webPaths.filter(path => !receptionPathUnAuth.includes(path.path))
        .map(p => p.path);

    await prisma.webLayout.deleteMany({
        where: {
            roleId: RoleType.RECEPTION,
            path: { notIn: receptionPaths }
        }
    });
    for (let index = 0; index < receptionPaths.length; index++) {
        const receptionPath = receptionPaths[index];
        await prisma.webLayout.upsert({
            where: {
                roleId_path: {
                    roleId: RoleType.RECEPTION,
                    path: receptionPath
                }
            },
            update: {},
            create: {
                roleId: RoleType.RECEPTION,
                WebPage: { connect: { path: receptionPath } }
            }
        });
    }
    //#endregion

    //#region Seeding Attendant ADmin pages
    let attAdminPathUnAuth = ["/usr/all-users", "/usr/com-dept", "/usr/shift-report", "/usr/user-in-out",
        "/roles", "/roles/role",
        "/guest", "/guest/guests", "/guest/access",
        "/medical", "/medical/patients", "/medical/access",
        "/devices", "/devices/all-devices", "/devices/camera",
        "/security/access-control", "/devices/all-devices",
        "/permission",
        "/settings", "/settings/server-settings", "/settings/system-log",
        "/faceid/unknown-search", "/faceid/report"
    ];
    let attAdminPaths = webPaths.filter(path => !attAdminPathUnAuth.includes(path.path))
        .map(p => p.path);

    await prisma.webLayout.deleteMany({
        where: {
            roleId: RoleType.ATTANDANT_ADMIN,
            path: { notIn: attAdminPaths }
        }
    });
    for (let index = 0; index < attAdminPaths.length; index++) {
        const attAdminPath = attAdminPaths[index];
        await prisma.webLayout.upsert({
            where: {
                roleId_path: {
                    roleId: RoleType.ATTANDANT_ADMIN,
                    path: attAdminPath
                }
            },
            update: {},
            create: {
                roleId: RoleType.ATTANDANT_ADMIN,
                WebPage: { connect: { path: attAdminPath } }
            }
        });
    }
    //#endregion
}

main()
    .catch((e) => {
        console.error(e)
        process.exit(1)
    })
    .finally(async () => {
        await prisma.$disconnect()
    })