-- Test timezone fix

-- 1. TEST VIOLATION COUNT SERVICE LOGIC AFTER FIX
SELECT 'After timezone fix - violation count' as test_type;

-- Uniform violations (Monday only + timezone corrected)
SELECT 
    'Uniform violations (Monday + timezone corrected)' as metric,
    COUNT(DISTINCT (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::date) as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::date = shift_user."startDate"::date
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= shift_type."startHour"::time
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= shift_type."endHour"::time
    AND EXTRACT(DOW FROM (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')) = 1  -- Monday only
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" = 16

UNION ALL

-- Card violations (all days + timezone corrected)
SELECT 
    'Card violations (all days + timezone corrected)' as metric,
    COUNT(DISTINCT attr."trackId") as count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::date = shift_user."startDate"::date
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= shift_type."startHour"::time
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= shift_type."endHour"::time
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" = 18
    AND NOT EXISTS (
        SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
        WHERE attr2."trackId" = attr."trackId"
        AND attr2."type" = 17
    );

-- 2. DETAILED ANALYSIS OF SPECIFIC VIOLATIONS
SELECT 'Detailed analysis after timezone fix' as test_type;

SELECT 
    attr."trackId",
    attr."dateCreated" as original_utc,
    (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh') as vietnam_time,
    (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time as vietnam_time_only,
    st."startHour",
    st."endHour",
    
    -- Check if within working hours (timezone corrected)
    CASE 
        WHEN (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= st."startHour"::time
             AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= st."endHour"::time
        THEN '✅ Within working hours'
        ELSE '❌ Outside working hours'
    END as working_hours_check,
    
    -- Check day of week (timezone corrected)
    EXTRACT(DOW FROM (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')) as day_of_week,
    CASE 
        WHEN EXTRACT(DOW FROM (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')) = 1
        THEN '✅ Monday'
        ELSE '❌ Not Monday'
    END as monday_check,
    
    -- Final result for uniform violations
    CASE 
        WHEN (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::date = su."startDate"::date
             AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= st."startHour"::time
             AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= st."endHour"::time
             AND EXTRACT(DOW FROM (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')) = 1
             AND attr."type" = 16
        THEN '✅ UNIFORM VIOLATION COUNTED'
        WHEN (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::date = su."startDate"::date
             AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= st."startHour"::time
             AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= st."endHour"::time
             AND attr."type" = 18
        THEN '✅ CARD VIOLATION COUNTED'
        ELSE '❌ VIOLATION EXCLUDED'
    END as final_result

FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::date = su."startDate"::date
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)
ORDER BY attr."dateCreated";

-- 3. COMPARISON: BEFORE vs AFTER TIMEZONE FIX
SELECT 'Comparison: Before vs After timezone fix' as test_type;

SELECT 
    'Before timezone fix (original logic)' as method,
    COUNT(*) as violation_count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND attr."dateCreated"::date = su."startDate"::date
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND attr."dateCreated"::time >= st."startHour"::time
    AND attr."dateCreated"::time <= st."endHour"::time
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18)

UNION ALL

SELECT 
    'After timezone fix (corrected logic)' as method,
    COUNT(*) as violation_count
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" su ON result."userIdReCheckResult" = su."userId"
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::date = su."startDate"::date
INNER JOIN "ShiftType" st ON su."shiftId" = st."code"
WHERE attr."dateCreated" >= '2025-06-01'::date
    AND attr."dateCreated" <= '2025-06-30'::date + interval '1 day'
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time >= st."startHour"::time
    AND (attr."dateCreated" AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh')::time <= st."endHour"::time
    AND result."userIdReCheckResult" = '62417db10f022fca95fa3e40'
    AND attr."type" IN (16, 18);
