-- <PERSON><PERSON><PERSON> để xem chi tiết tất cả vi phạm
-- HƯỚNG DẪN: Thay đổi userId và date range bên dưới

-- ===== THAY ĐỔI CÁC GIÁ TRỊ NÀY =====
WITH test_params AS (
    SELECT 
        'your-user-id' as user_id,
        '2024-12-01 00:00:00'::timestamp as start_date,
        '2024-12-31 23:59:59'::timestamp as end_date
)

-- ===== 1. CHI TIẾT VI PHẠM ĐỒNG PHỤC =====
SELECT '=== VI PHẠM ĐỒNG PHỤC (CHỈ TÍNH THỨ 2) ===' as section_title;

SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::date as violation_date,
    attr."dateCreated"::time as violation_time,
    EXTRACT(DOW FROM attr."dateCreated") as day_of_week,
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'Chủ nhật'
        WHEN 1 THEN 'Th<PERSON> 2 ✅'
        WHEN 2 THEN 'Thứ 3'
        WHEN 3 THEN 'Thứ 4'
        WHEN 4 THEN 'Thứ 5'
        WHEN 5 THEN 'Thứ 6'
        WHEN 6 THEN 'Thứ 7'
    END as day_name,
    shift_user."shiftId",
    shift_type."startHour" as ca_bat_dau,
    shift_type."endHour" as ca_ket_thuc,
    shift_type."startBreak" as nghi_bat_dau,
    shift_type."endBreak" as nghi_ket_thuc,
    CASE 
        WHEN attr."dateCreated"::time < shift_type."startHour"::time 
             OR attr."dateCreated"::time > shift_type."endHour"::time
        THEN 'Ngoài giờ làm ❌'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
             AND shift_type."startBreak" != '00:00' 
             AND shift_type."endBreak" != '00:00'
        THEN 'Giờ nghỉ ❌'
        ELSE 'Giờ làm việc ✅'
    END as time_status,
    CASE 
        WHEN EXTRACT(DOW FROM attr."dateCreated") = 1 
             AND attr."dateCreated"::time >= shift_type."startHour"::time
             AND attr."dateCreated"::time <= shift_type."endHour"::time
             AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                     AND attr."dateCreated"::time <= shift_type."endBreak"::time
                     AND shift_type."startBreak" != '00:00' 
                     AND shift_type."endBreak" != '00:00')
        THEN 'TÍNH VÀO ✅'
        ELSE 'KHÔNG TÍNH ❌'
    END as final_count_status,
    'Manual recheck' as source_type
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
CROSS JOIN test_params tp
WHERE attr."dateCreated" >= tp.start_date
    AND attr."dateCreated" <= tp.end_date
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = tp.user_id
    AND attr."type" = 16  -- none_ppe_body

UNION ALL

SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::date as violation_date,
    attr."dateCreated"::time as violation_time,
    EXTRACT(DOW FROM attr."dateCreated") as day_of_week,
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'Chủ nhật'
        WHEN 1 THEN 'Thứ 2 ✅'
        WHEN 2 THEN 'Thứ 3'
        WHEN 3 THEN 'Thứ 4'
        WHEN 4 THEN 'Thứ 5'
        WHEN 5 THEN 'Thứ 6'
        WHEN 6 THEN 'Thứ 7'
    END as day_name,
    shift_user."shiftId",
    shift_type."startHour" as ca_bat_dau,
    shift_type."endHour" as ca_ket_thuc,
    shift_type."startBreak" as nghi_bat_dau,
    shift_type."endBreak" as nghi_ket_thuc,
    CASE 
        WHEN attr."dateCreated"::time < shift_type."startHour"::time 
             OR attr."dateCreated"::time > shift_type."endHour"::time
        THEN 'Ngoài giờ làm ❌'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
             AND shift_type."startBreak" != '00:00' 
             AND shift_type."endBreak" != '00:00'
        THEN 'Giờ nghỉ ❌'
        ELSE 'Giờ làm việc ✅'
    END as time_status,
    CASE 
        WHEN EXTRACT(DOW FROM attr."dateCreated") = 1 
             AND attr."dateCreated"::time >= shift_type."startHour"::time
             AND attr."dateCreated"::time <= shift_type."endHour"::time
             AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                     AND attr."dateCreated"::time <= shift_type."endBreak"::time
                     AND shift_type."startBreak" != '00:00' 
                     AND shift_type."endBreak" != '00:00')
        THEN 'TÍNH VÀO ✅'
        ELSE 'KHÔNG TÍNH ❌'
    END as final_count_status,
    'Face recognition' as source_type
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
CROSS JOIN test_params tp
WHERE attr."dateCreated" >= tp.start_date
    AND attr."dateCreated" <= tp.end_date
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND face."userId" = tp.user_id
    AND attr."type" = 16  -- none_ppe_body
    AND NOT EXISTS (
        SELECT 1 FROM "CoreAiHumanTrackResult" r
        WHERE r."humanTrackId" = attr."trackId"
        AND r."userIdReCheckResult" IS NOT NULL
    )
ORDER BY violation_date, violation_time;

-- ===== 2. CHI TIẾT VI PHẠM THẺ =====
SELECT '=== VI PHẠM THẺ (TẤT CẢ NGÀY) ===' as section_title;

SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::date as violation_date,
    attr."dateCreated"::time as violation_time,
    EXTRACT(DOW FROM attr."dateCreated") as day_of_week,
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'Chủ nhật'
        WHEN 1 THEN 'Thứ 2'
        WHEN 2 THEN 'Thứ 3'
        WHEN 3 THEN 'Thứ 4'
        WHEN 4 THEN 'Thứ 5'
        WHEN 5 THEN 'Thứ 6'
        WHEN 6 THEN 'Thứ 7'
    END as day_name,
    shift_user."shiftId",
    shift_type."startHour" as ca_bat_dau,
    shift_type."endHour" as ca_ket_thuc,
    shift_type."startBreak" as nghi_bat_dau,
    shift_type."endBreak" as nghi_ket_thuc,
    CASE 
        WHEN attr."dateCreated"::time < shift_type."startHour"::time 
             OR attr."dateCreated"::time > shift_type."endHour"::time
        THEN 'Ngoài giờ làm ❌'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
             AND shift_type."startBreak" != '00:00' 
             AND shift_type."endBreak" != '00:00'
        THEN 'Giờ nghỉ ❌'
        ELSE 'Giờ làm việc ✅'
    END as time_status,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17  -- wearing_employee_card
        ) THEN 'Có thẻ ❌'
        ELSE 'Không có thẻ ✅'
    END as card_status,
    CASE 
        WHEN attr."dateCreated"::time >= shift_type."startHour"::time
             AND attr."dateCreated"::time <= shift_type."endHour"::time
             AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                     AND attr."dateCreated"::time <= shift_type."endBreak"::time
                     AND shift_type."startBreak" != '00:00' 
                     AND shift_type."endBreak" != '00:00')
             AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                WHERE attr2."trackId" = attr."trackId"
                AND attr2."type" = 17
             )
        THEN 'TÍNH VÀO ✅'
        ELSE 'KHÔNG TÍNH ❌'
    END as final_count_status,
    'Manual recheck' as source_type
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
CROSS JOIN test_params tp
WHERE attr."dateCreated" >= tp.start_date
    AND attr."dateCreated" <= tp.end_date
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND result."userIdReCheckResult" = tp.user_id
    AND attr."type" = 18  -- no_employee_card

UNION ALL

SELECT 
    attr."trackId",
    attr."dateCreated",
    attr."dateCreated"::date as violation_date,
    attr."dateCreated"::time as violation_time,
    EXTRACT(DOW FROM attr."dateCreated") as day_of_week,
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'Chủ nhật'
        WHEN 1 THEN 'Thứ 2'
        WHEN 2 THEN 'Thứ 3'
        WHEN 3 THEN 'Thứ 4'
        WHEN 4 THEN 'Thứ 5'
        WHEN 5 THEN 'Thứ 6'
        WHEN 6 THEN 'Thứ 7'
    END as day_name,
    shift_user."shiftId",
    shift_type."startHour" as ca_bat_dau,
    shift_type."endHour" as ca_ket_thuc,
    shift_type."startBreak" as nghi_bat_dau,
    shift_type."endBreak" as nghi_ket_thuc,
    CASE 
        WHEN attr."dateCreated"::time < shift_type."startHour"::time 
             OR attr."dateCreated"::time > shift_type."endHour"::time
        THEN 'Ngoài giờ làm ❌'
        WHEN attr."dateCreated"::time >= shift_type."startBreak"::time 
             AND attr."dateCreated"::time <= shift_type."endBreak"::time
             AND shift_type."startBreak" != '00:00' 
             AND shift_type."endBreak" != '00:00'
        THEN 'Giờ nghỉ ❌'
        ELSE 'Giờ làm việc ✅'
    END as time_status,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17  -- wearing_employee_card
        ) THEN 'Có thẻ ❌'
        ELSE 'Không có thẻ ✅'
    END as card_status,
    CASE 
        WHEN attr."dateCreated"::time >= shift_type."startHour"::time
             AND attr."dateCreated"::time <= shift_type."endHour"::time
             AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                     AND attr."dateCreated"::time <= shift_type."endBreak"::time
                     AND shift_type."startBreak" != '00:00' 
                     AND shift_type."endBreak" != '00:00')
             AND NOT EXISTS (
                SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
                WHERE attr2."trackId" = attr."trackId"
                AND attr2."type" = 17
             )
        THEN 'TÍNH VÀO ✅'
        ELSE 'KHÔNG TÍNH ❌'
    END as final_count_status,
    'Face recognition' as source_type
FROM "CoreAiHumanTrackAttribute" attr
INNER JOIN "FaceResult" face ON attr."trackId" = face."humanTrackId"
INNER JOIN "ShiftUser" shift_user ON face."userId" = shift_user."userId"
INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
CROSS JOIN test_params tp
WHERE attr."dateCreated" >= tp.start_date
    AND attr."dateCreated" <= tp.end_date
    AND attr."dateCreated"::date = shift_user."startDate"::date
    AND face."userId" = tp.user_id
    AND attr."type" = 18  -- no_employee_card
    AND NOT EXISTS (
        SELECT 1 FROM "CoreAiHumanTrackResult" r
        WHERE r."humanTrackId" = attr."trackId"
        AND r."userIdReCheckResult" IS NOT NULL
    )
ORDER BY violation_date, violation_time;

-- ===== 3. TỔNG KẾT =====
SELECT '=== TỔNG KẾT VI PHẠM ===' as section_title;

WITH final_counts AS (
    -- Đồng phục (chỉ thứ 2)
    SELECT 
        'Đồng phục (chỉ thứ 2)' as loai_vi_pham,
        COUNT(DISTINCT attr."dateCreated"::date) as so_luong
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    CROSS JOIN test_params tp
    WHERE attr."dateCreated" >= tp.start_date
        AND attr."dateCreated" <= tp.end_date
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND attr."dateCreated"::time >= shift_type."startHour"::time
        AND attr."dateCreated"::time <= shift_type."endHour"::time
        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                AND shift_type."startBreak" != '00:00' 
                AND shift_type."endBreak" != '00:00')
        AND EXTRACT(DOW FROM attr."dateCreated") = 1  -- Monday only
        AND result."userIdReCheckResult" = tp.user_id
        AND attr."type" = 16

    UNION ALL

    -- Thẻ (tất cả ngày)
    SELECT 
        'Thẻ (tất cả ngày)' as loai_vi_pham,
        COUNT(DISTINCT attr."trackId") as so_luong
    FROM "CoreAiHumanTrackAttribute" attr
    INNER JOIN "CoreAiHumanTrackResult" result ON attr."trackId" = result."humanTrackId"
    INNER JOIN "ShiftUser" shift_user ON result."userIdReCheckResult" = shift_user."userId"
    INNER JOIN "ShiftType" shift_type ON shift_user."shiftId" = shift_type."code"
    CROSS JOIN test_params tp
    WHERE attr."dateCreated" >= tp.start_date
        AND attr."dateCreated" <= tp.end_date
        AND attr."dateCreated"::date = shift_user."startDate"::date
        AND attr."dateCreated"::time >= shift_type."startHour"::time
        AND attr."dateCreated"::time <= shift_type."endHour"::time
        AND NOT (attr."dateCreated"::time >= shift_type."startBreak"::time 
                AND attr."dateCreated"::time <= shift_type."endBreak"::time
                AND shift_type."startBreak" != '00:00' 
                AND shift_type."endBreak" != '00:00')
        AND result."userIdReCheckResult" = tp.user_id
        AND attr."type" = 18
        AND NOT EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackAttribute" attr2
            WHERE attr2."trackId" = attr."trackId"
            AND attr2."type" = 17
        )
)
SELECT 
    loai_vi_pham,
    so_luong,
    CASE loai_vi_pham
        WHEN 'Đồng phục (chỉ thứ 2)' THEN 'ngày vi phạm'
        WHEN 'Thẻ (tất cả ngày)' THEN 'lần vi phạm'
    END as don_vi
FROM final_counts

UNION ALL

SELECT 
    'TỔNG CỘNG' as loai_vi_pham,
    SUM(so_luong) as so_luong,
    'vi phạm' as don_vi
FROM final_counts;
