-- <PERSON><PERSON>t để xem vi phạm theo từng track cụ thể
-- Thay đổi userId và date range

-- ===== THAY ĐỔI GIÁ TRỊ =====
\set userId '''your-user-id'''
\set startDate '''2024-12-01'''
\set endDate '''2024-12-31'''

-- 1. CHI TIẾT TỪNG TRACK
SELECT 
    attr."trackId",
    attr."dateCreated",
    CASE EXTRACT(DOW FROM attr."dateCreated")
        WHEN 0 THEN 'Chủ nhật'
        WHEN 1 THEN 'Thứ 2 ⭐'
        WHEN 2 THEN 'Thứ 3'
        WHEN 3 THEN 'Thứ 4'
        WHEN 4 THEN 'Thứ 5'
        WHEN 5 THEN 'Thứ 6'
        WHEN 6 THEN 'Thứ 7'
    END as thu_trong_tuan,
    
    -- Tất cả attributes trong track này
    STRING_AGG(
        CASE attr."type"
            WHEN 16 THEN 'Đồng phục'
            WHEN 17 THEN 'Có thẻ'
            WHEN 18 THEN 'Không có thẻ'
            ELSE 'Type ' || attr."type"::text
        END, 
        ', ' 
        ORDER BY attr."type"
    ) as cac_vi_pham_trong_track,
    
    -- Logic đếm cho đồng phục
    CASE 
        WHEN BOOL_OR(attr."type" = 16) AND EXTRACT(DOW FROM attr."dateCreated") = 1
        THEN '✅ TÍNH (Đồng phục thứ 2)'
        WHEN BOOL_OR(attr."type" = 16) AND EXTRACT(DOW FROM attr."dateCreated") != 1
        THEN '❌ KHÔNG TÍNH (Đồng phục không phải thứ 2)'
        ELSE '➖ Không có vi phạm đồng phục'
    END as dong_phuc_status,
    
    -- Logic đếm cho thẻ
    CASE 
        WHEN BOOL_OR(attr."type" = 18) AND NOT BOOL_OR(attr."type" = 17)
        THEN '✅ TÍNH (Không có thẻ)'
        WHEN BOOL_OR(attr."type" = 18) AND BOOL_OR(attr."type" = 17)
        THEN '❌ KHÔNG TÍNH (Có cả không có thẻ và có thẻ)'
        WHEN BOOL_OR(attr."type" = 17) AND NOT BOOL_OR(attr."type" = 18)
        THEN '➖ Không vi phạm (Có thẻ)'
        ELSE '➖ Không có thông tin thẻ'
    END as the_status,
    
    -- Nguồn dữ liệu
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM "CoreAiHumanTrackResult" r 
            WHERE r."humanTrackId" = attr."trackId"
        ) THEN 'Manual recheck'
        ELSE 'Face recognition'
    END as nguon_du_lieu

FROM "CoreAiHumanTrackAttribute" attr
WHERE attr."dateCreated" >= :startDate::date
    AND attr."dateCreated" <= :endDate::date + interval '1 day'
    AND attr."trackId" IN (
        -- Chỉ lấy tracks của user này
        SELECT DISTINCT attr2."trackId"
        FROM "CoreAiHumanTrackAttribute" attr2
        INNER JOIN "CoreAiHumanTrackResult" result ON attr2."trackId" = result."humanTrackId"
        WHERE result."userIdReCheckResult" = :userId
        
        UNION
        
        SELECT DISTINCT attr2."trackId"
        FROM "CoreAiHumanTrackAttribute" attr2
        INNER JOIN "FaceResult" face ON attr2."trackId" = face."humanTrackId"
        WHERE face."userId" = :userId
    )
    AND attr."type" IN (16, 17, 18)
GROUP BY attr."trackId", attr."dateCreated", EXTRACT(DOW FROM attr."dateCreated")
ORDER BY attr."dateCreated", attr."trackId";

-- 2. THỐNG KÊ THEO NGÀY
SELECT '=== THỐNG KÊ THEO NGÀY ===' as title;

SELECT 
    violation_date,
    thu_trong_tuan,
    dong_phuc_tracks,
    the_tracks,
    dong_phuc_counted,
    the_counted,
    CASE 
        WHEN thu_trong_tuan LIKE '%Thứ 2%' AND dong_phuc_tracks > 0 
        THEN '1 ngày đồng phục'
        ELSE '0 ngày đồng phục'
    END as dong_phuc_final,
    the_counted::text || ' lần thẻ' as the_final
FROM (
    SELECT 
        attr."dateCreated"::date as violation_date,
        CASE EXTRACT(DOW FROM attr."dateCreated")
            WHEN 0 THEN 'Chủ nhật'
            WHEN 1 THEN 'Thứ 2 ⭐'
            WHEN 2 THEN 'Thứ 3'
            WHEN 3 THEN 'Thứ 4'
            WHEN 4 THEN 'Thứ 5'
            WHEN 5 THEN 'Thứ 6'
            WHEN 6 THEN 'Thứ 7'
        END as thu_trong_tuan,
        
        -- Đếm tracks có vi phạm đồng phục
        COUNT(DISTINCT CASE WHEN BOOL_OR(attr."type" = 16) THEN attr."trackId" END) as dong_phuc_tracks,
        
        -- Đếm tracks vi phạm thẻ (chỉ có 18, không có 17)
        COUNT(DISTINCT 
            CASE 
                WHEN BOOL_OR(attr."type" = 18) AND NOT BOOL_OR(attr."type" = 17) 
                THEN attr."trackId" 
            END
        ) as the_tracks,
        
        -- Đếm cuối cùng cho đồng phục (chỉ thứ 2)
        CASE 
            WHEN EXTRACT(DOW FROM attr."dateCreated") = 1 
                 AND COUNT(DISTINCT CASE WHEN BOOL_OR(attr."type" = 16) THEN attr."trackId" END) > 0
            THEN 1
            ELSE 0
        END as dong_phuc_counted,
        
        -- Đếm cuối cùng cho thẻ (tất cả ngày)
        COUNT(DISTINCT 
            CASE 
                WHEN BOOL_OR(attr."type" = 18) AND NOT BOOL_OR(attr."type" = 17) 
                THEN attr."trackId" 
            END
        ) as the_counted
        
    FROM "CoreAiHumanTrackAttribute" attr
    WHERE attr."dateCreated" >= :startDate::date
        AND attr."dateCreated" <= :endDate::date + interval '1 day'
        AND attr."trackId" IN (
            SELECT DISTINCT attr2."trackId"
            FROM "CoreAiHumanTrackAttribute" attr2
            INNER JOIN "CoreAiHumanTrackResult" result ON attr2."trackId" = result."humanTrackId"
            WHERE result."userIdReCheckResult" = :userId
            
            UNION
            
            SELECT DISTINCT attr2."trackId"
            FROM "CoreAiHumanTrackAttribute" attr2
            INNER JOIN "FaceResult" face ON attr2."trackId" = face."humanTrackId"
            WHERE face."userId" = :userId
        )
        AND attr."type" IN (16, 17, 18)
    GROUP BY attr."dateCreated"::date, EXTRACT(DOW FROM attr."dateCreated"), attr."trackId"
) daily_stats
GROUP BY violation_date, thu_trong_tuan
ORDER BY violation_date;

-- 3. TỔNG KẾT CUỐI CÙNG
SELECT '=== TỔNG KẾT CUỐI CÙNG ===' as title;

WITH final_summary AS (
    SELECT 
        -- Đồng phục: đếm số ngày thứ 2 có vi phạm
        COUNT(DISTINCT 
            CASE 
                WHEN EXTRACT(DOW FROM attr."dateCreated") = 1 
                     AND BOOL_OR(attr."type" = 16)
                THEN attr."dateCreated"::date 
            END
        ) as uniform_days,
        
        -- Thẻ: đếm số tracks vi phạm (có 18, không có 17)
        COUNT(DISTINCT 
            CASE 
                WHEN BOOL_OR(attr."type" = 18) AND NOT BOOL_OR(attr."type" = 17) 
                THEN attr."trackId" 
            END
        ) as card_violations
        
    FROM "CoreAiHumanTrackAttribute" attr
    WHERE attr."dateCreated" >= :startDate::date
        AND attr."dateCreated" <= :endDate::date + interval '1 day'
        AND attr."trackId" IN (
            SELECT DISTINCT attr2."trackId"
            FROM "CoreAiHumanTrackAttribute" attr2
            INNER JOIN "CoreAiHumanTrackResult" result ON attr2."trackId" = result."humanTrackId"
            WHERE result."userIdReCheckResult" = :userId
            
            UNION
            
            SELECT DISTINCT attr2."trackId"
            FROM "CoreAiHumanTrackAttribute" attr2
            INNER JOIN "FaceResult" face ON attr2."trackId" = face."humanTrackId"
            WHERE face."userId" = :userId
        )
        AND attr."type" IN (16, 17, 18)
    GROUP BY attr."trackId"
)
SELECT 
    'Đồng phục (chỉ thứ 2)' as loai_vi_pham,
    uniform_days as so_luong,
    'ngày' as don_vi
FROM final_summary

UNION ALL

SELECT 
    'Thẻ (tất cả ngày)' as loai_vi_pham,
    card_violations as so_luong,
    'lần' as don_vi
FROM final_summary

UNION ALL

SELECT 
    'TỔNG CỘNG' as loai_vi_pham,
    uniform_days + card_violations as so_luong,
    'vi phạm' as don_vi
FROM final_summary;
